import React from "react";
import styled from "styled-components";
import { Modal, Text, Button, FormGroup } from "unmatched/components";

const ModalHeader = styled(Modal.Header)`
  background: #fbfbfb;
`;

const ModalFooter = styled(Modal.Footer)`
  border: none;
  padding: 2rem;
`;

const UserItem = ({ title, value }: any) => {
  return (
    <FormGroup className="pb-4">
      <FormGroup.Label>{title}</FormGroup.Label>
      <Text.H3>{value}</Text.H3>
    </FormGroup>
  );
};

const ViewUserDetails = (props: any) => {
  const { show, onHide, user, onModify } = props;
  return (
    <Modal
      show={show}
      onHide={onHide}
      size="lg"
      aria-labelledby="contained-modal-title-vcenter"
      centered
    >
      <ModalHeader closeButton>
        <Modal.Title id="contained-modal-title-vcenter">
          <Text.P2 className="pt-1">User Details</Text.P2>
        </Modal.Title>
      </ModalHeader>
      <Modal.Body>
        <UserItem title="First Name" value={user.first_name} />
        <UserItem title="Last Name" value={user.last_name} />
        <UserItem title="Email" value={user.email} />
        <UserItem title="Location" value={user.location} />
        <UserItem title="Department" value={user.department} />
        <UserItem title="Employee ID" value={user.emp_id} />
      </Modal.Body>
      <ModalFooter>
        <Button
          variant="outline-primary"
          className="mx-1"
          onClick={() => (onModify ? onModify() : "")}
        >
          Modify
        </Button>
      </ModalFooter>
    </Modal>
  );
};

export default ViewUserDetails;
