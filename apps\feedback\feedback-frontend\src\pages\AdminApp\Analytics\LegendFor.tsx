import React from "react";
import { Div, Icon, Layout, Text } from "unmatched/components";
// import PropTypes from 'prop-types'

const LegendFor = (props: any) => {
  return (
    <Div className="py-3">
      <Layout.Flex className="justify-content-center">
        {props.legends?.map((item: any) => {
          return (
            <Layout.FlexItem className="pr-3" key={item.key}>
              <Text.P2>
                <Icon
                  icon="fas fa-circle"
                  className="mr-2"
                  color={item.color}
                />{" "}
                {item.label}
              </Text.P2>
            </Layout.FlexItem>
          );
        })}

        {/* <Layout.Col xl={3}>
          <Text.P2>
            <Icon icon="fas fa-circle" className="mr-2" variant="danger" />{" "}
            {props.danger}
          </Text.P2>
        </Layout.Col>
        {props.success && (
          <Layout.Col xl={3}>
            <Text.P2>
              <Icon icon="fas fa-circle" className="mr-2" variant="success" />{" "}
              {props.success}
            </Text.P2>
          </Layout.Col>
        )} */}
      </Layout.Flex>
    </Div>
  );
};

// LegendFor.propTypes = {

// }

export default LegendFor;
