import React, { useEffect } from "react";
import _ from "lodash";
import * as yup from "yup";
import { isFieldInvalid, isFieldValid } from "unmatched/utils/formik";
import { useState, useFormik, useQuery } from "unmatched/hooks";
import useToastr from "unmatched/modules/toastr/hook";
import useSession from "unmatched/modules/session/hook";
import Loader from "../Loader";
import AuthContainer from "../AuthContainer";
import {
  Button,
  Card,
  Div,
  Form,
  FormControl,
  FormGroup,
  Layout,
  Text,
  Span,
} from "unmatched/components";
import { getUserFact, updateUserFact } from "unmatched/modules/session/api";

const INPUT_FIELDS = [
  {
    key: "firstName",
    placeholder: "First Name",
    label: "First Name",
  },
  {
    key: "lastName",
    placeholder: "Last Name",
    label: "Last Name",
  },
  {
    key: "title",
    placeholder: "Title",
    label: "Title",
  },
  {
    key: "department",
    placeholder: "Department",
    label: "Department",
  },
  {
    key: "location",
    placeholder: "Location",
    label: "Location",
  },
];

const META_DATA = {
  title: "Account Information",
  content: "You’re nearly done! Fill in your user details",
  loadingTitle: "Setting up your account...",
  loadingContent: "Please wait while we setup your account.",
};

const InformationForm = (props: any) => {
  const { onUpdateUser, meta, userData } = props;
  const initialValues = _.pick(userData, [
    "firstName",
    "lastName",
    "title",
    "department",
    "location",
  ]);
  const formik = useFormik({
    initialValues,
    validationSchema: yup.object().shape({
      firstName: yup.string().required(),
      lastName: yup.string().required(),
      title: yup.string().required(),
      department: yup.string().required(),
      location: yup.string().required(),
    }),
    onSubmit: (values) => {
      onUpdateUser(values);
    },
  });

  const getFormControlTemplate = (formik: any, options: any) => {
    const { key, placeholder, label } = options;
    return (
      <FormGroup className="pr-xl-5 mr-xl-5 pb-4">
        <FormGroup.Label isRequired label={label} />
        <FormControl.Text
          name={key}
          value={_.get(formik.values, key)}
          isInvalid={isFieldInvalid(formik, key)}
          isValid={isFieldValid(formik, key)}
          onBlur={formik.handleBlur}
          onChange={formik.handleChange}
          placeholder={placeholder}
        ></FormControl.Text>
      </FormGroup>
    );
  };

  return (
    <Form onReset={formik.handleReset} onSubmit={formik.handleSubmit}>
      <Text.H2 className="text-primary pb-2">{meta.title}</Text.H2>
      <Text.P1 className="pb-4">
        {meta.content}
        <Div className="py-4">
          <Span className="font-weight-bold">{meta.email}</Span>
        </Div>
        <Layout.Row className="py-3">
          {INPUT_FIELDS.map((item) => {
            return (
              <Layout.Col key={item.key} xl={6} lg={6} md={6} sm={6} xs={12}>
                {getFormControlTemplate(formik, item)}
              </Layout.Col>
            );
          })}
        </Layout.Row>
      </Text.P1>
      <Card.Footer>
        <Button size="lg" variant="primary" type="submit">
          Get Started
        </Button>
      </Card.Footer>
    </Form>
  );
};

export default function Activation() {
  const meta = META_DATA;
  const { showToast } = useToastr();

  const session = useSession();

  const query = useQuery();

  const [state, setState] = useState({
    isLoading: false,
    email: "",
    token: query.get("token") || "",
    expiry: query.get("expiry") || "",
    user: {
      id: 0,
      firstName: "",
      lastName: "",
      title: "",
      department: "",
      location: "",
    },
    error: {
      msg: "",
      statusCode: "",
    },
  });

  const getUserData = async () => {
    // setState(_state => ({
    //   ..._state,
    //   isLoading: true,
    // }));
    try {
      const user = await getUserFact({ token: state.token || "" });
      setState((_state: any) => ({
        ..._state,
        isLoading: false,
        user,
        email: user.email,
      }));
    } catch (err) {
      onFetchFail(err);
    }
  };

  useEffect(() => {
    getUserData();
  }, []);

  const onFetchFail = (config: any) => {
    showToast({
      show: true,
      title: `Error ${config.statusCode}`,
      variant: "danger",
      content: config.msg,
    });
    setState((state: any) => ({
      ...state,
      isLoading: false,
      error: config,
    }));
  };

  const onUpdateUser = async (data: any) => {
    try {
      const user = await updateUserFact(data, { token: state.token || "" });
      setState((_state: any) => ({
        ..._state,
        isLoading: false,
      }));
      showToast({
        show: true,
        title: "Successs",
        variant: "success",
        content: "Successfully Updated user information",
      });
      session.login({
        token: state.token,
        expiry: state.expiry,
        user,
      });
    } catch (err) {
      onFetchFail(err);
    }
  };

  let Content = null;

  if (state.isLoading) {
    Content = (
      <Loader title={meta.loadingTitle} content={meta.loadingContent}></Loader>
    );
  } else {
    Content = state.user.id ? (
      <InformationForm
        meta={{ ...meta, email: state.email }}
        userData={state.user}
        onUpdateUser={onUpdateUser}
      />
    ) : null;
  }
  return (
    <AuthContainer className="col-xl-8 offset-xl-2">{Content}</AuthContainer>
  );
}
