// import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import "./index.css";
import "./ui.css";
import App from "./App.tsx";
import { Provider } from "react-redux";
import { store } from "./store.ts";
import { HashRouter } from "react-router";
import { ThemeProvider } from "@repo/ui/components/theme-provider";
// import util from "./unmatched/utils";

createRoot(document.getElementById("root")!).render(
  <ThemeProvider>
    <HashRouter>
      <Provider store={store}>
        <App />
      </Provider>
    </HashRouter>
  </ThemeProvider>
  // <StrictMode></StrictMode>
);

// if (util.env.isProd) {
//   util.api.init(`https://${window.location.hostname}/api/v2`);
// // } else if (util.env.apiUrl) {
// //   util.api.init(util.env.apiUrl);
// } else {
//   util.api.init("https://alpha.unmatched.app/api/v2");
// }
