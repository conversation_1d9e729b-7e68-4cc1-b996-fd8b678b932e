import appUrls from "unmatched/utils/urls/app-urls";
import Dashboard from "./Dashboard/Dashboard";
import Reports from "./Reports/Reports";
import FAQs from "./FAQ/FAQ";
import ContactUsContainer from "./ContactUs/ContactUs";
import Privacy from "pages/Privacy/Privacy";
import Terms from "pages/Terms/Terms";
import Confidentiality from "pages/Confidentiality/Confidentiality";

const routes = [
  {
    name: "Dashboard",
    path: appUrls.user.dashboard.default,
    isExact: false,
    isPrivate: true,
    component: Dashboard,
  },
  {
    name: "Reports",
    path: appUrls.user.reports,
    isExact: false,
    isPrivate: true,
    component: Reports,
  },
  {
    name: "FAQs",
    path: appUrls.user.faq,
    isExact: false,
    isPrivate: true,
    component: FAQs,
  },
  {
    name: "Privacy Policy",
    path: appUrls.user.privacy,
    isExact: false,
    isPrivate: true,
    component: Privacy,
  },
  {
    name: "Terms",
    path: appUrls.user.terms,
    isExact: false,
    isPrivate: true,
    component: Terms,
  },
  {
    name: "Confidentiality",
    path: appUrls.user.confidentiality,
    isExact: false,
    isPrivate: true,
    component: Confidentiality,
  },
  {
    name: "Contact Us",
    path: appUrls.user.contactUs,
    isExact: false,
    isPrivate: true,
    component: ContactUsContainer,
  },
];

export default routes;
