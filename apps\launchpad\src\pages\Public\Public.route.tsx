import PublicLayout from "./public.layout";
import Terms from "./Terms/Terms";
import Privacy from "./Privacy/Privacy";
import Confidentiality from "./Confidentiality/Confidentiality";
import { ROUTES } from "../../routes";

const PublicRoute = [
  {
    path: ROUTES.LEGAL.ROOT,
    layout: () => <PublicLayout />,
    children: [
      {
        // Use the last part of the path (after the last slash)
        path: ROUTES.LEGAL.TERMS,
        component: () => <Terms />,
      },
      {
        path: ROUTES.LEGAL.PRIVACY,
        component: () => <Privacy />,
      },
      {
        path: ROUTES.LEGAL.CONFIDENTIALITY,
        component: () => <Confidentiality />,
      },
    ],
  },
];

export default PublicRoute;
