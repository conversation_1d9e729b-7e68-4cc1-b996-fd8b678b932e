import React from "react";
import { Span, Text, Button, Icon, Layout, Table } from "unmatched/components";
// import PropTypes from 'prop-types'

const DownloadExcel = (props: any) => {
  const {
    years = {},
    surveys = {},
    onYearSelect,
    onSurveySelect,
    hideFilters,
    hideYearFilter,
    metaSlot,
  } = props;

  const getFilterLayout = () => {
    if (hideFilters) return "";
    return (
      <Layout.Col className={hideYearFilter ? "col-xl-3 col-lg-3" : ""}>
        <Layout.Row>
          {!hideYearFilter && (
            <Layout.Col>
              <Table.Filter
                title="Year"
                selected={years?.selected || ""}
                options={years?.options || []}
                onSelect={onYearSelect}
              ></Table.Filter>
            </Layout.Col>
          )}
          <Layout.Col>
            <Table.Filter
              title="Survey"
              selected={surveys?.selected || ""}
              options={surveys?.options || []}
              onSelect={onSurveySelect}
            ></Table.Filter>
          </Layout.Col>
        </Layout.Row>
      </Layout.Col>
    );
  };

  return (
    <Layout.Row className="pt-4 pb-3">
      <Layout.Col xl={4} lg={3} className="align-self-center">
        <Text.H3>
          <Span>{props.title}</Span>
        </Text.H3>
      </Layout.Col>
      {metaSlot}
      {getFilterLayout()}
      <Layout.Col className="text-right">
        <Button
          onClick={() => (props.onDownload ? props.onDownload() : "")}
          className="ml-auto"
          variant="outline-primary"
        >
          Download XLSX <Icon icon="fas fa-file-download" />
        </Button>
      </Layout.Col>
    </Layout.Row>
  );
};

// DownloadExcel.propTypes = {

// }

export default DownloadExcel;
