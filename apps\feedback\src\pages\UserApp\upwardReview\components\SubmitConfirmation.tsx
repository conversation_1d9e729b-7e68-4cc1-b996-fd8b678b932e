import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@repo/ui/components/card';
import { But<PERSON> } from '@repo/ui/components/button';
import { Progress } from '@repo/ui/components/progress';
import { Badge } from '@repo/ui/components/badge';
import { Separator } from '@repo/ui/components/separator';
import { CheckCircle, Clock, AlertCircle } from 'lucide-react';

interface SurveyMeta {
  title: string;
  surveyFor: string;
  endDate: string;
  lastModified: string;
  canSubmit: boolean;
  nextTitle: string;
  buttonType: string;
  hideBack: boolean;
  indexId: string;
}

interface Analytics {
  total: number;
  completed: number;
  sections: number[];
}

interface SubmitConfirmationProps {
  meta: SurveyMeta | null;
  analytics: Analytics;
  onBack: () => void;
  onSubmit: () => void;
  isLoading: boolean;
}

const SubmitConfirmation: React.FC<SubmitConfirmationProps> = ({
  meta,
  analytics,
  onBack,
  onSubmit,
  isLoading
}) => {
  const getCompletionPercentage = () => {
    if (analytics.total === 0) return 0;
    return Math.round((analytics.completed / analytics.total) * 100);
  };

  const completionPercentage = getCompletionPercentage();
  const isFullyCompleted = completionPercentage === 100;

  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-6">
      <Card className="w-full max-w-2xl">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4">
            {isFullyCompleted ? (
              <CheckCircle className="h-16 w-16 text-green-500" />
            ) : (
              <AlertCircle className="h-16 w-16 text-yellow-500" />
            )}
          </div>
          <CardTitle className="text-2xl">
            {isFullyCompleted ? 'Ready to Submit' : 'Confirm Submission'}
          </CardTitle>
          <p className="text-muted-foreground mt-2">
            {isFullyCompleted 
              ? 'You have completed all questions in this survey.'
              : 'You can submit this survey even though some questions are incomplete.'
            }
          </p>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Survey Details */}
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h3 className="font-semibold text-sm text-muted-foreground">Survey</h3>
                <p className="font-medium">{meta?.title}</p>
              </div>
              <div>
                <h3 className="font-semibold text-sm text-muted-foreground">Feedback For</h3>
                <p className="font-medium">{meta?.surveyFor}</p>
              </div>
            </div>

            <Separator />

            {/* Progress Summary */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="font-semibold">Completion Status</h3>
                <Badge 
                  variant={isFullyCompleted ? 'default' : 'secondary'}
                  className="text-sm"
                >
                  {completionPercentage}% Complete
                </Badge>
              </div>

              <Progress value={completionPercentage} className="h-3" />

              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Total Questions:</span>
                  <span className="font-medium">{analytics.total}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Completed:</span>
                  <span className="font-medium text-green-600">{analytics.completed}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Remaining:</span>
                  <span className="font-medium text-orange-600">{analytics.total - analytics.completed}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Sections:</span>
                  <span className="font-medium">{analytics.sections.length} completed</span>
                </div>
              </div>
            </div>

            <Separator />

            {/* Last Modified */}
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <Clock className="h-4 w-4" />
              <span>Last saved: {meta?.lastModified}</span>
            </div>

            {/* Warning for incomplete surveys */}
            {!isFullyCompleted && (
              <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <AlertCircle className="h-5 w-5 text-yellow-600 dark:text-yellow-400 mt-0.5" />
                  <div className="space-y-1">
                    <h4 className="font-medium text-yellow-800 dark:text-yellow-200">
                      Incomplete Survey
                    </h4>
                    <p className="text-sm text-yellow-700 dark:text-yellow-300">
                      You have {analytics.total - analytics.completed} unanswered questions. 
                      You can still submit this survey, but consider completing all questions for better feedback quality.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Success message for complete surveys */}
            {isFullyCompleted && (
              <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400 mt-0.5" />
                  <div className="space-y-1">
                    <h4 className="font-medium text-green-800 dark:text-green-200">
                      Survey Complete
                    </h4>
                    <p className="text-sm text-green-700 dark:text-green-300">
                      Excellent! You have answered all questions in this survey. 
                      Your comprehensive feedback will be valuable.
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 pt-4">
            <Button
              variant="outline"
              onClick={onBack}
              disabled={isLoading}
              className="flex-1"
            >
              Back to Survey
            </Button>
            <Button
              onClick={onSubmit}
              disabled={isLoading}
              className="flex-1"
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  Submitting...
                </>
              ) : (
                'Submit Survey'
              )}
            </Button>
          </div>

          {/* Additional Info */}
          <div className="text-xs text-muted-foreground text-center pt-4 border-t">
            <p>
              Once submitted, you will not be able to modify your responses. 
              Make sure you have reviewed all your answers before submitting.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SubmitConfirmation;
