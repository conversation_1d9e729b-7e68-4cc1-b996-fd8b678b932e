import React from "react";
import { Modal, Table, Div, Text } from "unmatched/components";
import ModalHeader from "../../../../ModalHeader";
// import PropTypes from 'prop-types'

const Status = (props: any) => {
  const { show, onHide } = props;

  const [users] = React.useState([
    {
      id: 1,
      name: "<PERSON>",
      email: "<EMAIL>",
      success: true,
    },
    {
      id: 2,
      name: "<PERSON>",
      email: "<EMAIL>",
      success: true,
    },
    {
      id: 3,
      name: "<PERSON>",
      email: "<EMAIL>",
      success: true,
    },
    {
      id: 4,
      name: "<PERSON>",
      email: "<EMAIL>",
      success: true,
    },
    {
      id: 5,
      name: "<PERSON>",
      email: "<EMAIL>",
      success: true,
    },
    {
      id: 6,
      name: "<PERSON>",
      email: "<EMAIL>",
      success: true,
    },
    {
      id: 7,
      name: "<PERSON>",
      email: "<EMAIL>",
      success: true,
    },
  ]);

  const getSuccessTemplate = (success: boolean) => {
    return success ? (
      <Div className={"text-success"}>Success</Div>
    ) : (
      <Div className={"text-success"}>Failed</Div>
    );
  };

  const getRowsTemplate = () => {
    return users.map((item: any, index: number) => {
      const isEven = index % 2 === 0 || index === 0;
      return (
        <Table.Row even={isEven} key={item.id}>
          <Table.Data>{index + 1}</Table.Data>
          <Table.Data>{item.name}</Table.Data>
          <Table.Data>{item.email}</Table.Data>
          <Table.Data>{getSuccessTemplate(item.success)}</Table.Data>
        </Table.Row>
      );
    });
  };

  return (
    <Modal show={show} backdrop="static" centered size="lg">
      <ModalHeader title="Email Statistics" onHide={onHide} />
      <Modal.Body>
        <Text.H3 className="py-3">
          Mail sent to {users.length} participants
        </Text.H3>
        <Table
          rows={users}
          type="striped"
          customRows
          render={() => getRowsTemplate()}
        />
      </Modal.Body>
    </Modal>
  );
};

// Status.propTypes = {

// }

export default Status;
