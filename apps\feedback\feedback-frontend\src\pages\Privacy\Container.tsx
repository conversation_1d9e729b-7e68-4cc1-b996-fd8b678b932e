import Header from "pages/UserApp/Header/Header";
import React from "react";
import util from "unmatched/utils";
import Privacy from "./Privacy";
// import PropTypes from 'prop-types'

const PrivacyContainer = () => {
  return (
    <div className={util.getUtilClassName({ mt: 5, pt: 4 })}>
      <Header hideOptions />
      <Privacy />
    </div>
  );
};

// Faq.propTypes = {

// }

export default PrivacyContainer;
