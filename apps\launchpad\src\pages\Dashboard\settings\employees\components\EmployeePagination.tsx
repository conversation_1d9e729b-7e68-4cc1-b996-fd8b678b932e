import React from "react";
import { But<PERSON> } from "@repo/ui/components/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@repo/ui/components/select";
import {
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
} from "lucide-react";

interface PaginationProps {
  pagination: {
    pageIndex: number;
    pageSize: number;
    pageCount: number;
    totalItems: number;
  };
  handlePaginationChange: (pageIndex: number, pageSize: number) => void;
}

export const EmployeePagination: React.FC<PaginationProps> = ({
  pagination,
  handlePaginationChange,
}) => {
  return (
    <div className="flex items-center justify-between">
      <div className="flex items-center space-x-2">
        <p className="text-sm text-muted-foreground">
          Showing{" "}
          <span className="font-medium">
            {pagination.pageIndex * pagination.pageSize + 1}
          </span>{" "}
          to{" "}
          <span className="font-medium">
            {Math.min(
              (pagination.pageIndex + 1) * pagination.pageSize,
              pagination.totalItems
            )}
          </span>{" "}
          of{" "}
          <span className="font-medium">{pagination.totalItems}</span> results
        </p>
      </div>
      <div className="flex items-center space-x-2">
        <Select
          value={pagination.pageSize.toString()}
          onValueChange={(value) => {
            handlePaginationChange(0, parseInt(value));
          }}
        >
          <SelectTrigger className="h-8 w-[70px]">
            <SelectValue placeholder={pagination.pageSize} />
          </SelectTrigger>
          <SelectContent>
            {[10, 20, 30, 40, 50].map((pageSize) => (
              <SelectItem key={pageSize} value={pageSize.toString()}>
                {pageSize}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Button
          variant="outline"
          size="icon"
          onClick={() => handlePaginationChange(0, pagination.pageSize)}
          disabled={pagination.pageIndex === 0}
        >
          <ChevronsLeft className="h-4 w-4" />
        </Button>
        <Button
          variant="outline"
          size="icon"
          onClick={() => handlePaginationChange(pagination.pageIndex - 1, pagination.pageSize)}
          disabled={pagination.pageIndex === 0}
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>
        <Button
          variant="outline"
          size="icon"
          onClick={() => handlePaginationChange(pagination.pageIndex + 1, pagination.pageSize)}
          disabled={pagination.pageIndex === pagination.pageCount - 1}
        >
          <ChevronRight className="h-4 w-4" />
        </Button>
        <Button
          variant="outline"
          size="icon"
          onClick={() => handlePaginationChange(pagination.pageCount - 1, pagination.pageSize)}
          disabled={pagination.pageIndex === pagination.pageCount - 1}
        >
          <ChevronsRight className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};
