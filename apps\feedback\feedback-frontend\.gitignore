# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/node_modules_old
/.pnp
.pnp.js
# package-lock.json
.npmrc
yarn.lock

# testing
/coverage

# production
/build

# editor
/.vscode
/.husky

# misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local
.env

.npmrc
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.eslintcache
debug.log