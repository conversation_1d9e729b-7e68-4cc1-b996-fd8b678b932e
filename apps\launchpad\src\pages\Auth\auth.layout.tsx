import { Outlet } from "react-router";
import { Text } from "@repo/ui/components/Text";
import { Link } from "react-router";
import { Key, Lock, Shield } from "lucide-react";
// import background from "../../assets/images/loginbg.png";
import styles from "./Auth.module.css";
import ROUTES from "@/routes";

function AuthLayout() {
  return (
    <div className={styles.loginBG} >
      <Outlet />
      <Highlighters />
      <Footer />
    </div>
  );
}
const Highlighters = () => {
  return (
    <nav className="navbar navbar-light">
      <div className="nav-item ml-auto mr-auto">
        <Text.P1 className="text-center flex flex-row justify-center mt-10">
          {/* <div className="pt-3"> */}
            <span className="px-2 flex items-center">
              {/* <Icon icon="fas fa-shield-alt" variant="primary" />{" "} */}
              <Shield size={16} className="text-primary inline" />
              <span className="pl-2">Highly Secure</span>
            </span>
            <span className="px-2 flex items-center">
              {/* <Icon icon="fas fa-key" variant="primary inline" />{" "} */}
              <Key size={16} className="text-primary inline" />
              <span className="pl-2">Military Grade</span> Encryption
            </span>
            <span className="px-2 flex items-center">
              {/* <Icon icon="fas fa-lock" variant="primary inline" />{" "} */}
              <Lock size={16} className="text-primary inline" />
              <span className="pl-2">GDPR Compliant</span>
            </span>
          {/* </div> */}
        </Text.P1>
      </div>
    </nav>
  );
};

const Footer = () => {
  const year = new Date().getFullYear();
  return (
    <nav className="navbar navbar-light">
      <div className="nav-item ml-auto mr-auto">
        <Text.P2 className="text-center text-xs">
          {/* <div className="pt-4"> */}
          <Link className="text-dark" rel="noreferrer" to={ROUTES.LEGAL.TERMS}>
            Terms
          </Link>{" "}
          •{" "}
          <Link className="text-dark" rel="noreferrer" to={ROUTES.LEGAL.PRIVACY}>
            Privacy Policy
          </Link>{" "}
          •{" "}
          <Link className="text-dark" rel="noreferrer" to={ROUTES.LEGAL.CONFIDENTIALITY}>
            Confidentiality
          </Link>{" "}
          •{" "}
          <Link className="text-dark" to={ROUTES.HELP.CONTACT}>
            Contact Us
          </Link>
        </Text.P2>
        {/* </div> */}
        <p className="m-0 text-center text-xs pt-3">Unmatched © {year}</p>
      </div>
    </nav>
  );
};
export default AuthLayout;
