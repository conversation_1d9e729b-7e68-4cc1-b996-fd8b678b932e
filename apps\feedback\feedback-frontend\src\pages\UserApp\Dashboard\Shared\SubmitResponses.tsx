import React, { useEffect } from "react";
import { useWindowSize } from "react-use";

import styled from "styled-components";
import {
  Div,
  Text,
  Card,
  FormGroup,
  FormControl,
  ScrollToBottom,
} from "unmatched/components";
import useToastr from "unmatched/modules/toastr/hook";
import surveyTakeCore from "unmatched/survey/take/components";
import {
  getWavierQuestionFact,
  getWavierResponseFact,
  patchQuestionResponseFact,
  postQuestionResponseFact,
} from "../dashboard-api";
import useUtil from "./hooks/util-hook";

const { TakeSurveyContainer, TakeSurveyFooter } = surveyTakeCore;

const Notice = styled(Div)`
  background: #ffffff;
  border-radius: 4px;
`;

const StyledCard = styled(Card)`
  background: rgba(238, 214, 255, 0.05);
  border: 1px solid #eed6ff;
  box-sizing: border-box;
  border-radius: 8px;
`;

const ClickableRadioLabel = styled(FormControl.Radio.Label)`
  &:hover {
    color: #2b73ff !important;
  }
`;

const SubmitResponses = (props: any) => {
  const {
    marginLeft,
    onSubmit,
    onBack,
    meta,
    lastModifiedLabel,
    completionLabel,
    isLoading,
    surveyType,
  } = props;
  const dashboardUtil = useUtil();
  const [wavier, setWavier]: any = React.useState({});
  const [selected, setSelected] = React.useState(0);
  const [responseId, setResponseId] = React.useState(0);
  const media = useWindowSize();
  const toastr = useToastr();

  const getWavierQuestion = () => {
    return getWavierQuestionFact(meta.eligibilityId).then((response: any) => {
      setWavier(response);
      setSelected(response.default);
    });
  };

  const getWavierResponse = () => {
    getWavierResponseFact(props.surveyResponseId, meta.wavierId).then(
      (response: any) => {
        setResponseId(response.id || 0);
        if (response.id) {
          setSelected(response.value);
        }
      }
    );
  };

  const updateWavierResponse = (_selected: any) => {
    const payload = {
      survey_response: props.surveyResponseId,
      question: meta.wavierId,
      value: _selected,
      is_valid: true,
    };
    if (!responseId) {
      postQuestionResponseFact(payload).then((response: any) => {
        // debugger;
        setSelected(_selected);
        setResponseId(response.data.id);
      });
    } else {
      const _oldSelection = selected;
      setSelected(_selected);
      patchQuestionResponseFact(payload, responseId)
        .then(() => {
          return;
        })
        .catch(() => {
          toastr.errorToast("Something went wrong");
          setSelected(_oldSelection);
        });
    }
  };

  const getWavierTemplate = () => {
    if (!meta.wavierId) return "";
    return (
      <Div>
        <StyledCard className="my-3 p-4">
          <Text.H3 className="pb-2">{wavier.question}</Text.H3>
          {wavier?.options?.map((item: any) => {
            return (
              <FormGroup className="pt-2" key={item.id}>
                <FormControl.Radio>
                  <FormControl.Radio.Input
                    className="cursor-pointer"
                    checked={selected === item.value}
                    onChange={() => {
                      updateWavierResponse(item.value);
                    }}
                  />
                  <ClickableRadioLabel className="cursor-pointer">
                    <Text.P1
                      onClick={() => {
                        updateWavierResponse(item.value);
                      }}
                    >
                      {item.label}
                    </Text.P1>
                  </ClickableRadioLabel>
                </FormControl.Radio>
              </FormGroup>
            );
          })}
        </StyledCard>
      </Div>
    );
  };

  useEffect(() => {
    if (!meta.wavierId) return;
    getWavierQuestion().then(() => {
      getWavierResponse();
    });
  }, []);

  return (
    <>
      <TakeSurveyContainer
        title={meta.title}
        isLoading={isLoading}
        percentage={completionLabel}
        lastSave={lastModifiedLabel}
        surveyType={surveyType}
        setShowSidebar={props.setShowSidebar}
        color={dashboardUtil.getSurveyColor(surveyType)}
        ScrollToBottom={ScrollToBottom}
      >
        <Div className="">
          {/* <Text.H2>Submit Your Response?</Text.H2> */}
          <Text.P1 className="mt-4">
            {/* You are providing feedback on a secure web site. SRA uses the same
            type of secure encrypted sessions used in online credit card
            transactions. The feedback will not be associated with the feedback
            provider's name, and reports will not identify the individuals who
            contributed to them. */}
            {meta.thankyouText.split("\n").map((item: string, i: number) => {
              if (item.trim() === "") {
                return <br key={i} />;
              }
              return (
                <span key={i}>
                  {item}
                  <br />
                </span>
              );
            })}
          </Text.P1>
          {getWavierTemplate()}
          <Text.P1 className="mt-3">
            {/* In suscipit metus et mi malesuada, sed dignissim tellus aliquam.
            Class aptent taciti sociosqu ad litora torquent per conubia nostra,
            per inceptos himenaeos. Ut lacinia, urna sit amet hendrerit finibus,
            tortor elit fermentum lorem, et laoreet purus metus eu mi. Cras
            dictum nisl quis ipsum porttitor auctor. Aenean pellentesque risus
            urna, quis tristique ligula consequat vel. Integer nec placerat
            urna, sit amet malesuada lorem. Interdum et malesuada fames ac ante
            ipsum primis in faucibus. Quisque eu velit vitae dolor tempor
            vestibulum bibendum id sem. Nunc non tortor */}
          </Text.P1>
        </Div>
        <Div className="mb-3">
          <Notice className="py-3 mb-5">
            <Text.P1>
              Note : You can continue to edit and change your responses until
              the survey end date.
            </Text.P1>
          </Notice>
        </Div>
      </TakeSurveyContainer>
      {!isLoading && (
        <TakeSurveyFooter
          marginLeft={media.width >= 768 ? marginLeft : ""}
          onNext={onSubmit}
          onBack={onBack}
          hideBack={false}
          nextTitle={"Next"}
          section={{}}
        />
      )}
    </>
  );
};

export default SubmitResponses;
