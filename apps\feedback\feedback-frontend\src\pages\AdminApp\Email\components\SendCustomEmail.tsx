import ModalHeader from "pages/AdminApp/ModalHeader";
import {
  getComputedValue,
  getComputedValueColorSub,
  getComputedValueText,
} from "pages/AdminApp/Survey/CreateSurvey/SendSurvey/SendSurvey";
import React, { useEffect, useState } from "react";
import {
  Div,
  Button,
  Form,
  FormGroup,
  MultiSelect,
  Icon,
  Modal,
} from "unmatched/components";
import RichEditor from "unmatched/components/RichEditor";
import useToastr from "unmatched/modules/toastr/hook";
import { CUSTOM_GENERIC_EMAIL_INSERT_OPTIONS } from "../meta";
import { ConfirmEmailSend } from "./ConfirmEmailSend";
import {
  optionStyles,
  CustomOption,
} from "./QuickSend";

const SendCustomEmail = (props: any) => {
  const [template] = useState("CUSTOM");
  const [targetGroupArr] = useState<any>([
    { value: "CUSTOM", label: "CUSTOM" },
  ]);
  const [subject, setSubject] = useState("");
  const [body, setBody] = useState<any>({ text: "", html: "" });
  const [showModal, setShowModal] = React.useState(false);
  const [recipientsCountArr, setRecipientsCountArr] = React.useState<any>({});

  const toastr = useToastr();

  useEffect(() => {
    const defTemplate = props.getTemplate("CUSTOM");
    populateEmail(defTemplate);
  }, []);

  useEffect(() => {
    const getCount = async (recipients: any) => {
      const countArray = await props.getRecipientsCountsArr(recipients);
      setRecipientsCountArr(countArray);
    };

    targetGroupArr?.[0]?.value !== "CUSTOM" && getCount(targetGroupArr);
  }, [targetGroupArr]);

  let richTextRef: any = null;
  let subRichTextRef: any = null;

  const setRef = (ref: any) => {
    richTextRef = ref;
  };

  const setSubRef = (ref: any) => {
    subRichTextRef = ref;
  };

  const emailInputProps = {
    closeMenuOnSelect: false,
    isCustom: true,
    onInputChange: props.onInputChange,
    isMulti: true,
    placeholder: "",
    options: props.userOptions,
    CustomOption,
    styles: optionStyles,
    postfix: () =>
      props.selectedEmails.length > 0 ? (
        <span
          onClick={(e: any) => {
            e.stopPropagation();
            props.setSelectedEmails([]);
          }}
        >
          <Icon
            className="mr-2 fw-300 fs-12 cursor-pointer"
            icon="fas fa-times"
          />
        </span>
      ) : null,
    customValContainer: true,
  };

  const onSelect = (users: any) => {
    props.setSelectedEmails(users);
  };

  const populateEmail = (defTemplate: any) => {
    if (defTemplate) {
      setBody({
        text: defTemplate.text,
        body: defTemplate.body,
      });
      setSubject(defTemplate.subject);
      const quillRef = richTextRef.getEditor();
      const subQuillRef = subRichTextRef.getEditor();
      setTimeout(() => {
        quillRef.clipboard.dangerouslyPasteHTML(
          getComputedValue(defTemplate.body, true)
        );

        subQuillRef.clipboard.dangerouslyPasteHTML(
          getComputedValueColorSub(getComputedValue(defTemplate.subject, true))
        );
      }, 100);
    }
  };

  const updateCustomTemplate = async () => {
    const customTemplate = await props.getTemplate("CUSTOM");
    customTemplate.subject = getComputedValueText(subject, false);
    customTemplate.body = getComputedValue(body.html, false);
    customTemplate.text = getComputedValueText(body.text, false);
    return props.updateEmailData(customTemplate);
  };

  return (
    <>
      <Div>
        <Div>
          <Form>
            {!!targetGroupArr.find((tg: any) => tg.value === "CUSTOM") &&
              targetGroupArr.length === 1 && (
                <FormGroup>
                  <FormGroup.Label className="pb-2">
                    Enter Email Address
                  </FormGroup.Label>
                  <MultiSelect
                    {...emailInputProps}
                    onSelect={onSelect}
                    hasError={false}
                    wrap
                    placeholder="Search users by email or name"
                    value={props.selectedEmails}
                  />
                </FormGroup>
              )}

            <FormGroup>
              <FormGroup.Label>Email Subject</FormGroup.Label>
              <Div className="survey-quill-subject">
                <RichEditor
                  onChange={(html: string, text: string) => {
                    setSubject(text);
                  }}
                  value={subject}
                  insertOptions={CUSTOM_GENERIC_EMAIL_INSERT_OPTIONS}
                  setRef={setSubRef}
                />
              </Div>
            </FormGroup>
            <FormGroup>
              <FormGroup.Label>Email Body</FormGroup.Label>
              <Div className="survey-quill custom-reminder">
                {
                  <RichEditor
                    onChange={(html: string, text: string) => {
                      setBody({ html, text });
                    }}
                    value={body.html}
                    insertOptions={CUSTOM_GENERIC_EMAIL_INSERT_OPTIONS}
                    setRef={setRef}
                  />
                }
              </Div>
            </FormGroup>
          </Form>
        </Div>
      </Div>
      <Div className="mb-2" />
      <Div style={{ textAlign: "right" }}>
        <Button onClick={() => setShowModal(true)} variant="outline-primary">
          Send Email
        </Button>
      </Div>
      <Modal show={showModal} size="lg" centered>
        <ModalHeader title="Send Email" onHide={() => setShowModal(false)} />
        <Modal.Body>
          <ConfirmEmailSend
            template={template}
            targetGroupArr={targetGroupArr}
            setShowModal={setShowModal}
            recipientsCountArr={recipientsCountArr}
            selectedEmails={props.selectedEmails}
            onSend={async () => {
              await updateCustomTemplate();
              if (
                targetGroupArr?.find((tg: any) => tg.value === "CUSTOM") &&
                targetGroupArr?.length === 1
              ) {
                props.sendCustomEmailTemplate(
                  "CUSTOM",
                  props.selectedEmails.map((usr: any) => usr.id),
                  true
                );
              } else {
                const res = await Promise.all(
                  targetGroupArr.map((tg: any) =>
                    props.sendEmail("CUSTOM", tg.value, false)
                  )
                ).catch((err: any) => console.log(err));
                if (res) {
                  // show toast
                  toastr.onSucces({
                    title: "Success",
                    content: "Email sent Successfully",
                  });
                }
                // props.sendEmail("CUSTOM", targetGroup);
              }
            }}
            onMockSend={async () => {
              await updateCustomTemplate();
              props.sendMockCustomEmail(template);
            }}
          />
        </Modal.Body>
      </Modal>
    </>
  );
};

export default SendCustomEmail;
