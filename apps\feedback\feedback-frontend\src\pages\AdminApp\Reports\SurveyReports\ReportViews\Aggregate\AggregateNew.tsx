import React, { useState } from "react";
import {
  <PERSON><PERSON>ontainer,
  Text,
  Layout,
  ComboBasic<PERSON>ilter,
  <PERSON><PERSON>,
  Div,
} from "unmatched/components";
import Header from "../../Header";
import useFilter from "pages/CommonFilters/hook";
import appUrls from "unmatched/utils/urls/app-urls";
import iconsSvgs from "assets/icons/icons";
import useToastr from "unmatched/modules/toastr/hook";
import { forEach } from "lodash";
import {
  requestAggregateReports,
} from "pages/AdminApp/Reports/reports-api";
import { AggregateSuccessModal } from "./AggreagteSuccessModal";

const { Page } = iconsSvgs;

export const AggregateNew = (props: any) => {
  const { survey, layout, surveyId } = props;
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const toastr = useToastr();
  const [selectedMeta, setSelectedMeta] = React.useState<any>({
    cohort1: "",
    applied1: undefined,
    cohort2: "",
    applied2: undefined,
    isSet: false,
  });
  const [metas, setMetas] = React.useState<any>([]);


  const filtersState = useFilter();

  const getFilters = () => {
    filtersState.getFilters((_filters: any) => {
      const arr: any = [];
      forEach(_filters, (values: any, key: any) => {
        arr.push({
          title: values.label,
          key: key,
          value: values.values.map((value: any) => {
            return { key: value, title: value };
          }),
        });
      });
      setMetas(arr);
    });
  };

  React.useEffect(() => {
    getFilters();
  }, []);


  const generateReport = async () => {
    if (selectedMeta.cohort1 === "") {
      return toastr.errorToast("Aggregation factor cannot be empty.");
    }
    try {
      await requestAggregateReports({
        index: surveyId,
        parent: selectedMeta.cohort1,
        child: selectedMeta.cohort2 !== "" ? selectedMeta.cohort2 : undefined,
      });
      setShowSuccessModal(true);
    } catch (error: any) {
      toastr.onError({
        ...error,
        msg: JSON.stringify(error.msg),
      });
    }
  };

  const getCohortSelected = (cohort: string) => {
    if (cohort === undefined || cohort === null) {
      return "";
    }
    return metas.filter((meta: any) => meta.key === cohort)[0]?.title ?? "";
  };
  const getCohortItems = (item: any) => {
    return metas.filter((_item: any) => _item.key !== item);
  };

  const filters = {
    showUnflagged: false,
    cohort1: {
      options: getCohortItems(selectedMeta.cohort2),
      selected: getCohortSelected(selectedMeta.cohort1),
    },
    applied1: {
      options: [], //getCohortValues(selectedMeta.cohort1),
      selected: "All", //selectedMeta.applied1,
    },
    cohort2: {
      options: getCohortItems(selectedMeta.cohort1),
      selected: getCohortSelected(selectedMeta.cohort2),
    },
    applied2: {
      options: [], //getCohortValues(selectedMeta.cohort2),
      selected: "All", //selectedMeta.applied2,
    },
  };

  const getFilterTemplate = () => {
    return (
      <>
        <Text.P1 className="py-2">
        Generate aggregate reports with all combinations of chosen criteria. 
        </Text.P1>
        <Text.P2>
        Example: If criteria selected is Title and Department, output will be the report of all combinations, like Partners&Litigation, Associates&Litigation etc.

        </Text.P2>
        <Layout.Row className="py-3">
          <Layout.Col xl={5} lg={8} sm={12}>
            <Div className="row">
              <Div className="col">
                <ComboBasicFilter
                  cohart={filters.cohort1}
                  applied={filters.applied1}
                  onCohartUpdate={(e: string) =>
                    setSelectedMeta({
                      ...selectedMeta,
                      cohort1: e,
                      applied1: undefined,
                    })
                  }
                  onAppliedUpdate={(e: any) =>
                    setSelectedMeta({ ...selectedMeta, applied1: e.title })
                  }
                  isAppliedShown={false}
                  hideIcon={true}
                />
              </Div>
              <Div className="col">
                <ComboBasicFilter
                  cohart={filters.cohort2}
                  applied={filters.applied2}
                  onCohartUpdate={(e: string) =>
                    setSelectedMeta({
                      ...selectedMeta,
                      cohort2: e,
                      applied2: undefined,
                    })
                  }
                  onAppliedUpdate={(e: any) =>
                    setSelectedMeta({ ...selectedMeta, applied2: e.title })
                  }
                  isAppliedShown={false}
                  hideIcon={true}
                />
              </Div>
            </Div>
          </Layout.Col>
        </Layout.Row>
        <Div>
          <Button onClick={generateReport}>Generate</Button>
        </Div>
      </>
    );
  };

  return (
    <PageContainer>
      <Header
        survey={survey}
        layout={layout}
        breadcrumbs={[
          {
            label: "Reports",
            icon: <Page className="grey-icon__svg" />,
            route: appUrls.admin.reports.default,
          },
          { label: "Aggregate Reports" },
          { label: survey.name },
        ]}
      />
      <Layout.Container fluid>
        <Text.H3 className="pt-5">Generate Aggregate Reports</Text.H3>
        {getFilterTemplate()}
        <AggregateSuccessModal
          surveyID={survey.id}
          goTo={props.onTabSelect}
          show={showSuccessModal}
          onHide={() => setShowSuccessModal(false)}
        />
      </Layout.Container>
    </PageContainer>
  );
};
