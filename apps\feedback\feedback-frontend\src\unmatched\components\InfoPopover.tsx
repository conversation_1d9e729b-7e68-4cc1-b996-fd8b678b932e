import React from "react";
import styled from "styled-components";
import { Div } from "@unmatchedoffl/ui-core";

const Popover = styled(Div)`
  top: 52vh;
  left: 12px;
  background-color: #518cff;
  color: #fff;
  border-radius: 4px;
`;

const InnerPopover = styled(Div)`
  height: 301px;
  width: 238px;
  padding: 17px;
`;

const InfoPopover = (props: any) => {
  return (
    <Popover className="position-fixed">
      <InnerPopover>{props.children}</InnerPopover>
    </Popover>
  );
};

export default InfoPopover;
