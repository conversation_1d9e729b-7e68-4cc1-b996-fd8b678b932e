import React from "react";
import { useMedia } from "react-use";

const useLayout = ({ width, sidebar, setSidebar, setMargin }: any) => {
  const isDesktop = useMedia("(min-width: 992px)");

  React.useEffect(() => {
    if (isDesktop) {
      setSidebar(true);
    }
    setMargin(isDesktop);
  }, [isDesktop, setSidebar, setMargin]);

  const layout = {
    sidebar: {
      show: sidebar,
      width: sidebar ? `${width}px` : "0px",
      marginLeft: sidebar ? `${width}px` : "0px",
    },
    content: {
      styles: {
        marginLeft: sidebar ? `${width}px` : "0px",
      },
    },
    hasOverlay: !isDesktop && sidebar,
  };

  return layout;
};

export default useLayout;
