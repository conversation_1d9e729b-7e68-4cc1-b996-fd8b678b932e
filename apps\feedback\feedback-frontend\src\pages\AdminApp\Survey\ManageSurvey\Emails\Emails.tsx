import { useCallback, useEffect, useState } from "react";
import {
  Div,
  Layout,
  Text,
  Card,
  Button,
  FormGroup,
  FormControl,
  Form,
  Modal,
  PageContainer,
  MultiSelect,
  RichEditor,
  Nav,
} from "unmatched/components";
import CustomHeader from "pages/AdminApp/Shared/CustomHeader/CustomHeader";
import ModalHeader from "pages/AdminApp/ModalHeader";
import { CUSTOM_EMAIL_INSERT_OPTIONS } from "../manage-survey-meta";
import {
  getComputedValue,
  getComputedValueColorSub,
  getComputedValueText,
} from "../../CreateSurvey/SendSurvey/SendSurvey";
import { debounce } from "lodash";
import Confirmation from "unmatched/components/Confirmation";
import QuickSend from "./components/QuickSend";
import "./components/styles.css";
import SendCustomEmail from "./components/SendCustomEmail";
import { searchUsersFact } from "pages/AdminApp/DataLoad/dataload-api";
import LoadingImg from "assets/images/Loading_2.gif";
import { components } from "react-select";
import appUrls from "unmatched/utils/urls/app-urls";
import icons from "assets/icons/icons";
import ParticipantsList from "./components/logs/participants/ParticipantsList";
import Logs from "./components/logs/Logs";
import NudgeSettingsContainer from "./components/nudgeSettings/NudgeSettingsContainer";
import { UMTab } from "unmatched/components/Tabs";

const { Paste } = icons;

export const userFriendlyTemplatesMap = {
  INACTIVE_ALL: "Activation Reminder",
  ACTIVE_INCOMPLETE: "General Reminder",
  SURVEY_INVITATION: "Survey Invitation",
  SPECIAL: "Special Reminder",

  REMINDER_INACTIVE: "Activation Reminder",
  REMINDER_ACTIVE: "General Reminder",
  CUSTOM: "Custom",
};

export const templatesDescMap = {
  INACTIVE_ALL: "Reminder for all reviewers who are inactive",
  ACTIVE_INCOMPLETE: "Reminder for reviewers who are active",
  SURVEY_INVITATION: "Invitation to participate",
  SPECIAL: "Special Reminder for reviewers",

  REMINDER_INACTIVE: "Reminder for all reviewers who are inactive",
  REMINDER_ACTIVE: "Reminder for reviewers who are active",
  CUSTOM: "Custom reminder",
};

export const userFriendlyRecipientsMap = {
  INACTIVE_ALL: "Inactive Users",
  ACTIVE_INCOMPLETE: "Idle Users",
  ACTIVE_COMPLETE: "Completed Users ",
  ACTIVE_PARTIAL: "Active Users",
  CUSTOM: "Custom",
};

export const recipientsDescMap = {
  INACTIVE_ALL:
    "Inactive Users - Users who haven’t even activated their accounts.",
  ACTIVE_INCOMPLETE:
    "Idle Users - Users who have activated their accounts but haven’t submitted any surveys.",
  ACTIVE_COMPLETE:
    "Completed Users - Users who have completed the surveys assigned to them.",
  ACTIVE_PARTIAL:
    "Active Users - Users who have activated and are submitting surveys.",
  CUSTOM: "Custom selected users.",
};

const Emails = (props: any) => {
  const [targetGroup, setTargetGroup] = useState("INACTIVE_ALL");
  const [showConfirm, setShowConfirm] = useState(false);
  const [showSendCustomModal, setShowSendCustomModal] = useState(false);
  const [showParticipantsModal, setShowParticipantsModal] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [userOptions, setUserOptions] = useState<any>([]);
  const [selectedEmails, setSelectedEmails] = useState<any>([]);
  const [filter, setFilter] = useState("emails");

  const templates = [
    "INACTIVE_ALL", // REMINDER_INACTIVE
    "ACTIVE_INCOMPLETE", // REMINDER_ACTIVE
    // "ACTIVE_PARTIAL", // REMINDER_ACTIVE
    // "ACTIVE_COMPLETE", // REMINDER_ACTIVE
    "SURVEY_INVITATION", // SURVEY_INVITATION
    "SPECIAL", // SPECIAL
  ];

  const targetGroupsMap = {
    INACTIVE_ALL: "REMINDER_INACTIVE",
    ACTIVE_INCOMPLETE: "REMINDER_ACTIVE",
    ACTIVE_PARTIAL: "REMINDER_ACTIVE",
    ACTIVE_COMPLETE: "REMINDER_ACTIVE",
    SURVEY_INVITATION: "SURVEY_INVITATION",
    SPECIAL: "SPECIAL",
  };
  // meta
  const { survey, layout } = props;
  let richTextRef: any = null;
  let subRichTextRef: any = null;

  const setRef = (ref: any) => {
    richTextRef = ref;
  };

  const setSubRef = (ref: any) => {
    subRichTextRef = ref;
  };

  const sendEmailReqBody = {
    survey_index: props.indexId,
    is_sandbox: false,
  };

  const sendEmail = (template: string, tGroup: string, showToast?: boolean) => {
    const reqData = { ...sendEmailReqBody };
    (reqData as any).target_group = tGroup;
    (reqData as any).template_name = template;
    return props.sendEmailReminder(reqData, showToast);
  };

  const sendCustomEmail = (
    template: string,
    tGroup: string,
    raterUsers: any
  ) => {
    const reqData = { ...sendEmailReqBody };
    (reqData as any).template_name = template;

    if (tGroup !== "CUSTOM") {
      (reqData as any).target_group = tGroup;
    } else {
      (reqData as any).rater_users = raterUsers;
    }
    props.sendCustomEmailReminder(reqData, true);
  };

  const sendMockCustomEmail = (template: string, raterUsers: any) => {
    const reqData = { ...sendEmailReqBody };
    (reqData as any).template_name = template;
    (reqData as any).rater_users = raterUsers;

    props.sendCustomEmailReminder(reqData, true);
  };

  const sendCustomEmailTemplate = (
    tGroup: string,
    raterUsers: any,
    showToast?: boolean
  ) => {
    const reqData = { ...sendEmailReqBody };
    (reqData as any).template_name = "CUSTOM";
    (reqData as any).rater_users = tGroup === "CUSTOM" ? raterUsers : [];
    if (tGroup !== "CUSTOM") {
      (reqData as any).target_group = tGroup;
    }
    return props.sendCustomEmailReminder(reqData, showToast);
  };

  const sendMockEmail = () => {
    const reqData = { ...sendEmailReqBody };
    reqData.is_sandbox = true;
    props.sendEmailReminder(reqData);
  };

  const getRecipientsCount = async (tGroup: any) => {
    const reqData = { ...sendEmailReqBody };
    reqData.is_sandbox = true;
    if (tGroup) {
      (reqData as any).target_group = tGroup;
    }
    const result = await props.sendEmailReminder(reqData);
    return { recipient: tGroup, count: result?.data?.count };
  };

  const getRecipientsCountsArr = async (recipients: any) => {
    return await Promise.all(
      recipients.map((r: any) => {
        return getRecipientsCount(r.value);
      })
    );
  };

  const getComputedUsers = (users: any) => {
    const usersData = users.map((u: any) => ({
      ...u,
      label: u.email,
      value: u.emp_id,
    }));
    return usersData;
  };

  const getUserOptions = debounce((search: string) => {
    if (!search) return;
    searchUsersFact("", { search })
      .then((res) => {
        const data = getComputedUsers(res.data?.results || []);
        setUserOptions(data);
      })
      .catch((err) => console.log(err));
  }, 200);

  // debounce on subject change start
  const onSubjectChange = () => {
    // props.updateEmailData(
    //   {
    //     ...props.emailData,
    //   },
    //   false,
    //   setIsSaving
    // );
  };

  const delayedSubChange = useCallback(debounce(onSubjectChange, 1000), [
    props.emailData?.subject,
  ]);

  useEffect(() => {
    delayedSubChange();
    return delayedSubChange.cancel;
  }, [props.emailData?.subject, delayedSubChange]);

  const getTemplate = (name: any) => {
    const template = props.templates.find((r: any) => r.label === name);
    return template;
  };


  const TemplateOption = (props: any) => {
    return (
      <div className="px-2 py-1">
        <components.Option {...props} className="rounded pt-1 pb-2">
          <FormGroup className="m-0 d-flex align-items-center">
            <Text.P1
              style={{ color: "#000", fontSize: 14 }}
              className="pt-1 cursor-pointer"
            >
              {props.label}
            </Text.P1>
          </FormGroup>
        </components.Option>
      </div>
    );
  };

  const getUpdateEmailTemplate = () => {
    return (
      <Card className="mb-4" noShadow>
        <Card.Header className="pl-3 py-2 d-flex justify-content-between">
          <Text.H3>Email Templates</Text.H3>
          {/* <span className="mr-4 fs-14">{isSaving ? "Saving..." : "Saved"}</span> */}
        </Card.Header>
        <Div className="pl-3 py-2">
          <Div className="pr-4 pt-2">
            <Form>
              <Div className="d-flex justify-content-between align-items-center">
                <Div style={{ width: 230 }}>
                  <FormGroup>
                    <FormGroup.Label className="pb-2">
                      Choose Template:
                    </FormGroup.Label>
                    <MultiSelect
                      CustomOption={TemplateOption}
                      options={templates.map((tg: any) => ({
                        value: tg,
                        label: (userFriendlyTemplatesMap as any)[tg],
                      }))}
                      isMulti={false}
                      closeMenuOnSelect
                      value={{
                        value: targetGroup,
                        label: (userFriendlyTemplatesMap as any)[targetGroup],
                      }}
                      onSelect={(selected: any) => {
                        setTargetGroup(selected.value);
                        const template = props.templates.find(
                          (r: any) =>
                            r.label === (targetGroupsMap as any)[selected.value]
                        );
                        if (template) {
                          props.setEmailData(template);
                          const quillRef = richTextRef.getEditor();
                          const subQuillRef = subRichTextRef.getEditor();
                          // https://stackoverflow.com/questions/46626633/how-do-you-insert-html-into-a-quilljs
                          setTimeout(() => {
                            quillRef.clipboard.dangerouslyPasteHTML(
                              getComputedValue(template.body, true)
                            );

                            subQuillRef.clipboard.dangerouslyPasteHTML(
                              getComputedValueColorSub(
                                getComputedValue(template.subject, true)
                              )
                            );
                          }, 100);
                        }
                      }}
                    />
                  </FormGroup>
                </Div>

                <Div>
                  <Button
                    onClick={() => setShowConfirm(true)}
                    variant="outline-primary"
                    loading={isSaving}
                  >
                    {isSaving && <img width={25} src={LoadingImg} />} Save
                    Template
                  </Button>
                </Div>
              </Div>

              <FormGroup>
                <FormGroup.Label>Email Subject</FormGroup.Label>
                <Div className="survey-quill-subject">
                  <RichEditor
                    onChange={(html: string, text: string) => {
                      props.setEmailData((eD: any) => ({
                        ...eD,
                        subject: getComputedValueText(text, false),
                      }));
                      // delayedSubChange();
                    }}
                    value={getComputedValueColorSub(
                      getComputedValue(props.emailData?.subject, true)
                    )}
                    insertOptions={CUSTOM_EMAIL_INSERT_OPTIONS.filter(
                      (el: any) => el.id !== 1 && el.id !== 2
                    )
                      .filter((el: any) => {
                        return !(
                          props.survey?.type === "SurveyIndexEngagement" &&
                          el.id === 7
                        );
                      })
                      .filter((el: any) => {
                        return !(targetGroup !== "SPECIAL" && el.id === 10);
                      })
                      .filter((el: any) => {
                        return !(
                          props.survey?.type === "SurveyIndexExit" &&
                          [5, 6, 7, 10].some((sel: any) => sel === el.id)
                        );
                      })
                      .filter((el: any) => {
                        return !(
                          props.survey?.type !== "SurveyIndexExit" &&
                          el.id === 11
                        );
                      })}
                    setRef={setSubRef}
                  />
                </Div>
              </FormGroup>
              <FormGroup>
                <FormGroup.Label>Email Body</FormGroup.Label>
                <Div className="survey-quill">
                  <RichEditor
                    onChange={(html: string, text: string) => {
                      props.setEmailData((eD: any) => ({
                        ...eD,
                        body: getComputedValue(html, false),
                        text: getComputedValueText(text, false),
                      }));
                    }}
                    value={getComputedValue(props.emailData?.body, true)}
                    insertOptions={CUSTOM_EMAIL_INSERT_OPTIONS.filter(
                      (el: any) => {
                        return !(
                          props.survey?.type === "SurveyIndexEngagement" &&
                          el.id === 7
                        );
                      }
                    )
                      .filter((el: any) => {
                        return !(targetGroup !== "SPECIAL" && el.id === 10);
                      })
                      .filter((el: any) => {
                        return !(
                          props.survey?.type === "SurveyIndexExit" &&
                          [5, 6, 7, 10].some((sel: any) => sel === el.id)
                        );
                      })
                      .filter((el: any) => {
                        return !(
                          props.survey?.type !== "SurveyIndexExit" &&
                          el.id === 11
                        );
                      })}
                    setRef={setRef}
                  />
                  <Div>
                    <FormControl.Textarea
                      className="email-body-foot"
                      disabled
                      value={`This is an automated email. Please do not respond to this email.
If you have any technical issues, please contact our support <NAME_EMAIL>

Unmatched by Survey Research Associates
Professional Development | People Management | Employee Engagement software`}
                      rows={5}
                      style={{ color: "#6D6D6D", fontSize: 14 }}
                    />
                  </Div>
                </Div>
              </FormGroup>
            </Form>
          </Div>
        </Div>
        {false && (
          <Card.Footer className="mr-4">
            <Button
              onClick={sendMockEmail}
              variant="outline-primary"
              className="mr-3"
            >
              Mock Send
            </Button>
            <Button
              onClick={() => setShowConfirm(true)}
              variant="outline-primary"
            >
              Send Email
            </Button>
          </Card.Footer>
        )}
      </Card>
    );
  };

  const onFilterChange = (_filter: string) => {
    setFilter(_filter);
  };

  const getNavItem = (title: string, key: string) => {

    return <UMTab
    eventKey={key}
    activeKey={filter}
    onClick={() => onFilterChange(key)}
  >
    {title}
  </UMTab>;
  };

  return (
    <Div>
      <CustomHeader
        style={{ marginLeft: layout.marginLeft, height: 107 }}
        title={
          <Div>
            <Text.H1 className="pb-2">{survey.name}</Text.H1>
            <Div className="sticky-tabs-container">
              <Nav className="nav-tabs sticky">
                {getNavItem("Emails", "emails")}
                {getNavItem("Logs", "logs")}
                {getNavItem("Templates", "templates")}
                {getNavItem("Nudge Settings", "nudge")}
              </Nav>
            </Div>
          </Div>
        }
        breadcrumbs={[
          {
            label: "Surveys",
            icon: <Paste className="grey-icon__svg" />,
            route: appUrls.admin.survey.default,
          },
          { label: "Manage Survey" },
          { label: survey.name },
        ]}
      />
      <PageContainer className="pt-4">
        <Layout.Container fluid className="pt-4">
          <Div>{filter === "logs" && <Logs indexId={props.indexId} />}</Div>
          {filter === "emails" && (
            <>
              <QuickSend
                sendEmail={sendEmail}
                getRecipientsCount={getRecipientsCount}
                sendMockEmail={sendMockEmail}
                setShowSendCustomModal={setShowSendCustomModal}
                userOptions={userOptions}
                onInputChange={getUserOptions}
                setSelectedEmails={setSelectedEmails}
                selectedEmails={selectedEmails}
                sendCustomEmail={sendCustomEmail}
                sendMockCustomEmail={sendMockCustomEmail}
                getRecipientsCountsArr={getRecipientsCountsArr}
                // onBlur={() => setUserOptions([])}
                survey={props.survey}
              />
            </>
          )}

          {filter === "nudge" && (
            <>
              <NudgeSettingsContainer
                onFilterChange={onFilterChange}
                survey={{ ...survey, indexID: props.indexId }}
              />
            </>
          )}

          {filter === "templates" && getUpdateEmailTemplate()}
        </Layout.Container>
      </PageContainer>
      <Confirmation
        confirmText="Are you sure you want to save template?"
        show={showConfirm}
        onOk={async () => {
          await props.updateEmailData(
            {
              ...props.emailData,
            },
            false,
            setIsSaving,
            true
          );
        }}
        setShow={setShowConfirm}
      />
      <Modal show={showSendCustomModal} size="lg" centered>
        <ModalHeader
          title="Send a Custom Email"
          onHide={() => setShowSendCustomModal(false)}
        />
        <Modal.Body>
          <SendCustomEmail
            userOptions={userOptions}
            onInputChange={getUserOptions}
            setSelectedEmails={setSelectedEmails}
            selectedEmails={selectedEmails}
            getTemplate={getTemplate}
            getRecipientsCount={getRecipientsCount}
            sendCustomEmailTemplate={sendCustomEmailTemplate}
            sendEmail={sendEmail}
            sendMockCustomEmail={sendMockCustomEmail}
            updateEmailData={props.updateEmailData}
            getRecipientsCountsArr={getRecipientsCountsArr}
            // onBlur={() => setUserOptions([])}
            survey={props.survey}
          />
        </Modal.Body>
      </Modal>
      <Modal show={showParticipantsModal} size="xl" centered>
        <ModalHeader
          title="Participants"
          onHide={() => setShowParticipantsModal(false)}
        />
        <Modal.Body>
          <ParticipantsList logID={showParticipantsModal} />
        </Modal.Body>
      </Modal>
    </Div>
  );
};

export default Emails;
