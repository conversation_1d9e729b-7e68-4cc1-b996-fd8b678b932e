// Node modules
import React from "react";
import { useEffect, useFormik } from "unmatched/hooks";
import { QUESTION } from "../../survey-enums";
import {
  patchCheckboxOptionsFact,
  patchRadioOptionsFact,
  patchRatingOptionsFact,
  patchSurveyQuestionFact,
  removeSurveyQuestionFact,
} from "../../survey-api";
import util from "unmatched/utils";
import useToastr from "unmatched/modules/toastr/hook";
import surveyCore from "unmatched/survey/creation/components";
// import Rating from "./Rating-R";
// import OptionLabel from "./OptionLabel-R";
// import QuestionFormComponent from "./QuestionForm-R";
// import QuestionTypes from "./QuestionTypes-R";
import { get } from "lodash";
// import Checkbox from "./Checkbox-R";
// import QuestionOptionWrapper from "./QuestionOptionWrapper-R";

const {
  Radio,
  Checkbox,
  Input,
  QuestionOptionWrapper,
  Rating,
  OptionLabel,
  // QuestionTypes,
} = surveyCore;

const QuestionFormComponent = surveyCore.QuestionForm;

const { yup } = util.formik;
interface QuestionFormProps {
  question: any;
  index: number;
  hideCollectFeedback: boolean;
  hasDemoLabel: boolean;
  onPatch: Function;
  updateValidators: Function;
  onClone: Function;
  onDelete: Function;
  setLoading: Function;
  viewOnly?: boolean;
}

const OptionLabelWithFormik = (props: any) => {
  // const { option } = props;
  const formik = useFormik({
    initialValues: {
      option: props.text || "",
    },
    validationSchema: yup.object().shape({
      option: yup.string().required(),
    }),
    onSubmit: () => {
      // console.log()
    },
  });

  return <OptionLabel {...props} formik={formik} />;
};

const QuestionForm = (props: QuestionFormProps) => {
  const toastr = useToastr();
  const {
    question,
    onPatch,
    updateValidators,
    onClone,
    onDelete,
    setLoading,
    hideCollectFeedback,
    hasDemoLabel,
    viewOnly,
  } = props;

  const validationSchema = yup.object().shape({
    type: yup.string().required(),
    question: yup.string().required(),
    mandatory: yup.bool(),
    name: hasDemoLabel ? yup.string().required() : yup.string(),
    textFeedback: yup.bool(),
  });

  const onSubmit = (formValues: any) => {
    if (props.viewOnly) return;
    const payload = {
      ...question,
      question: formValues.question,
      type: formValues.type,
      mandatory: formValues.mandatory,
      textFeedback: formValues.textFeedback,
      // name: formValues.name || question.name,
    };
    if (formValues.type !== question.type) {
      payload.updatedType = formValues.type;
    }
    if (formValues.name && formValues.name !== question.name) {
      payload.meta = {
        name: formValues.name || question.name,
      };
    }
    setLoading(true);
    patchSurveyQuestionFact(question.id, payload).then(
      (response: any) => {
        onPatch(response);
        setLoading(false);
      },
      (err: any) => {
        setLoading(false);
        toastr.onError(err);
      }
    );
  };

  const initialValues = {
    type: question.type || "",
    question: question.question || "",
    mandatory: question.mandatory || false,
    textFeedback: question.textFeedback || false,
    name: question.name || "",
  };

  const formikOptions = {
    initialValues,
    validationSchema,
    onSubmit,
  };

  const formik = useFormik(formikOptions);

  const onQuestionDelete = () => {
    setLoading(true);
    removeSurveyQuestionFact(question.id).then(
      () => {
        setLoading(false);
        onDelete(question.id);
      },
      () => {
        setLoading(false);
      }
    );
  };

  const onOptionsUpdate = (_data: any) => {
    const payload = {
      ...question,
      ..._data,
    };
    onPatch(payload);
  };

  useEffect(() => {
    const key = `question-${question.id}`;
    updateValidators({
      key,
      validate: () => util.formik.formikSubmitForm(formik),
    });
    return () => {
      updateValidators({ key, validate: null });
    };
  }, []);

  const renderOption = (_item: any, Content: any) => {
    return (
      <QuestionOptionWrapper
        typeItem={Content}
        options={_item}
        viewOnly={viewOnly}
        onRemove={(_id: any) => {
          _item.onRemove(_id)?.then((payload: any) => {
            onOptionsUpdate(payload);
          });
        }}
        key={_item.id}
        quesType={question.type}
      >
        <OptionLabelWithFormik
          onSubmit={(val: any) => {
            _item.onSubmit(val, _item)?.then((payload: any) => {
              onOptionsUpdate(payload);
            });
          }}
          updateValidators={updateValidators}
          setValidations={_item.setValidations}
          text={_item.value}
          option={_item}
          viewOnly={viewOnly}
        />
      </QuestionOptionWrapper>
    );
  }

  const optionTempate = {
    [QUESTION.RADIO]: (
      <Radio
        id={question.radioId}
        question={question}
        renderOption={renderOption}
        questionTypes={QUESTION}
        api={patchRadioOptionsFact}
      />
    ),
    [QUESTION.CHECKBOX]: (
      <Checkbox
        id={question.checkboxId}
        question={question}
        viewOnly={viewOnly}
        renderOption={renderOption}
        questionTypes={QUESTION}
        api={patchCheckboxOptionsFact}
      />
    ),
    [QUESTION.INPUT]: <Input />,
    [QUESTION.RATING]: (
      <Rating
        viewOnly={viewOnly}
        question={question}
        renderOption={renderOption}
        questionTypes={QUESTION}
        onUpdate={onOptionsUpdate}
        api={patchRatingOptionsFact}
      />
    ),
    [QUESTION.RANKING]: (
      <Rating
        viewOnly={viewOnly}
        question={question}
        renderOption={renderOption}
        questionTypes={QUESTION}
        onUpdate={onOptionsUpdate}
        api={patchRatingOptionsFact}
      />
    ),
  };

  return (
    <>
      <QuestionFormComponent
        initialValues={initialValues}
        question={question}
        index={props.index}
        onClone={onClone}
        onDelete={onQuestionDelete}
        formik={formik}
        questionTypes={QUESTION}
        permissions={{
          hasName: hasDemoLabel,
          hideCollectFeedback,
          readonly: viewOnly,
        }}
      >
        {get(optionTempate, question.type)}
        {/* {question.type === QUESTION.RANKING ||
          (question.type === QUESTION.RATING ? (
            get(optionTempate, question.type)
          ) : (
            <QuestionTypes
              Radio={(_props: any) => (
                <Radio {..._props} api={patchRadioOptionsFact} />
              )}
              Checkbox={(_props: any) => (
                <Checkbox {..._props} api={patchCheckboxOptionsFact} />
              )}
              Input={Input}
              Rating={(_props: any) => (
                <Rating
                  {..._props}
                  onUpdate={onOptionsUpdate}
                  api={patchRatingOptionsFact}
                />
              )}
              question={question}
              type={question.type}
              viewOnly={viewOnly}
              questionTypes={QUESTION}
              renderOption={(_item: any, Content: any) => {
                return (
                  <QuestionOptionWrapper
                    typeItem={Content}
                    options={_item}
                    viewOnly={viewOnly}
                    onRemove={(_id: any) => {
                      _item.onRemove(_id)
                      .then((payload: any) => {
                        onOptionsUpdate(payload);
                      });
                    }}
                    key={_item.id}
                  >
                    <OptionLabelWithFormik
                      onSubmit={(val: any) => {
                        console.log(_item.onSubmit);
                        _item.onSubmit(val, _item)
                        .then((payload: any) => {
                          onOptionsUpdate(payload);
                        });
                      }}
                      updateValidators={updateValidators}
                      setValidations={_item.setValidations}
                      text={_item.value}
                      option={_item}
                      viewOnly={viewOnly}
                    />
                  </QuestionOptionWrapper>
                );
              }}
            />
          ))} */}
      </QuestionFormComponent>
    </>
  );
};

export default QuestionForm;
