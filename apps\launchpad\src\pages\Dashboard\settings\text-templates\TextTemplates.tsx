import React, { useState, useEffect } from "react";
import { useTextTemplates } from "./useTextTemplates";
import { Text } from "@repo/ui/components/Text";
import { Button } from "@repo/ui/components/button";
import { Textarea } from "@repo/ui/components/textarea";
import { Separator } from "@repo/ui/components/separator";
import { PreviewLogin } from "./components/PreviewLogin";
import { ContactForm } from "./components/ContactForm";

export const TextTemplates = () => {
  useEffect(() => {
    document.title = "Text Templates - Unmatched";
  }, []);
  const {
    loading,
    loginText,
    setLoginText,
    contactText,
    setContactText,
    contacts,
    reportContacts,
    editState,
    setEditState,
    handleSaveLoginText,
    handleSaveContactText,
    handleSaveReportContacts,
    addContact,
    removeContact,
    updateContact,
    addReportContact,
    removeReportContact,
    updateReportContact
  } = useTextTemplates();

  const [showPreview, setShowPreview] = useState(false);

  // Safely handle potentially undefined values
  const safeLoginText = loginText || "";
  const safeContactText = contactText || "";
  const safeContacts = Array.isArray(contacts) ? contacts : [];
  const safeReportContacts = Array.isArray(reportContacts) ? reportContacts : [];

  return (
    <div className="container mx-auto p-4 space-y-8">
      <Text.H2 className="text-2xl font-bold">Text Templates</Text.H2>

      {/* Welcome Message Section */}
      <div className="space-y-4">
        <Text.H3>Welcome message</Text.H3>
        <Text.P2 className="text-gray-600">
          Write welcome text for your employees on the login page{" "}
          <button
            className="text-primary underline cursor-pointer"
            onClick={() => setShowPreview(true)}
          >
            Preview Page
          </button>
        </Text.P2>

        <div className="relative max-w-2xl">
          <Textarea
            maxLength={130}
            value={safeLoginText}
            onChange={(e) => setLoginText(e.target.value)}
            disabled={editState !== "LOGIN_TEXT"}
            className="pr-14 resize-none min-h-[100px]"
          />
          <Text.P2 className="absolute bottom-2 right-3 text-gray-500">
            {safeLoginText.length}/130
          </Text.P2>
        </div>

        {editState !== "LOGIN_TEXT" ? (
          <Button
            onClick={() => setEditState("LOGIN_TEXT")}
            disabled={loading}
          >
            Edit
          </Button>
        ) : (
          <Button
            onClick={handleSaveLoginText}
            disabled={loading}
          >
            Save
          </Button>
        )}
      </div>

      <Separator className="my-8" />

      {/* Point of contact for survey takers */}
      <ContactForm
        title="Point of contact for survey takers"
        description="Provide contact information of admin users who will resolve the queries for users while participating in the survey process."
        contactText={safeContactText}
        setContactText={setContactText}
        contacts={safeContacts}
        addContact={addContact}
        removeContact={removeContact}
        updateContact={updateContact}
        isEditing={editState === "CONTACT_TEXT"}
        onEdit={() => setEditState("CONTACT_TEXT")}
        onSave={handleSaveContactText}
        loading={loading}
      />

      <Separator className="my-8" />

      {/* Point of contact for clarifications on reports */}
      <ContactForm
        title="Point of contact for clarifications on reports"
        description="Provide contact information of admin users who will resolve the queries regarding clarifications on reports."
        contactText=""
        setContactText={() => {}}
        contacts={safeReportContacts}
        addContact={addReportContact}
        removeContact={removeReportContact}
        updateContact={updateReportContact}
        isEditing={editState === "REPORT_CONTACT"}
        onEdit={() => setEditState("REPORT_CONTACT")}
        onSave={handleSaveReportContacts}
        loading={loading}
      />

      {/* Preview Login Modal */}
      <PreviewLogin
        showPreview={showPreview}
        setShowPreview={setShowPreview}
        loginText={safeLoginText}
      />
    </div>
  );
};
