import React, { useState } from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@repo/ui/components/dropdown-menu";
// import { Text } from "@repo/ui/components/Text";
import { Link, NavLink, useNavigate } from "react-router";
import { Button } from "@repo/ui/components/button";
import UNMATCHED_BOTM_LOGO from "../../assets/images/unmatched_btm.svg";
import useSession from "@/unmatched/modules/session/hook";
import ROUTES from "@/routes";
import ChangePasswordModal from "./ChangePasswordModal";
import { ModeToggle } from "@repo/ui/components/mode-toggle";
import { useTheme } from "@repo/ui/components/theme-provider";
type Props = {
  children: React.ReactNode;
};
const launcherRoutes = {
  HOME: ROUTES.ROOT,
  ADMIN: "/admin",
  SETTINGS: ROUTES.SETTINGS.ROOT,
};

function SharedLayout({ children }: Props) {
  const { user, client } = useSession();
  const {theme} = useTheme();
  const navigate = useNavigate();
  const [showChangePassword, setShowChangePassword] = useState(false);

  return (
    <div className="min-h-screen h-full">
      <header className="bg-card px-5 py-2 border-b">
        <nav className="flex flex-row items-center">
          <Link to={ROUTES.ROOT}>
            <img
              src={theme === "dark" ? client?.darkLogo : client?.lightLogo}
              alt="CLIENT_LOGO"
              className="h-[20px] w-full"
            />
          </Link>
          <div className="flex justify-center flex-grow">
            <div className="h-full header_nav">
              <NavLink className="fs-14 h-full pt-3" to={launcherRoutes.HOME}>
                Home
              </NavLink>
              {user.role === "ADMIN" && (
                <NavLink
                  className="fs-14 h-full pt-3"
                  to={launcherRoutes.SETTINGS}
                >
                  Settings
                </NavLink>
              )}
            </div>
          </div>
          <div className="flex items-center justify-end gap-3">
            <ModeToggle />
            <DropdownMenu>
              <DropdownMenuTrigger
                // style={{ width: 28, height: 28 }}
                // className="rounded-circle"
                asChild
              >
                {/* {name} */}
                <Button
                  variant="ghost"
                  className="rounded-full text-xs bg-primary text-primary-foreground m-0 w-8 h-8 p-0 hover:bg-primary/80 hover:text-primary-foreground"
                >
                  {user.firstName && user.firstName[0]}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                style={{ width: 200 }}
                align="end"
                className="mt-2 shadow-sm"
              >
                <div className="font-bold my-1 pl-2 text-break pr-2">
                  {`${user.firstName} ${user.lastName}`}
                </div>
                <div className="fs-12 pl-2 pr-2 text-break text-zinc-300 line-clamp-1 text-xs">
                  {user.email}
                </div>
                <hr className="my-2" />
                <DropdownMenuItem
                  onClick={() => setShowChangePassword(true)}
                  className="cursor-pointer text-xs outline-none"
                >
                  {user.isPasswordSet ? "Change Password" : "Set Password"}
                </DropdownMenuItem>
                <DropdownMenuItem
                  className="cursor-pointer text-xs outline-none text-red-500"
                  onClick={() => navigate("/logout")}
                  // as={Div}
                >
                  Logout
                </DropdownMenuItem>
                {/* <Link
                to={util.appUrls.logout}
                className={util.getUtilClassName({ textColor: "danger" })}
              >
                <Dropdown.Item className="fs-14 py-1 px-3" as={Div}>
                  Logout
                </Dropdown.Item>
              </Link> */}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </nav>
      </header>
      {children}
      <footer className="fixed bottom-0 w-full border-t bg-card">
        <nav className=" py-2 justify-end px-5">
          <p className="text-right flex text-xs justify-end items-center py-1 mt-0 text-black/50 dark:text-white/50">
            <img src={UNMATCHED_BOTM_LOGO} height="15px" className="mr-3 dark:invert" />{" "}
            Employee well-being. Engagement. Upliftment. Culture.
          </p>
        </nav>
      </footer>
      <ChangePasswordModal
        open={showChangePassword}
        onOpenChange={() => setShowChangePassword(false)}
      />
    </div>
  );
}

export default SharedLayout;
