import React from "react";
import { Modal } from "react-bootstrap";
import { But<PERSON>, Card, Text } from "@unmatchedoffl/ui-core";
// import Button from "./Button/Button";
// import Card from "./Card/Card";
// import Text from "./Text/Text";

export default function Confirmation(props: {
  show: boolean;
  onOk?: Function;
  confirmText?: string;
  setShow?: Function;
}) {
  const { show, onOk, confirmText, setShow } = props;
  return (
    <Modal centered show={show}>
      <Card>
        <Card.Body>
          <Text.H3 className="pb-3 text-primary">{confirmText}</Text.H3>
        </Card.Body>
        <Card.Footer className="text-right pr-4">
          <Button variant="outline-primary" onClick={() => setShow?.(false)}>
            No
          </Button>
          <Button
            variant="danger"
            className="ml-3"
            onClick={() => {
              onOk?.();
              setShow?.(false);
            }}
          >
            Yes
          </Button>
        </Card.Footer>
      </Card>
    </Modal>
  );
}
