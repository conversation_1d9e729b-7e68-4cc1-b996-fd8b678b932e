import React from "react";
import ModalHeader from "pages/AdminApp/ModalHeader";
import util from "unmatched/utils";
// import surveyCore from "unmatched/survey/creation/components";
import { launchSurveyFact, onValidateSurveyFact, publishDraftSurveyFact } from "../../survey-api";
import { useHistory } from "react-router";
import { useCreateSurveyContext } from "../Provider";
import useToastr from "unmatched/modules/toastr/hook";
import ValidateSurveyComponentLocal from "./ValidateSurveyComponentLocal";
// import PropTypes from 'prop-types';

// const ValidateSurveyComponent = surveyCore.ValidateSurvey;

const validation = {
  success: "success",
  error: "error",
  loading: "loading",
};

const fieldsData = {
  properties: {
    stage: validation.loading,
    success: {
      header: "All fields are verified",
      content: "Success",
    },
    error: {
      header: "Please verify all fields",
      content: "",
    },
    loading: {
      header: "Verifying required mandatory fields",
      content: "Please wait...",
    },
  },
  participants: {
    stage: validation.loading,
    success: {
      header: "Participants data is verified",
      content: "Success",
    },
    error: {
      header: "Participants data has errors",
      content: "",
    },
    loading: {
      header: "Verifying participants data",
      content: "Checking for invalid and duplicate records",
    },
  },
  questions: {
    stage: validation.loading,
    success: {
      header: "Questionnaire verified",
      content: "Success",
    },
    error: {
      header: "Questionnaire data has errors",
      content: "",
    },
    loading: {
      header: "Checking for invalid questions",
      content: "Checking Questionnaire",
    },
  },
  email: {
    stage: validation.loading,
    success: {
      header: "Email Template verified",
      content: "Success",
    },
    error: {
      header: "Email Template has errors",
      content: "",
    },
    loading: {
      header: "Checking for invalid email templates",
      content: "Checking Email Templates",
    },
  },
};

const ValidateSurvey = (props: {
  show: boolean;
  onHide: Function;
  indexId: string;
  sendInvite: any;
}) => {
  const { survey } = useCreateSurveyContext();

  const history = useHistory();

  const toastr = useToastr();

  const onSuccess = () => {
    history.push(util.appUrls.admin.survey.default);
  };

  return (
    <>
      <ValidateSurveyComponentLocal
        survey={survey}
        onHide={props.onHide}
        show={props.show}
        indexId={props.indexId}
        toastr={toastr}
        fieldsData={fieldsData}
        validation={validation}
        onSuccess={onSuccess}
        Header={ModalHeader}
        onValidateSurveyFact={onValidateSurveyFact}
        publishDraftSurveyFact={publishDraftSurveyFact}
        launchSurveyFact={launchSurveyFact}
        sendInvite={props.sendInvite}        
      />
    </>
  );
};

ValidateSurvey.propTypes = {};

export default ValidateSurvey;
