{"name": "unmatched", "version": "1.0.0", "type": "module", "private": true, "exports": {"./*": "./src/*", "./utils": "./src/utils/index.ts", "./utils/*": "./src/utils/*.ts", "./modules": "./src/modules/index.ts", "./modules/*": "./src/modules/*.ts"}, "devDependencies": {"@eslint/js": "^9.24.0", "@next/eslint-plugin-next": "^15.2.1", "eslint": "^9.24.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-only-warn": "^1.1.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-turbo": "^2.5.0", "globals": "^16.0.0", "typescript": "^5.8.2", "typescript-eslint": "^8.29.0"}, "dependencies": {"@reduxjs/toolkit": "^2.7.0", "axios": "^1.8.4", "lodash": "^4.17.21", "luxon": "^3.6.1", "react-router": "^7.5.1", "react-use": "^17.6.0", "ua-parser-js": "^2.0.3"}}