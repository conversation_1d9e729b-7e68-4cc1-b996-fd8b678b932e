import React, { useState, useRef, useEffect } from "react";
import { Input } from "@repo/ui/components/input";
import { Search, Filter, ChevronDown, Loader2, Download, Plus } from "lucide-react";
import { Button } from "@repo/ui/components/button";
import { FilterOption } from "../useEmployees";

interface EmployeeHeaderProps {
  searchValue: string;
  onSearchChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  metadataFields: string[];
  metadataValues: FilterOption[];
  selectedMetadataField: string | null;
  selectedMetadataValue: string | null;
  onMetadataFieldChange: (field: string | null) => void;
  onMetadataValueChange: (value: string | null) => void;
  formatFieldName: (field: string) => string;
  loadingMetadata?: boolean;
  onAddEmployee?: () => void;
  onDownloadEmployees?: () => void;
}

export const EmployeeHeader: React.FC<EmployeeHeaderProps> = ({
  searchValue,
  onSearchChange,
  metadataFields,
  metadataValues,
  selectedMetadataField,
  selectedMetadataValue,
  onMetadataFieldChange,
  onMetadataValueChange,
  formatFieldName,
  loadingMetadata = false,
  onAddEmployee = () => {},
  onDownloadEmployees = () => {},
}) => {
  const [isFieldDropdownOpen, setIsFieldDropdownOpen] = useState(false);
  const [isValueDropdownOpen, setIsValueDropdownOpen] = useState(false);
  const fieldDropdownRef = useRef<HTMLDivElement>(null);
  const valueDropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (fieldDropdownRef.current && !fieldDropdownRef.current.contains(event.target as Node)) {
        setIsFieldDropdownOpen(false);
      }
      if (valueDropdownRef.current && !valueDropdownRef.current.contains(event.target as Node)) {
        setIsValueDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h1 className="text-xl font-bold">All Employees</h1>
        <div className="flex items-center gap-2">
          <div className="relative w-64">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search for name, email or emp id"
              value={searchValue}
              onChange={onSearchChange}
              className="pl-8"
            />
          </div>

          {/* Add Employee Button */}
          <Button
            className="flex items-center gap-1 hover:bg-primary/80"
            onClick={onAddEmployee}
          >
            <Plus className="h-4 w-4" />
            <span>Add Employees</span>
          </Button>

          {/* Download Button */}
          <Button
            variant="outline"
            className="flex items-center gap-1"
            onClick={onDownloadEmployees}
          >
            <Download className="h-4 w-4" />
            <span>Download</span>
          </Button>
        </div>
      </div>

      <div className="flex items-center gap-2">
        <Filter className="h-5 w-5 text-muted-foreground" />

        {loadingMetadata ? (
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Loader2 className="h-4 w-4 animate-spin" />
            Loading filters...
          </div>
        ) : (
          <>
            {/* First dropdown - Select metadata field */}
            <div className="relative" ref={fieldDropdownRef}>
              <button
                className="flex items-center justify-between w-[180px] px-3 py-2 border rounded-md bg-background"
                onClick={() => setIsFieldDropdownOpen(!isFieldDropdownOpen)}
              >
                <span className="text-sm font-medium">
                  {selectedMetadataField ? formatFieldName(selectedMetadataField) : "Class year"}
                </span>
                <ChevronDown className="h-4 w-4 ml-2" />
              </button>

              {isFieldDropdownOpen && (
                <div className="absolute z-10 mt-1 w-[180px] bg-background border rounded-md shadow-lg max-h-[200px] overflow-y-auto">
                  {metadataFields.length === 0 ? (
                    <div className="px-3 py-2 text-sm text-muted-foreground">No fields available</div>
                  ) : (
                    <div className="py-1">
                      {metadataFields.map(field => (
                        <div
                          key={field}
                          className="px-3 py-2 text-sm hover:bg-muted cursor-pointer font-medium"
                          onClick={() => {
                            onMetadataFieldChange(field);
                            setIsFieldDropdownOpen(false);
                          }}
                        >
                          {formatFieldName(field)}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Second dropdown - Select metadata value */}
            <div className="relative" ref={valueDropdownRef}>
              <button
                className="flex items-center justify-between w-[180px] px-3 py-2 border rounded-md bg-background"
                onClick={() => setIsValueDropdownOpen(!isValueDropdownOpen)}
                disabled={!selectedMetadataField}
              >
                <span className="text-sm font-medium">
                  {selectedMetadataValue || "Select value"}
                </span>
                <ChevronDown className="h-4 w-4 ml-2" />
              </button>

              {isValueDropdownOpen && selectedMetadataField && (
                <div className="absolute z-10 mt-1 w-[180px] bg-background border rounded-md shadow-lg max-h-[200px] overflow-y-auto">
                  <div
                    className="sticky top-0 px-3 py-2 text-sm hover:bg-muted cursor-pointer bg-background border-b font-medium"
                    onClick={() => {
                      onMetadataValueChange(null);
                      setIsValueDropdownOpen(false);
                    }}
                  >
                    All {selectedMetadataField ? formatFieldName(selectedMetadataField) : ""}
                  </div>
                  {metadataValues.length === 0 ? (
                    <div className="px-3 py-2 text-sm text-muted-foreground">No values available</div>
                  ) : (
                    <div className="py-1">
                      {metadataValues.map(option => (
                        <div
                          key={option.value}
                          className="px-3 py-2 text-sm hover:bg-muted cursor-pointer font-medium"
                          onClick={() => {
                            onMetadataValueChange(option.value);
                            setIsValueDropdownOpen(false);
                          }}
                        >
                          {option.label}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </div>
          </>
        )}

        {/* Clear filter button - only show when both field and value are selected */}
        {selectedMetadataField && selectedMetadataValue && (
          <Button
            onClick={() => {
              onMetadataFieldChange(null);
              onMetadataValueChange(null);
            }}
            className="px-3 py-2 text-sm font-medium rounded-md hover:bg-primary/80"
          >
            Clear filter
          </Button>
        )}
      </div>
    </div>
  );
};
