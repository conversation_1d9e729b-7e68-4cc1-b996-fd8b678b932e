/**
 * Application-wide route definitions
 *
 * This file centralizes all route paths used throughout the application.
 * Use these constants instead of hardcoding paths to ensure consistency
 * and make route changes easier to manage.
 */
const settingsRoot = "/settings";
const AUTH = "/auth";

export const ROUTES = {
  // Dashboard routes
  ROOT: "/",
  LOGOUT: "/logout",
  SETTINGS: {
    ROOT: settingsRoot,
    EMPLOYEES: `${settingsRoot}/employees`,
    FILES: `${settingsRoot}/files`,
    FILE_DETAILS: `${settingsRoot}/files/:id`,
    FILE_DETAILS_PATH: (id: string) => `${settingsRoot}/files/${id}`,
    TEXT_TEMPLATES: `${settingsRoot}/text-templates`,
  },

  // Authentication routes
  AUTH: {
    ROOT: "/auth",
    LOGIN: "/auth/login",
    REQUEST_PASSWORD: `${AUTH}/request-password`,
    MAGIC_LINK: `${AUTH}/magiclink/:email/:token`,
    // Dynamic Urls
    ACTIVATION: `${AUTH}/activation/:email/:token`,
    RESET_PASSWORD: `${AUTH}/reset-password/:email/:token`,
  },

  // Legal/Public routes
  LEGAL: {
    ROOT: "/legal",
    TERMS: "/legal/terms",
    PRIVACY: "/legal/privacy",
    CONFIDENTIALITY: "/legal/confidentiality",
  },

  // Profile routes
  PROFILE: {
    ROOT: "/profile",
    SETTINGS: "/profile/settings",
  },

  // Help routes
  HELP: {
    ROOT: "/help",
    DOCUMENTATION: "/help/documentation",
    FAQ: "/help/faq",
    CONTACT: "/help/contact",
  },
};

export default ROUTES;
