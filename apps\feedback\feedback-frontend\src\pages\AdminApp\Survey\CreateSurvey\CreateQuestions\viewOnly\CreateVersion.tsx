import React from "react";
import { useFormik } from "unmatched/hooks";
import util from "unmatched/utils";
import {
  getWavierQuestionFact,
  patchSurveyVersionFact,
} from "../../../survey-api";
import CreateVersionComponent from "./CreateVersionComponent";


const { yup } = util.formik;


const CreateVersion = (props: any) => {
  const { version, onUpdate, updateValidators, setLoading, isUpwardReview, survey } =
    props;
  const [wavier, setWavier]: any = React.useState({});
  const initialValues = {
    label: version.label || "",
    raterGroup: "",
    targetGroup: "",
    minResponses: version.min_response_count || 0,
  };
  const upwardValidations: any = isUpwardReview
    ? {
        raterGroup: yup.string(),
        targetGroup: yup.string(),
        minResponses: yup.string(),
      }
    : {};

  const formikOptions = {
    initialValues,
    validationSchema: yup.object().shape({
      label: yup.string().required(),
      ...upwardValidations,
    }),
    onSubmit: () => {
      // on submit
    },
  };

  const formik = useFormik(formikOptions);

  const getWavierQuestion = () => {
    if (!version.report_eligibility_settings) return;
    getWavierQuestionFact(version.report_eligibility_settings).then((response: any) => {
      setWavier(response);
    });
  };

  React.useEffect(() => {
    const key = `version-${version.id}`;
    updateValidators({
      key,
      validate: () => util.formik.formikSubmitForm(formik),
    });
    return () => {
      updateValidators({
        key,
        validate: null,
      });
    };
  }, []);

  React.useEffect(() => {
    getWavierQuestion();
  }, [version.eligibilityId]);
  return (
    <>
      <CreateVersionComponent
        patchSurveyVersionFact={patchSurveyVersionFact}
        formik={formik}
        onUpdate={onUpdate}
        setLoading={setLoading}
        wavier={wavier}
        version={version}
        permissions={{
          hasVisibility: isUpwardReview,
        }}
        survey={survey}
      />
    </>
  );
};

export default CreateVersion;
