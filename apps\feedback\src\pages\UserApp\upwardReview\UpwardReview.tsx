import React from 'react';
import { useParams, Navigate, useLocation } from 'react-router';
import { USER_ROUTES } from '@/app.routes';
import Pairings from './Pairings';

interface ParamTypes {
  id?: string;
}

export default function UpwardReview() {
  const { id } = useParams<ParamTypes>();
  const location = useLocation();

  // Check if we're on the base upward route (without /pairings or /take)
  const isBaseUpwardRoute = location.pathname.endsWith('/upward');

  // Default redirect to pairings page (similar to legacy app)
  if (isBaseUpwardRoute) {
    return <Navigate to={USER_ROUTES().dashboard.upwardReview.getPairingsUrl(id || "")} replace />;
  }

  // For all other upward routes (pairings, take), show the Pairings component
  // The Pairings component will handle its own internal routing
  return <Pairings />;
}
