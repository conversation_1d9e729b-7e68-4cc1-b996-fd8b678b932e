// Node modules
import React, { ReactNode } from "react";
import { useHistory } from "react-router";
import styled from "styled-components";
// Helpers
import appUrls from "unmatched/utils/urls/app-urls";
// Components
import { Text, Layout, Card, Div } from "unmatched/components";
import Footer from "../Footer";
import Header from "../Header";

interface CardProps {
  children: ReactNode;
  title: string;
  onClick?: Function;
}

const ChooseSurveyCard = ({ title, children, onClick }: CardProps) => {
  return (
    <Layout.Col className="col-xl-3 col-lg-4 col-md-6 col-sm-12 pb-2 cursor-pointer">
      <Card
        onClick={() => {
          if (onClick) onClick();
        }}
        noShadow
      >
        <Div className="p-3">
          <Text.H3 className="pb-2">{title}</Text.H3>
          {children}
        </Div>
      </Card>
    </Layout.Col>
  );
};

const Pannel = styled.div`
  padding: 10px 0px 50px 0px;
`;

const ChooseSurvey = () => {
  const history = useHistory();

  return (
    <>
      <Header title={<Text.H1>Create a Survey</Text.H1>}></Header>
      <Layout.Container className="pt-3" fluid>
        <Text.P1 className="pb-4">
          Begin by choosing a type of survey, you can choose to create a
          template from scratch or clone an existing template.{" "}
        </Text.P1>
        <Pannel>
          <Text.H3 className="pb-3">New Survey</Text.H3>
          <Layout.Row>
            <ChooseSurveyCard
              onClick={() => {
                history.push(appUrls.admin.survey.create.getUpwardReviewUrl(0));
              }}
              title="Upward Review"
            >
              <Text.P2>
                Lorem ipsum dolor sit amet, consectetur adipiscing elit.
              </Text.P2>
            </ChooseSurveyCard>
            <ChooseSurveyCard title="Custom Survey">
              <Text.P2>
                Lorem ipsum dolor sit amet, consectetur adipiscing elit.
              </Text.P2>
            </ChooseSurveyCard>
          </Layout.Row>
        </Pannel>
        <Pannel>
          <Text.H3 className="pb-3">Draft Surveys</Text.H3>
          <Layout.Row>
            <ChooseSurveyCard title="360 Degree Feedback">
              <Text.P2>
                Lorem ipsum dolor sit amet, consectetur adipiscing elit.
              </Text.P2>
              <Text.P2 className="pt-3">
                Created {new Date().toDateString()}
              </Text.P2>
            </ChooseSurveyCard>
          </Layout.Row>
        </Pannel>
        <Pannel>
          <Text.H3 className="pb-3">Clone Existing Survey</Text.H3>
          <Layout.Row>
            <ChooseSurveyCard title="Upward Feedback March’20">
              <Text.P2>
                Lorem ipsum dolor sit amet, consectetur adipiscing elit.
              </Text.P2>
              <Text.P2 className="pt-3">
                Created {new Date().toDateString()}
              </Text.P2>
            </ChooseSurveyCard>
          </Layout.Row>
        </Pannel>
      </Layout.Container>
      <Footer hideBack />
    </>
  );
};

export default ChooseSurvey;
