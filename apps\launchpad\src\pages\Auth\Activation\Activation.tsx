// import { useFormik } from "formik";
import  {  useEffect, useState } from "react";
import useToastr from "@unmatched/modules/toastr/hook";
// import { Button, Card, div, Form, Span, Text } from "@unmatched/components";
import Loader from "../Loader";
import PasswordForm from "../Password/PasswordForm";
import FORM_SCHEMA, { validatePassword } from "../Password/password-form";
import AuthContainer from "../AuthContainer";
import { useNavigate, useParams } from "react-router";
import { activationFact } from "../auth.api";
// import { useHistory, useState } from "@unmatched/hooks";
import appUrls from "@unmatched/utils/urls/app-urls";
import { validateUserEmailFact } from "@unmatched/modules/session/api";
import { Text } from "@repo/ui/components/Text";
import { Form } from "@repo/ui/components/form";
import { Button } from "@repo/ui/components/button";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";

// interface ParamTypes {
//   email: string;
//   token: string;
// }

const META_DATA = {
  title: "Activate your account",
  content: "Hey there! Set up a strong password to get started",
  loadingTitle: "Setting up your account...",
  loadingContent: "Please wait while we setup your account",
};

const Error = (error: string) => {
  const errors: any = {
    USER_ALREADY_ACTIVE: "User is already active",
    USER_NOT_FOUND: "User not found. Try again",
    WRONG_TOKEN: "Wrong user token. Try again",
    OTHER: "Something went wrong",
  };
  return errors[error] ?? errors.OTHER;
};


export default function Activation() {
  // const history = useHistory();
  const { showToast } = useToastr();
  const { email, token } = useParams();
  const meta = META_DATA;
  const navigate = useNavigate();
  const [state, setState] = useState({
    token,
    email,
    password: "",
    confirmPassword: "",
    tooltip: {
      number: false,
      alphabet: false,
      min: false,
    },
    isLoading: true,
    error: {
      statusCode: 0,
      msg: "",
    },
  });

  useEffect(() => {
    const handleUser = async () => {
      try {
        const response = await validateUserEmailFact({ email }, {});
        if (response?.isActive) {
          // history.push(appUrls.auth.login);
          navigate(appUrls.auth.login);
        }
      } finally {
        setState((state: any) => ({
          ...state,
          isLoading: false,
        }));
      }
    };

    handleUser();
  }, []);

  const onFetchFail = (err: any, payload: any) => {
    showToast({
      show: true,
      title: "Error",
      variant: "danger",
      content: Error(err.msg),
    });
    setState((state: any) => ({
      ...state,
      isLoading: false,
      ...payload,
      error: err,
    }));
    navigate("/");
  };

  const onActivation = async (data: any) => {
    setState((state: any) => ({
      ...state,
      isLoading: true,
    }));
    try {
      await activationFact(data);
      showToast({
        variant: "success",
        title: "Activation successful",
        content: "Log in to continue",
      });
      navigate(appUrls.auth.login);
    } catch (err) {
      onFetchFail(err, {});
    }
  };

  // const formik = useFormik({
  //   initialValues: {
  //     email: email,
  //     token,
  //     password: "",
  //     confirmPassword: "",
  //   },
  //   validationSchema: FORM_SCHEMA,
  //   onSubmit: (values) => {
  //     const { email, token } = state;
  //     const { password } = values;
  //     if (email && token) onActivation({ email, password, token });
  //   },
  // });
  const form = useForm({
    defaultValues: {
      // email: email,
      // token,
      password: "",
      confirmPassword: "",
    },
    resolver: zodResolver(FORM_SCHEMA),
  });

  // const onPasswordChange = useCallback(
  //   (e: React.ChangeEvent<any>) => {
  //     formik.handleChange(e);
  //     const tooltip: any = validatePassword(e.target.value);
  //     setState((_state) => ({
  //       ..._state,
  //       tooltip,
  //     }));
  //   },
  //   [formik]
  // );

  let Content = null;
  const onFormSubmit = (values: z.infer<typeof FORM_SCHEMA>) => {
    // formik.handleSubmit(e);
    const { email, token } = state;
    const { password } = values;
    if (email && token) onActivation({ email, password, token });
  };

  if (state.isLoading) {
    Content = (
      <Loader
        title={meta.loadingTitle}
        icon="CircleNotch"
        content={meta.loadingContent}
      />
    );
  } else {
    Content = (
      // onReset={formik.handleReset} onSubmit={onFormSubmit}
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onFormSubmit)}>
          <Text.H2 className="text-primary pb-2">{meta.title}</Text.H2>
          <Text.P1 className="pb-4">
            {meta.content}
            <div className="pt-4">
              Account email id: <span className="font-bold">{email}</span>
            </div>
            <div className="">
              <PasswordForm
                form={form}
                // onPasswordChange={onPasswordChange}
                // tooltip={state.tooltip}
                // colXL={12}
              ></PasswordForm>
            </div>
          </Text.P1>
          <div>
            <Button size="lg" type="submit">
              Next
            </Button>
          </div>
        </form>
      </Form>
    );
  }
  return (
    <AuthContainer className="col-xl-4 offset-xl-4 col-lg-6 offset-lg-3">
      {Content}
    </AuthContainer>
  );
}
