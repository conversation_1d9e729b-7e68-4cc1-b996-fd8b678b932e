import React from "react";
import { Route, Redirect } from "react-router-dom";
import AppRoutes from "../../../AppRoutes";
import ENGAGEMENT_ROUTES from "./engagement-routes";
import appUrls from "unmatched/utils/urls/app-urls";

const EngagementAnalytics = () => {
  return (
    <AppRoutes routes={ENGAGEMENT_ROUTES}>
      <Route exact path={appUrls.admin.analytics.engagement.default}>
        <Redirect to={appUrls.admin.analytics.engagement.list} />
      </Route>
    </AppRoutes>
  );
};
export default EngagementAnalytics;
