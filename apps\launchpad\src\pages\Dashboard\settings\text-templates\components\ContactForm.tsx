import React from "react";
import { Input } from "@repo/ui/components/input";
import { Button } from "@repo/ui/components/button";
import { Text } from "@repo/ui/components/Text";
import { Textarea } from "@repo/ui/components/textarea";
import { Plus, X } from "lucide-react";
import { ContactInfo } from "../textTemplatesService";

interface ContactFormProps {
  title: string;
  description: string;
  contactText: string;
  setContactText: (text: string) => void;
  contacts: ContactInfo[];
  addContact: () => void;
  removeContact: (id: string) => void;
  updateContact: (id: string, field: keyof ContactInfo, value: string) => void;
  isEditing: boolean;
  onEdit: () => void;
  onSave: () => void;
  loading: boolean;
}

export const ContactForm: React.FC<ContactFormProps> = ({
  title,
  description,
  contactText = "",
  setContactText,
  contacts = [],
  addContact,
  removeContact,
  updateContact,
  isEditing,
  onEdit,
  onSave,
  loading
}) => {
  // Ensure contacts is an array
  const safeContacts = Array.isArray(contacts) ? contacts : [];

  return (
    <div className="space-y-4">
      <Text.H3>{title || "Contact Information"}</Text.H3>
      <Text.P2 className="text-gray-600">{description || ""}</Text.P2>

      <div className="max-w-2xl">
        <Textarea
          value={contactText}
          onChange={(e) => setContactText(e.target.value)}
          disabled={!isEditing}
          className="min-h-[120px] w-full"
        />
      </div>

      <div className="space-y-6">
        {safeContacts.map((contact, index) => (
          <div key={contact.id || index} className="space-y-2">
            {index > 0 && (
              <div className="flex items-center">
                <Text.P2 className="font-medium">Contact {index + 1}</Text.P2>
                {isEditing && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="ml-2 h-8 w-8 p-0"
                    onClick={() => removeContact(contact.id)}
                  >
                    <X className="h-4 w-4" />
                    <span className="sr-only">Remove contact</span>
                  </Button>
                )}
              </div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Input
                  placeholder="Full Name"
                  value={contact.fullName || ""}
                  onChange={(e) => updateContact(contact.id, "fullName", e.target.value)}
                  disabled={!isEditing}
                />
              </div>
              <div>
                <Input
                  placeholder="Title"
                  value={contact.title || ""}
                  onChange={(e) => updateContact(contact.id, "title", e.target.value)}
                  disabled={!isEditing}
                />
              </div>
              <div>
                <Input
                  placeholder="Phone"
                  value={contact.phone || ""}
                  onChange={(e) => updateContact(contact.id, "phone", e.target.value)}
                  disabled={!isEditing}
                />
              </div>
              <div>
                <Input
                  placeholder="Email"
                  type="email"
                  value={contact.email || ""}
                  onChange={(e) => updateContact(contact.id, "email", e.target.value)}
                  disabled={!isEditing}
                />
              </div>
              <div>
                <Input
                  placeholder="Location"
                  value={contact.location || ""}
                  onChange={(e) => updateContact(contact.id, "location", e.target.value)}
                  disabled={!isEditing}
                />
              </div>
              <div>
                <Input
                  placeholder="Branch"
                  value={contact.branch || ""}
                  onChange={(e) => updateContact(contact.id, "branch", e.target.value)}
                  disabled={!isEditing}
                />
              </div>
            </div>
          </div>
        ))}

        {isEditing && (
          <Button
            variant="outline"
            size="sm"
            className="flex items-center gap-1"
            onClick={addContact}
          >
            <Plus className="h-4 w-4" />
            Add Contact
          </Button>
        )}
      </div>

      {isEditing ? (
        <Button
          onClick={onSave}
          disabled={loading}
        >
          Save
        </Button>
      ) : (
        <Button
          onClick={onEdit}
          disabled={loading}
        >
          Edit
        </Button>
      )}
    </div>
  );
};
