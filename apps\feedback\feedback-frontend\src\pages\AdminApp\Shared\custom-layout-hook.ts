import { useAdminContext } from "../Provider";

const WIDTH = 250;

const useCustomLayout = () => {
  const meta = useAdminContext();

  return {
    sidebar: {
      width: meta.sidebar ? `${WIDTH}px` : "0px",
      marginLeft: meta.sidebar ? `${meta.width}px` : "0px",
    },
    container: {
      marginLeft: meta.margin ? `${WIDTH}px` : "0px",
    },
    marginLeft: meta.margin ? `${WIDTH + meta.width}px` : "0px",
  };
};

export default useCustomLayout;
