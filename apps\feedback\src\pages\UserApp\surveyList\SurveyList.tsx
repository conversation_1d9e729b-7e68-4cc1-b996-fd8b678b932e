import { useState } from 'react';
import { useNavigate } from 'react-router';
import { useSurveys, useSurveyStatus } from '../../../hooks/useSurveys';
import { SurveysSection } from '../../../components/survey/SurveysSection';
import { USER_ROUTES } from '@/app.routes';

function SurveyList() {
  const [page, setPage] = useState(1);
  const pageSize = 10;
  const navigate = useNavigate();

  const {
    surveys,
    statsMap,
    loading,
    error
  } = useSurveys(page, pageSize);

  const { isOngoing } = useSurveyStatus();

  const handleViewDetails = (surveyId: string) => {
    // Navigate to terms page as in legacy app
    navigate(USER_ROUTES().dashboard.getSurveyTermsUrl(surveyId));
  };
  
  const ongoingSurveys = surveys.filter(isOngoing);
  const upcomingSurveys = surveys.filter(survey => !isOngoing(survey));

  if (error) {
    return (
      <div className="flex min-h-screen w-full items-center justify-center text-red-500">
        {error}
      </div>
    );
  }


  return (
    <div className="w-full p-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-semibold">Surveys</h1>
        {/* Test navigation button */}
        <button
          onClick={() => navigate(USER_ROUTES().dashboard.getSurveyTermsUrl('test-survey-123'))}
          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
        >
          Test Survey Navigation
        </button>
      </div>
      
      <SurveysSection
        title="On-Going"
        surveys={ongoingSurveys}
        statsMap={statsMap}
        loading={loading && ongoingSurveys.length === 0}
        isOngoing={isOngoing}
        onViewDetails={handleViewDetails}
        emptyMessage="No ongoing surveys available."
      />
      
      <SurveysSection
        title="Upcoming"
        surveys={upcomingSurveys}
        statsMap={statsMap}
        loading={loading && upcomingSurveys.length === 0}
        isOngoing={isOngoing}
        onViewDetails={handleViewDetails}
        emptyMessage="No upcoming surveys available."
      />
    </div>
  );
}

export default SurveyList;
