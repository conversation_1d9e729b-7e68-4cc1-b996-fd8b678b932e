import ModalHeader from "pages/AdminApp/ModalHeader";
import React, { useEffect, useState } from "react";
import {
  Div,
  Text,
  Card,
  Button,
  Form,
  FormGroup,
  MultiSelect,
  Modal,
  Icon,
  FormControl,
} from "unmatched/components";
import { components } from "react-select";
import { ConfirmEmailSend } from "./ConfirmEmailSend";
import useSession from "unmatched/modules/session/hook";
import useToastr from "unmatched/modules/toastr/hook";
import { Note } from "./Note";
import {
  recipientsDescMap,
  templatesDescMap,
  userFriendlyRecipientsMap,
  userFriendlyTemplatesMap,
} from "../Emails";
import Tip from "./Tip";
import DndList from "./DndList";
import { searchUsersFact } from "pages/AdminApp/DataLoad/dataload-api";

const QuickSend = (props: any) => {
  const [template, setTemplate] = useState("REMINDER_INACTIVE");
  const [targetGroupArr, setTargetGroupArr] = useState<any>([
    { value: "INACTIVE_ALL", label: "INACTIVE_ALL" },
  ]);
  const [showModal, setShowModal] = React.useState(false);
  const [recipientsCountArr, setRecipientsCountArr] = React.useState<any>({});

  const toastr = useToastr();
  const { user } = useSession();
  const [loggedInUser, setLoggedInUser] = React.useState<any>({});

  useEffect(() => {
    if (user?.email) {
      searchUsersFact("", { search: user.email })
        .then((res) => {
          setLoggedInUser(res.data?.results[0]);
        })
        .catch((err) => console.log(err));
    }
  }, [user?.email]);

  useEffect(() => {
    const getCount = async (recipients: any) => {
      const countArray = await props.getRecipientsCountsArr(recipients);
      setRecipientsCountArr(countArray);
    };

    targetGroupArr?.[0]?.value !== "CUSTOM" && getCount(targetGroupArr);
  }, [targetGroupArr]);

  const onSelect = (users: any) => {
    props.setSelectedEmails(users);
  };

  const emailInputProps = {
    closeMenuOnSelect: false,
    isCustom: true,
    onInputChange: props.onInputChange,
    isMulti: true,
    placeholder: "",
    options: props.userOptions,
    CustomOption,
    styles: optionStyles,
    postfix: () =>
      props.selectedEmails.length > 0 ? (
        <span
          onClick={(e: any) => {
            e.stopPropagation();
            props.setSelectedEmails([]);
          }}
        >
          <Icon
            className="mr-2 fw-300 fs-12 cursor-pointer"
            icon="fas fa-times"
          />
        </span>
      ) : null,
    customValContainer: true,
  };

  const getRecipientOptions = (cTemplate: string): any => {
    switch (cTemplate) {
      case "REMINDER_ACTIVE":
      case "REMINDER_INACTIVE":
        return [
          ...Object.keys(targetGroups)
            .filter((k) => (targetGroups as any)[k] === cTemplate)
            .map((v: any) => ({
              value: v,
              label: v,
              disabled: !!targetGroupArr.find(
                (tg: any) => tg.value === "CUSTOM"
              ),
            })),
          {
            value: "CUSTOM",
            label: "CUSTOM",
            disabled: allRecepients
              .filter((r: any) => r !== "CUSTOM")
              .some((r: any) => {
                return !!targetGroupArr.find((tg: any) => tg.value === r);
              }),
          },
        ];
      case "SPECIAL":
      case "SURVEY_INVITATION":
        return allRecepients.map((v: any) => ({
          value: v,
          label: v,
          disabled:
            v !== "CUSTOM"
              ? !!targetGroupArr.find((tg: any) => tg.value === "CUSTOM")
              : allRecepients
                  .filter((r: any) => r !== "CUSTOM")
                  .some((r: any) => {
                    return !!targetGroupArr.find((tg: any) => tg.value === r);
                  }),
        }));
      default:
        return [{ value: "INACTIVE_ALL", label: "INACTIVE_ALL" }];
    }
  };

  return (
    <>
      <Card className="mb-4" noShadow>
        <Card.Header className="pl-3 py-2">
          <Text.H3>Quick Send</Text.H3>
        </Card.Header>
        <Div className="pl-3 py-2">
          <Text.P1 style={{ fontSize: 14 }}>
            Send reminder emails to a group of reviewers who are yet to complete
            their assigned surveys. Also send custom emails to individual
            reviewers related to this survey.
          </Text.P1>
          <Div className="pt-3 pr-4">
            <Form>
              <FormGroup>
                <Div style={{ width: 230 }}>
                  <FormGroup.Label className="pb-2">
                    Choose Template
                  </FormGroup.Label>
                  <MultiSelect
                    options={Object.values(targetGroups)
                      .filter(
                        (el: any) =>
                          !(
                            props.survey?.type === "SurveyIndexEngagement" &&
                            el === "SPECIAL"
                          )
                      )
                      .filter(
                        (v, i) => Object.values(targetGroups).indexOf(v) === i
                      )
                      .map((v: any) => ({
                        value: v,
                        label: (userFriendlyTemplatesMap as any)[v],
                      }))}
                    isMulti={false}
                    closeMenuOnSelect
                    value={{
                      value: template,
                      label: (userFriendlyTemplatesMap as any)[template],
                    }}
                    onSelect={async (selected: any) => {
                      setTemplate(selected.value);
                      const v =
                        selected.value === "REMINDER_INACTIVE"
                          ? "INACTIVE_ALL"
                          : "ACTIVE_INCOMPLETE";
                      setTargetGroupArr([{ value: v, label: v }]);
                    }}
                    CustomOption={TemplateOption}
                  />
                </Div>
              </FormGroup>
              {template !== "SPECIAL" && (
                <FormGroup className="d-flex">
                  <Div style={{ minWidth: 230 }}>
                    <Div className="d-flex">
                      <FormGroup.Label className="pb-2">
                        Recipients
                      </FormGroup.Label>{" "}
                      <Tip>
                        {allRecepients.map((r: any, i: number) => {
                          return (
                            <Text.P1 style={{ fontSize: 14 }} key={r}>
                              {i + 1}. {(recipientsDescMap as any)[r]}
                            </Text.P1>
                          );
                        })}
                      </Tip>
                    </Div>

                    <MultiSelect
                      options={getRecipientOptions(template)}
                      isMulti={true}
                      value={targetGroupArr.map((tg: any) => ({
                        ...tg,
                        label: (userFriendlyRecipientsMap as any)[tg.value],
                      }))}
                      onSelect={async (selected: any) => {
                        setTargetGroupArr(selected);
                      }}
                      CustomOption={RecipientOption}
                    />
                  </Div>

                  {!!targetGroupArr.find((tg: any) => tg.value === "CUSTOM") &&
                    targetGroupArr.length === 1 && (
                      <Div style={{ width: 700, marginLeft: 15 }}>
                        <FormGroup.Label className="pb-2">
                          Enter Email Address
                        </FormGroup.Label>
                        <MultiSelect
                          {...emailInputProps}
                          onSelect={onSelect}
                          hasError={false}
                          onBlur={props.onBlur}
                          wrap
                          placeholder="Search users by email or name"
                          value={props.selectedEmails}
                        />
                      </Div>
                    )}
                </FormGroup>
              )}
            </Form>
          </Div>
          {template !== "SPECIAL" && (
            <Note>
              <Text.P1 style={{ fontWeight: 500, fontSize: 14 }}>
                <b>Note :</b> All the{" "}
                <b>
                  {targetGroupArr
                    .map(
                      (tg: any) => (userFriendlyRecipientsMap as any)[tg.value]
                    )
                    .join(", ")}
                </b>{" "}
                raters of the survey will receive the{" "}
                <b>
                  {" "}
                  {(userFriendlyTemplatesMap as any)[template]} (
                  {(templatesDescMap as any)[template]})
                </b>{" "}
                email.
              </Text.P1>
            </Note>
          )}
        </Div>
        <Card.Footer className="mr-4">
          <Div className="d-flex align-items-center justify-content-between">
            <Div>
              <Text.P1
                className="fs-12 pl-3 cursor-pointer"
                style={{
                  color: "#518CFF",
                  textDecoration: "underline",
                  fontSize: 14,
                }}
                onClick={() => props.setShowSendCustomModal(true)}
              >
                Send a custom email to selective participants instead
              </Text.P1>
            </Div>
            <Div>
              <Button
                onClick={() => setShowModal(true)}
                variant="outline-primary"
              >
                Send Email
              </Button>
            </Div>
          </Div>
        </Card.Footer>
        <Modal show={showModal} size="lg" centered>
          <ModalHeader title="Send Email" onHide={() => setShowModal(false)} />
          <Modal.Body>
            <ConfirmEmailSend
              template={template}
              targetGroupArr={targetGroupArr}
              setShowModal={setShowModal}
              recipientsCountArr={recipientsCountArr}
              selectedEmails={props.selectedEmails}
              onSend={async () => {
                if (
                  targetGroupArr?.find((tg: any) => tg.value === "CUSTOM") &&
                  targetGroupArr?.length === 1
                ) {
                  props.sendCustomEmail(
                    template,
                    "CUSTOM",
                    props.selectedEmails.map((usr: any) => usr.id)
                  );
                } else {
                  const res = await Promise.all(
                    (template === "SPECIAL"
                      ? [{ value: "SPECIAL" }]
                      : targetGroupArr
                    ).map((tg: any) =>
                      props.sendEmail(template, tg.value, false)
                    )
                  ).catch((err: any) => console.log(err));
                  if (res) {
                    // show toast
                    toastr.onSucces({
                      title: "Success",
                      content: "Email sent Successfully",
                    });
                  }
                }
              }}
              onMockSend={() => {
                props.sendMockCustomEmail(template, [loggedInUser.id]);
              }}
            />
          </Modal.Body>
        </Modal>
      </Card>
      <DndList />
    </>
  );
};

export default QuickSend;

// Reusable small components. can be moved to new file
export const ROpt = (props: any) => {
  return (
    <FormGroup className="m-0">
      <FormControl.Checkbox
        className={props.data.disabled ? "disabled-opt" : ""}
      >
        <FormControl.Checkbox.Label className="f14">
          {(userFriendlyRecipientsMap as any)[props.value]}
        </FormControl.Checkbox.Label>
        <FormControl.Checkbox.Input
          checked={props.isSelected}
          onChange={() => null}
        />
      </FormControl.Checkbox>
    </FormGroup>
  );
};

export const RecipientOption = (props: any) => {
  return (
    <>
      {props.value === "CUSTOM" && <hr />}
      <div className="px-2 py-1">
        {!props.data?.disabled ? (
          <components.Option {...props} className="rounded pt-1 pb-2">
            <ROpt {...props} />
          </components.Option>
        ) : (
          <div className="rounded pt-1 pb-2 pl-2">
            <ROpt {...props} />
          </div>
        )}
      </div>
    </>
  );
};

export const TemplateOption = (props: any) => {
  return (
    <div>
      <components.Option {...props} className="rounded pt-1 pb-2">
        <FormGroup className="m-0 d-flex align-items-center">
          <Text.P1
            style={{ color: "#000", fontSize: 14 }}
            className="pt-1 cursor-pointer"
          >
            {(userFriendlyTemplatesMap as any)[props.value]}
          </Text.P1>
        </FormGroup>
      </components.Option>
    </div>
  );
};

export const optionStyles = {
  option: (styles: any, state: any) => {
    return {
      ...styles,
      backgroundColor: state.isSelected ? "#518cff" : "#fff",
      color: state.isSelected ? "#FFF" : "#000",
      cursor: state.isDisabled ? "not-allowed" : "default",
    };
  },
};

export const CustomOption = (props: any) => {
  return (
    <div className="px-2 py-1">
      <components.Option {...props} className="rounded pt-1 pb-2">
        <Div>
          {props.data.first_name} {props.data.last_name}
        </Div>
        <Div>{props.data.email}</Div>
      </components.Option>
      <hr className="my-0" />
    </div>
  );
};

export const targetGroups = {
  INACTIVE_ALL: "REMINDER_INACTIVE",
  ACTIVE_INCOMPLETE: "REMINDER_ACTIVE",
  ACTIVE_PARTIAL: "REMINDER_ACTIVE",
  ACTIVE_COMPLETE: "REMINDER_ACTIVE",
  SURVEY_INVITATION: "SURVEY_INVITATION",
  SPECIAL: "SPECIAL",
};

export const allRecepients = [
  "INACTIVE_ALL",
  "ACTIVE_INCOMPLETE",
  "ACTIVE_PARTIAL",
  "ACTIVE_COMPLETE",
  "CUSTOM",
];
