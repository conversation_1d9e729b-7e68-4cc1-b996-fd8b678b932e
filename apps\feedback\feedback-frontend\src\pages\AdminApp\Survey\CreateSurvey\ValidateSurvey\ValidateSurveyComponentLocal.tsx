import { Layout, Modal, util, Text, Div, Icon, Button } from "@unmatchedoffl/ui-core";
import React from "react";
// import { Layout, Modal, Text, Div, Icon, Button } from "../../../components";

const ListItem = (props: any) => {
  const getVariant = () => {
    if (props.state === props.validation.success) {
      return {
        variant: "success",
        icon: "fas fa-check-circle text-success",
      };
    } else if (props.state === props.validation.error) {
      return {
        variant: "danger",
        icon: "fad fa-exclamation-circle text-danger",
      };
    }
    return {
      variant: "primary",
      icon: "fal fa-circle-notch text-primary fa-spin",
    };
  };

  const { icon, variant } = getVariant();

  return (
    <Layout.Flex className="my-4">
      <Div className="mr-2">
        <Icon className="fs-20" icon={icon}></Icon>
      </Div>
      <Div>
        <Text.H2 className={`text-${variant}`}>{props.header}</Text.H2>
        <Text.P1>{props.content}</Text.P1>
      </Div>
    </Layout.Flex>
  );
};

interface Props {
  show: boolean;
  toastr: any;
  survey: any;
  indexId: string;
  fieldsData: any;
  validation: any;
  onHide: Function;
  onValidateSurveyFact: Function;
  onSuccess: Function;
  publishDraftSurveyFact: Function;
  Header: any;
  sendInvite: any;
  launchSurveyFact: any;
}

const ValidateSurveyComponentLocal = (props: Props) => {
  const {
    toastr,
    onValidateSurveyFact,
    publishDraftSurveyFact,
    Header,
    fieldsData,
    validation,
    survey,
    onSuccess,
    sendInvite,
    launchSurveyFact
  } = props;
  const [stage, setStage] = React.useState(validation.loading);

  const [fields, setFields] = React.useState(util.lib.cloneDeep(fieldsData));

  const updateItemStage = ({
    properties,
    participants,
    questions,
    email,
  }: any) => {
    const copy = { ...fields };
    copy.properties.stage = properties || copy.properties.stage;
    copy.participants.stage = participants || copy.participants.stage;
    copy.questions.stage = questions || copy.questions.stage;
    copy.email.stage = email || copy.email.stage;
    setFields(copy);
  };

  const updateReason = ({
    properties,
    participants,
    questions,
    email,
  }: any) => {
    const copy = { ...fields };
    copy.properties.error.content = properties || copy.properties.error.content;
    copy.participants.error.content =
      participants || copy.participants.error.content;
    copy.questions.error.content = questions || copy.questions.error.content;
    copy.email.error.content = email || copy.email.error.content;
    setFields(copy);
  };

  const onValidate = () => {
    // setState(validation.)
    onValidateSurveyFact(props.indexId).then(
      (response: any) => {
        if (response.isSuccess) {
          setStage(validation.success);
          // console.log('success');
        } else {
          setStage(validation.error);
          updateReason({
            questions: response.questions.reason,
            properties: response.properties.reason,
            participants: response.participants.reason,
            email: response.email.reason,
          });
        }
        updateItemStage({
          questions: response.questions.isValid
            ? validation.success
            : validation.error,
          participants: response.participants.isValid
            ? validation.success
            : validation.error,
          properties: response.properties.isValid
            ? validation.success
            : validation.error,
          email: response.email.isValid ? validation.success : validation.error,
        });
      },
      (error: any) => {
        setStage(validation.error);
        toastr.onError(error);
      }
    );
  };

  const onSendSurvey = () => {
    survey.setSaving(true);
    return launchSurveyFact(survey.data.id).then(
      () => {
        survey.setSaving(false);
        onSuccess();
        toastr.onSucces({ content: "Survey launched sucessfully!" });
        if (sendInvite) {
            publishDraftSurveyFact(survey.data.id).then(
              () => {
                toastr.onSucces({ content: "Invite sent sucessfully!" });
              },
              (err: any) => {
                survey.setSaving(false);
                toastr.onError(err);
              }
            );
        }
      },
      (err: any) => {
        survey.setSaving(false);
        toastr.onError(err);
      }
    );
    // return publishDraftSurveyFact(survey.data.id).then(
    //   () => {
    //     survey.setSaving(false);
    //     onSuccess();
    //     // history.push(util.appUrls.admin.survey.default);
    //     toastr.onSucces({ content: "Survey launched sucessfully!" });
    //   },
    //   (err: any) => {
    //     survey.setSaving(false);
    //     toastr.onError(err);
    //   }
    // );
  };

  const onSkip = () => {
    props.onHide();
  };

  const list = util.lib.keys(fields);

  const getFieldTemplate = (_field: any) => {
    const { get } = util.lib;
    const current = get(fields, _field);
    const { header, content } = get(current, current.stage);
    return (
        <ListItem
          key={_field}
          state={current.stage}
          header={header}
          content={content}
          validation={validation}
        />
    );
  };

  React.useEffect(() => {
    if (props.show) {
      setFields(util.lib.cloneDeep(fieldsData));
      setStage(validation.loading);
      onValidate();
    }
  }, [props.show]);

  const getFooterTemplate = () => {
    if (stage === validation.loading) return null;
    if (stage === validation.error) {
      return (
        <Modal.Footer className="text-right">
          <Button onClick={onSkip}>Skip & Continue</Button>
        </Modal.Footer>
      );
    }
    return (
      <Modal.Footer className="text-right">
        <Button onClick={onSendSurvey}>Launch</Button>
      </Modal.Footer>
    );
  };

  return (
    <>
      <Modal show={props.show} size="lg" onHide={() => props.onHide()} centered>
        <Header title="Publish Survey" onHide={() => props.onHide()} />
        <Modal.Body>
          <Div className="p-5 m-5">{list.map(getFieldTemplate)}</Div>
        </Modal.Body>
        {getFooterTemplate()}
      </Modal>
    </>
  );
};

ValidateSurveyComponentLocal.propTypes = {};

export default ValidateSurveyComponentLocal;
