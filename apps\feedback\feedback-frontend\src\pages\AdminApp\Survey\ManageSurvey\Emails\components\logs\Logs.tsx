import {
  VerticalTimeline,
  VerticalTimelineElement,
} from "react-vertical-timeline-component";
import "react-vertical-timeline-component/style.min.css";
import "./timelineStyles.css";

import { useEffect, useState } from "react";
import { Div, Layout, Modal } from "unmatched/components";
import ModalHeader from "pages/AdminApp/ModalHeader";
import ParticipantsList from "./participants/ParticipantsList";
import { getEmailLogs } from "../../../../survey-api";
import { useScrollAtBottom } from "unmatched/hooks/useScrollAtBottom";
import { util } from "@unmatchedoffl/ui-core";

const Logs = (props: any) => {
  const [showParticipantsModal, setShowParticipantsModal] = useState(false);
  const scrollAtBottom = useScrollAtBottom();
  const [emailLogs, setEmailLogs] = useState<any>(undefined);
  const [logParams, setLogParams] = useState<any>({
    page: 1,
    page_size: 10,
  });
  useEffect(() => {
    if (scrollAtBottom && emailLogs?.count_pages >= logParams.page) {
      getSurveyEmailLogs(logParams);
    }
  }, [scrollAtBottom]);

  useEffect(() => {
    getSurveyEmailLogs(logParams);
  }, []);

  const getSurveyEmailLogs = async (params: any) => {
    const res = await getEmailLogs(
      props.indexId,
      params,
      props.dedicatedTemplates
    );
    if ((res as any).data) {
      setEmailLogs(
        emailLogs
          ? {
              ...emailLogs,
              results: [...emailLogs.results, ...(res as any).data.results],
            }
          : (res as any).data
      );
      const newLogParams = { ...logParams, page: logParams.page + 1 };
      setLogParams(newLogParams);
    }
  };

  const getUsersLink = (log: any) => {
    return (
      <span
        style={{ cursor: "pointer" }}
        onClick={() => {
          setShowParticipantsModal(log?.id);
        }}
        className="unm-link"
      >
        {log?.recepient_count}
      </span>
    );
  };

  return (
    <>
      <Layout.Container fluid>
        <VerticalTimeline animate={false} lineColor="#F2F2F2">
          {emailLogs?.results?.map((log: any) => {
            return (
              <VerticalTimelineElement
                key={log?.id}
                className="vertical-timeline-element--work"
                date={
                  <Div>
                    <Div className="unm-date">
                      {util.date.getFormatedTime(
                        new Date(log?.created_at),
                        "MMMM dd yyyy"
                      )}
                    </Div>
                    <Div className="unm-time">
                      {util.date.getFormatedTime(
                        new Date(log?.created_at),
                        "hh:mm:ss"
                      )}
                      hrs
                    </Div>
                  </Div>
                }
                position="right"
                iconStyle={{
                  background: "#F2F2F2",
                  color: "#fff",
                  width: 10,
                  height: 10,
                  marginTop: 25,
                  marginLeft: -5,
                }}
              >
                <Div>
                  {log?.initiator?.model_name === "NudgeSetting"
                    ? getLogMsg(
                        `${log?.email_type}_NUDGE`,
                        log?.initiator?.created_by,
                        getUsersLink(log)
                      )
                    : getLogMsg(
                        log?.email_type,
                        log?.initiator?.created_by,
                        getUsersLink(log)
                      )}
                </Div>
              </VerticalTimelineElement>
            );
          })}
        </VerticalTimeline>
      </Layout.Container>
      <Modal show={showParticipantsModal} size="xl" centered>
        <ModalHeader
          title="Participants"
          onHide={() => setShowParticipantsModal(false)}
        />
        <Modal.Body>
          <ParticipantsList logID={showParticipantsModal} />
        </Modal.Body>
      </Modal>
    </>
  );
};

const getLogMsg = (type: string, name: string, count: any) =>
  ({
    REMINDER_INACTIVE: (
      <>
        <span>{`${name} sent reminder to`}</span> {count} inactive users who are
        yet to visit survey.
      </>
    ),
    REMINDER_ACTIVE: (
      <>
        <span>{`${name} sent reminder to`}</span> {count} users who have
        outstanding reviews to complete.
      </>
    ),
    SPECIAL: (
      <>
        <span>{`${name} sent special reminder to`}</span> {count} users.
      </>
    ),
    CUSTOM: (
      <>
        <span>{`${name} sent custom reminder to`}</span> {count} users.
      </>
    ),
    SURVEY_INVITATION: (
      <>
        <span>{`${name} sent survey invitation to `}</span> {count} users.
      </>
    ),
    REMINDER_INACTIVE_NUDGE: (
      <>
        <span>Sent nudge reminder to</span> {count} inactive users who are yet
        to visit survey.
      </>
    ),
    REMINDER_ACTIVE_NUDGE: (
      <>
        <span>Sent nudge reminder to</span> {count} users who have outstanding
        reviews to complete.
      </>
    ),
    SPECIAL_NUDGE: (
      <>
        <span>Sent special nudge reminder to</span> {count} users.
      </>
    ),
    CUSTOM_NUDGE: (
      <>
        <span>Sent custom nudge reminder to</span> {count} users.
      </>
    ),
    ENCRYPTED_REPORT: (<>
      <span>{`${name} has sent reports to`} {count} reviewees (Clicking on {count}, show the list of {count} reviewees).</span>
    </>),
    REPORT_ACCESS_KEY: (<>
      <span>{`${name} has sent access keys to`} {count} reviewees (Clicking on {count}, show the list of {count} reviewees).</span>
    </>)
  }[type]);

export default Logs;
