import React, { useState } from "react";
import { useHistory } from "react-router-dom";
import { Div, FormControl, Icon, Layout } from "unmatched/components";
import { useXHR } from "unmatched/hooks";
import util from "unmatched/utils";
import { getUsersFact } from "unmatched/modules/session/api";
// import PropTypes from 'prop-types';

const Suggestions = (): any => {
  const history = useHistory();
  const usersData = useXHR({ defaultResponse: [] }, "users");
  const [search, setSearch] = useState("");

  const getUsers = () => {
    if (!search) return;
    usersData.setLoading(true);
    getUsersFact(search).then((res: any) => {
      usersData.onSuccess(res);
    });
  };

  const getLoadingTemplate = () => {
    return (
      <>
        <Layout.Flex>
          <Div>
            <Icon icon="fas fas-spinner" spin></Icon>
          </Div>
          <Div>Loading ...</Div>
        </Layout.Flex>
      </>
    );
  };

  return (
    <div>
      <FormControl.SearchWithSuggestion
        value={search}
        delay={500}
        setValue={setSearch}
        onSearch={getUsers}
        placeholder="Search with name, email or employee ID "
      >
        <Div>
          {usersData.isLoading
            ? getLoadingTemplate()
            : usersData.users.map((item: any) => {
                return (
                  // variant="link px-0 py-1"
                  // className="text-left text-dark item-hover"
                  // block
                  // preventDefault
                  // stopPropagation
                  <FormControl.SearchWithSuggestion.Item
                    key={item.id}
                    onClick={(e: any) => {
                      util.preventAndStopEvent(e);
                      history.push(
                        util.appUrls.admin.analytics.people.getAnalyticsUrl(
                          item.id
                        )
                      );
                    }}
                  >
                    {item.name}
                  </FormControl.SearchWithSuggestion.Item>
                );
              })}
        </Div>
      </FormControl.SearchWithSuggestion>
    </div>
  );
};

// Suggestions.propTypes = {

// }

export default Suggestions;
