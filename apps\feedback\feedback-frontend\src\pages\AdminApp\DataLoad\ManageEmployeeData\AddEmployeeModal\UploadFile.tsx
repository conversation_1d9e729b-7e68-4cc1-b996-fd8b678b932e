import React from "react";
import {
  Text,
  Layout,
  Div,
  But<PERSON>,
  CustomModal as Modal,
} from "unmatched/components";
import data from "assets/images/images";
import icons from "assets/icons/icons";
import Loader from "assets/images/Loader";
import { useDropzone } from "react-dropzone";
import {
  // Modal,,
  FileCard,
  CancelButton,
} from "../../CreateDataLoad/CreateDataLoad.style";
import { employeeFileUploadFact } from "../../dataload-api";

const activeStyle = {
  borderColor: "#2196f3",
};
const rejectStyle = {
  borderColor: "#2196f3",
};

const uploadBox = {
  width: "100%",
  minHeight: "40vh",
  // border: "1px dashed #494949",
  borderColor: "rgba(0,0,0,0.1)",
  borderStyle: "dashed",
  padding: "5% 5%",
  fontSize: "14px !important",
};
const isActiveDiv = {
  background: "rgba(0,0,0,0.1)",
};

const stylee = {
  uploadText: {
    textDecoration: "underline",
    cursor: "pointer",
  },
};

const Uploadfile = (props: any) => {
  const { setScreen } = props;
  const [Stage, setStage] = React.useState(0);
  const [File, setFile] = React.useState<any>([]);
  React.useEffect(() => {
    setFile([]);
  }, []);
  const {
    // acceptedFiles,
    // fileRejections,
    getRootProps,
    getInputProps,
    isDragActive,
    isDragAccept,
    isDragReject,
  } = useDropzone({
    accept: ".xlsx, .xls, .csv",
    maxFiles: 1,
    onDrop: (acceptedFiles) => {
      setFile(
        acceptedFiles.map((file) =>
          Object.assign(file, {
            preview: URL.createObjectURL(file),
          })
        )
      );
      if (
        acceptedFiles.map((file) =>
          Object.assign(file, {
            preview: URL.createObjectURL(file),
          })
        ).length > 0
      ) {
        setStage(1);
        tryUpload(
          acceptedFiles.map((file) =>
            Object.assign(file, {
              preview: URL.createObjectURL(file),
            })
          )
        );
      }
    },
  });

  const tryUpload = async (File: any) => {
    try {
      const extArray = File[0].name.split(".");
      const ext = extArray[extArray.length - 1];
      console.log(ext);
      await employeeFileUploadFact({ File: File[0], Format: ext });
      setStage(2);
      // upLoad;
    } catch (err) {
      console.log(err);
    }
  };

  const styledBox = React.useMemo(
    () => ({
      ...uploadBox,
      ...(isDragActive ? activeStyle : {}),
      ...(isDragAccept ? isActiveDiv : {}),
      ...(isDragReject ? rejectStyle : {}),
    }),
    [isDragActive, isDragReject, isDragAccept]
  );

  const viewStage = (screen: number) => {
    switch (screen) {
      case 0:
        return (
          <Upload
            getRootProps={getRootProps}
            getInputProps={getInputProps}
            styledBox={styledBox}
          />
        );
      case 1:
        return <OnUploadStart setStage={setStage} file={File[0]} />;

      case 2:
        return <OnUploadFinish setStage={setStage} file={File[0]} />;

      default:
        break;
    }
  };
  return (
    <>
      <Modal.Body>
        <Text.P1 className="pb-4">
          To add more employees to this record, please upload an excel file with
          the new employees or the previous file with the updated list of
          employees. The required fileds should be the same that was already
          used to upload the employee data.
        </Text.P1>
        {viewStage(Stage)}
      </Modal.Body>
      <Modal.Footer>
        {Stage === 2 ? (
          <Div className="w-100">
            <Layout.Row>
              <Layout.Col></Layout.Col>
              <Layout.Col>
                <Button
                  className="float-right"
                  type="button"
                  variant="primary"
                  size="lg"
                  onClick={() => {
                    setScreen(2);
                  }}
                >
                  Save and Next
                </Button>
              </Layout.Col>
            </Layout.Row>
          </Div>
        ) : (
          ""
        )}
      </Modal.Footer>
    </>
  );
};
const Upload = (props: any) => {
  const { PAIRICON } = data;
  const { getRootProps, getInputProps, styledBox } = props;
  return (
    <>
      <section className="container">
        <div
          {...getRootProps({
            style: styledBox,
            className:
              "d-flex justify-content-center align-items-center flex-column text-center",
          })}
        >
          <input {...getInputProps()} />
          <input type="file" id="uploader" style={{ display: "none" }} />
          <Text.H3 className="pb-2 mb-3 f10">
            Simply drag & drop the file here to Upload or{" "}
            <label
              htmlFor="uploader"
              style={stylee.uploadText}
              className="text-primary"
            >
              browse & choose your file
            </label>
          </Text.H3>
          <img src={PAIRICON} alt="" />
        </div>
      </section>
    </>
  );
};
const OnUploadStart = (props: any) => {
  const { setStage, file } = props;
  React.useEffect(() => {
    setTimeout(() => {
      setStage(2);
    }, 3000);
  }, [setStage]);
  return (
    <>
      <div
        style={uploadBox}
        className="d-flex justify-content-center align-items-center flex-column text-center"
      >
        <Loader size={105} />
        <Text.H2 className="pb-2 text-primary">
          Uploading Your File, Please wait.
        </Text.H2>
        <Text.P1 className="pb-0">{file?.name}</Text.P1>
      </div>
    </>
  );
};

const OnUploadFinish = (props: any) => {
  const { file, setStage } = props;
  const { SuccessTick } = icons;
  return (
    <>
      <Text.H2 className="pb-2">1 File uploaded</Text.H2>
      <FileCard className="f-12">
        <Layout.Row className="justify-content-center align-item-center">
          <Layout.Col className="col-1">
            <SuccessTick />
          </Layout.Col>
          <Layout.Col className="col-8">{file?.name}</Layout.Col>
          <Layout.Col className="col-3">
            <CancelButton onClick={() => setStage(0)}>Remove</CancelButton>
          </Layout.Col>
        </Layout.Row>
      </FileCard>
    </>
  );
};

export default Uploadfile;
