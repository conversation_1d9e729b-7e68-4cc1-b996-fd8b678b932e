import React, { ReactNode } from "react";
import { Text, Icon, Div } from "unmatched/components";

const Loader = (props: {
  hideContent?: boolean;
  title?: string;
  content?: string;
  icon?: string;
  iconTemplate?: ReactNode;
  image?: ReactNode;
}) => {
  const { iconTemplate, hideContent } = props;
  const getIcon = () => {
    return (
      iconTemplate || (
        <Icon
          icon={props.icon}
          spin
          className="fs-spin my-3 mb-4 fs-60"
          variant="primary"
        ></Icon>
      )
    );
  };
  return (
    <Div className="text-center py-5 my-5">
      <Div className="my-3 mb-4">{getIcon()}</Div>
      {!hideContent && (
        <>
          <Text.H2 className="text-primary mb-4">{props.title}</Text.H2>
          <Text.P1>{props.content}</Text.P1>
        </>
      )}
    </Div>
  );
};

Loader.defaultProps = {
  icon: "fal fa-circle-notch",
};

export default Loader;
