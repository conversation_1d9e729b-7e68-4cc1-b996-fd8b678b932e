import { useState, useEffect } from "react";
import { getClientInfoService, updateClientInfoService, ContactInfo } from "./textTemplatesService";
import { toast } from "sonner";
import useSession from "../../../../unmatched/modules/session/hook";

export const useTextTemplates = () => {
  const [loading, setLoading] = useState(false);
  const [loginText, setLoginText] = useState("");
  const [contactText, setContactText] = useState("");
  const [contacts, setContacts] = useState<ContactInfo[]>([]);
  const [reportContacts, setReportContacts] = useState<ContactInfo[]>([]);
  const [editState, setEditState] = useState<"LOGIN_TEXT" | "CONTACT_TEXT" | "REPORT_CONTACT" | "">("");
  const { client, getClientInfo } = useSession();

  // Load initial data
  useEffect(() => {
    fetchClientInfo();
  }, []);

  // Update from session when client changes
  useEffect(() => {
    if (client) {
      setLoginText(client.loginText || "");
      setContactText(client.contactText || "");

      // Safely handle contacts array
      const clientContacts = Array.isArray(client.contacts) ? client.contacts : [];
      setContacts(clientContacts);

      // Initialize with at least one empty contact if none exists
      if (clientContacts.length === 0) {
        setContacts([createEmptyContact()]);
      }

      // Initialize report contacts (this would need to be updated if there's a separate API for report contacts)
      if (reportContacts.length === 0) {
        setReportContacts([createEmptyContact()]);
      }
    }
  }, [client, reportContacts.length]);

  const fetchClientInfo = async () => {
    try {
      setLoading(true);
      const data = await getClientInfoService();
      setLoginText(data.loginText || "");
      setContactText(data.contactText || "");

      // Ensure we always have at least one contact
      if (data.contacts && data.contacts.length > 0) {
        setContacts(data.contacts);
      } else {
        setContacts([createEmptyContact()]);
      }

      setLoading(false);
    } catch (error) {
      console.error("Error fetching client info:", error);
      setLoading(false);
      toast.error("Failed to load text templates");
    }
  };

  const createEmptyContact = (): ContactInfo => ({
    id: Math.random().toString(),
    fullName: "",
    email: "",
    phone: "",
    title: "",
    location: "",
    branch: ""
  });

  const handleSaveLoginText = async () => {
    try {
      setLoading(true);
      await updateClientInfoService({
        loginText
      });
      setEditState("");
      setLoading(false);
      toast.success("Login text saved successfully");
      getClientInfo(); // Refresh client info in session
    } catch (error) {
      console.error("Error saving login text:", error);
      setLoading(false);
      toast.error("Failed to save login text");
    }
  };

  const handleSaveContactText = async () => {
    try {
      setLoading(true);
      await updateClientInfoService({
        contactText,
        contacts
      });
      setEditState("");
      setLoading(false);
      toast.success("Contact information saved successfully");
      getClientInfo(); // Refresh client info in session
    } catch (error) {
      console.error("Error saving contact text:", error);
      setLoading(false);
      toast.error("Failed to save contact information");
    }
  };

  const handleSaveReportContacts = async () => {
    try {
      setLoading(true);
      // This would need to be updated if there's a separate API for report contacts
      // For now, we're just simulating the save
      setEditState("");
      setLoading(false);
      toast.success("Report contacts saved successfully");
    } catch (error) {
      console.error("Error saving report contacts:", error);
      setLoading(false);
      toast.error("Failed to save report contacts");
    }
  };

  const addContact = () => {
    setContacts([...contacts, createEmptyContact()]);
  };

  const removeContact = (id: string) => {
    setContacts(contacts.filter(contact => contact.id !== id));
  };

  const updateContact = (id: string, field: keyof ContactInfo, value: string) => {
    setContacts(contacts.map(contact =>
      contact.id === id ? { ...contact, [field]: value } : contact
    ));
  };

  const addReportContact = () => {
    setReportContacts([...reportContacts, createEmptyContact()]);
  };

  const removeReportContact = (id: string) => {
    setReportContacts(reportContacts.filter(contact => contact.id !== id));
  };

  const updateReportContact = (id: string, field: keyof ContactInfo, value: string) => {
    setReportContacts(reportContacts.map(contact =>
      contact.id === id ? { ...contact, [field]: value } : contact
    ));
  };

  return {
    loading,
    loginText,
    setLoginText,
    contactText,
    setContactText,
    contacts,
    reportContacts,
    editState,
    setEditState,
    handleSaveLoginText,
    handleSaveContactText,
    handleSaveReportContacts,
    addContact,
    removeContact,
    updateContact,
    addReportContact,
    removeReportContact,
    updateReportContact
  };
};
