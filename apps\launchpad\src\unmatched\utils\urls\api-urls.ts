// import { util } from "@unmatchedoffl/ui-core";

// export const BASE_URL = util.env.apiUrl;

export const BASE_URL = import.meta.env.VITE_API_URL;

const API_URLS = {
  LOGIN: "/auth/login/",
  LOGOUT: "/auth/logout/",
  SESSION: "/auth/user/",
  REQ_ACTIVATION: "/auth/activation/request/",
  REQ_MAGIC_LINK: "/auth/magiclink/request/",
  PERFORM_MAGIC_LINK_LOGIN: "/auth/magiclink/perform/",
  SET_ACTIVATION: "/auth/activation/perform/",
  REQ_RESET: "/auth/password/request/",
  SET_RESET: "/auth/password/reset/",
  SET_PASSWORD: "/auth/password/set/",
  CHANGE_PASSWORD: "/auth/password/change/",
  VALIDATE_EMAIL: (email: string) => `/auth/user/${email}/`,
  GET_360_REPORT_LISTS: "/survey/admin/group/ENDED/",

  ADMIN_SURVEYS: "/survey/admin/index/",
  ADMIN_SURVEY_VERSIONS: "survey/admin/survey/",
  ADMIN_SURVEY_SECTIONS: "survey/admin/section/",
  ADMIN_SURVEY_QUESTIONS: "survey/admin/components/",
  ADMIN_SURVEY_QUESTION: "survey/admin/component/",
  ADMIN_QUESTIONS: "/survey/admin/index/",
  ADMIN_SELF_CLONE: "/survey/auxillary/",
  ADMIN_360_REPORT_INFO: (id: string) => `/survey/admin/group/${id}`,

  //ANALYTICS
  ENGAGEMENT_URL: "analytics/engagement/",
  ENGAGEMENT_COMMENT: "/analytics/engagement/comments",
  ENGAGEMENT_OVERALL: "report/engagement/norm/",
  PEOPLE_ANALYTICS: "/analytics/people/",
  ENGAGEMENT_DEMOGRAPHICS: "/analytics/engagement/demographics",
  DEMOGRAPHICS_SCHEMA: "/survey/demographics-schema/",
  COMPARATIVE_ANALYSIS: "/analytics/engagement/comparative_analysis",

  //REPORT APIS
  GET_FIRM_REPORTS: "/staff/files/firmreports/",
  GET_UPWARD_REPORTS: "/staff/files/upwardreports/",
  GET_360_REPORTS: "/staff/files/360reports/",
  REQUEST_UPWARD_REGEN_REPORT: "/report/upward/regen/",
  REQUEST_FIRM_REGEN_REPORT: "/report/engagement/regen/",
  REQUEST_360_REGEN_REPORT: "/report/360/regen/",
  GET_ENGAGEMENT_DATADUMP: "/analytics/engagement/datadump/",
  GET_UPWARD_DATADUMP: "/analytics/upward/datadump",

  //MANAGE SURVEY URLS
  REVIEWER_STATS: "/stats/statistics-core/reviewer",
  REVIEWEE_STATS: "/stats/statistics-core/reviewee",
  SURVEY_STATS: "/stats/statistics-core/survey",
  PARTICIPATION_STATS: "/stats/statistics-core/participation",
  REPORT_STATS: "/stats/statistics-core/report",
  META_LABELS: "/staff/customers/metadata-labels/",
  GET_GRAPH_DATA: "/stats/statistics-core/submission",

  //DATALOAD URLS
  GET_ALL_USER_FILES: "/staff/files/users/",
  GET_ALL_PAIR_FILES: "/staff/files/pairs/",
  GET_ALL_ASSOCIATE_FILES: "/staff/files/ongoing/",
  SAMPLE_EMPLOYEE_DOWNLOAD: "staff/files/load/users/example/",
  DOWNLOAD_ALL_EMPLOYEES: "/auth/users/?f=xlsx",
  SAMPLE_PAIRING_DOWNLOAD: "/staff/files/load/pairings/example/",
  EMPLOYEE_UPLOAD: "/staff/files/load/users/",
  PATCH_USER_UPLOAD: (id: string) => `/staff/files/users/${id}/`,
  PAIRING_UPLOAD: "/staff/files/load/pairings/",
  PAIRINGS_DOWNLOAD: "/survey/admin/pairings/download/",
  PATCH_PAIRING_UPLOAD: (id: string) => `/staff/files/pairs/${id}/`,
  GET_ALL_PAIRINGS_FROM_FILE: "/staff/files/abstract-pairings/",
  GET_ALL_USERS_FROM_FILE: "/staff/files/abstract-users/",
  GET_ADMIN_PAIRINGS: "/survey/admin/pairings/",
  ADD_BULK_PAIRING: "/survey/admin/pairings/bulk_create/",
  GET_USERS: "/auth/users/",
  VALIDATE_PAIRING: "/survey/admin/pairings/validate/",
  FETCH_PAIRING_FILE_INFO: (id: string) => `/staff/files/pairs/${id}/`,
  FETCH_USER_FILE_INFO: (id: string) => `/staff/files/users/${id}/`,
  GET_REQUIRED_USER_FIELDS: "staff/files/load/users/",
  GET_REQUIRED_PAIR_FIELDS: "staff/files/load/pairings/",
  GET_ALL_COMMENT_QUESTION: "/survey/admin/comment-review/summary/",
  GET_ALL_COMMENTS: "/survey/admin/comment-review/",
  COMMENT_OPERATION: (id: string) => `/survey/admin/comment-review/${id}`,
  BOOKMARK_OPERATION: "/survey/bookmark/comments/",

  //DASHBOARD URLS
  GET_ALL_SURVEYS: "/survey/index",

  //ORG SETTINGS URLS
  FAQ: "/staff/customers/faq/",
  FAQ_OPERATION: (id: string) => `/staff/customers/faq/${id}`,
  POINT_OF_CONTACTS: "/staff/customers/contact-us/",
  //USER UPWARD URLS
  GET_INDIVIDUAL_SURVEY_INFO: (id: string) => `/survey/index/${id}/`,
  GET_UPWARD_PAIRS: "/survey/pairing/",
  GET_SET_RESPONSE: "/survey/survey-response/",
  GET_ALL_USERS_IN_SURVEY: "/survey/pairing/add/",
  SET_ADD_PAIRING: "/survey/pairing/",

  //EMPLOYEE
  ADD_EMPLOYEE: "staff/customers/employees/",
};

export default API_URLS;
