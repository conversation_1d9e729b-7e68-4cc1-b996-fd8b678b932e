import { Navigate, Route, Routes } from "react-router";
import authRoutes from "./pages/Auth/Auth.route";
import publicRoutes from "./pages/Public/Public.route";
// import settingsRoutes from "./pages/Dashboard/settings/settings.route";
// ROUTES is used in commented code below for future implementation
// import { ROUTES } from "./routes";
import DashboardRoute from "./pages/Dashboard/Dashbaoard.route";
import NotFound from "./pages/NotFound";
import Logout from "./pages/Logout";
import ROUTES from "./routes";
import useSession from "@/unmatched/modules/session/hook";
import { FileDetails } from "./pages/Dashboard/settings/files/FileDetails";
import DashboardLayout from "./pages/Dashboard/Dashboard.layout";

// Combine all routes
const _Routes = [...DashboardRoute, ...authRoutes, ...publicRoutes];

function AppRoutes() {
  const { isLoggedIn } = useSession();
  return (
    <>
      {/* {JSON.stringify(_Routes)}
      {_Routes.map((route) => (
        <>
        {JSON.stringify(route)}
          {route.children &&
            route.children.map((child: any) => (
              <>
                <p>-{child.path}</p>
                <p>==={JSON.stringify(child.child)}</p>
                {child.children?.children &&
                  child.children.children.map((grandChild: any) => (
                    <p>{grandChild.path}</p>
                  ))}
              </>
            ))}
        </>
      ))} */}
      <Routes>
        {/* Map through all route configurations */}

        {_Routes.map((route) => (
          <Route key={route.path} path={route.path} element={<route.layout />}>
            {/* Map through children routes */}
            {route.children &&
              route.children.map((child: any) => (
                <>
                  <Route key={route.path} path={"*"} element={<></>}></Route>

                  {/* {child?.children?.children && (
                    <Route
                      key={route.path}
                      path={route.path}
                      element={<h1>I am </h1>}
                    >
                      {child.children.map((grandChild: any) => (
                        <Route
                          key={grandChild.path}
                          path={grandChild.path}
                          element={<grandChild.component />}
                        />
                      ))}
                    </Route>
                  )} */}
                  {/* {
                    <Route
                      key={child.path}
                      path={child.path}
                      element={
                        <>ss</>
                        // child.protected && !isLoggedIn() ? (
                        //   <Navigate to={ROUTES.AUTH.LOGIN} />
                        // ) : (
                        //   <child.component />
                      }
                    />
                  } */}
                  {/* {child.children &&
                    child.children.map((grandChild: any) => (

                    ))} */}
                  <Route
                    key={child.path}
                    path={child.path}
                    element={
                      child.protected && !isLoggedIn() ? (
                        <Navigate to={ROUTES.AUTH.LOGIN} />
                      ) : (
                        <child.component />
                      )
                    }
                  />
                </>
              ))}
          </Route>
        ))}

        {/* Add any additional routes that don't fit the pattern */}
        {/* <Route path={ROUTES.HELP.ROOT} element={<HelpLayout />}>
        <Route path={ROUTES.HELP.FAQ.split('/').pop()} element={<FAQ />} />
        <Route path={ROUTES.HELP.DOCUMENTATION.split('/').pop()} element={<Documentation />} />
        <Route path={ROUTES.HELP.CONTACT.split('/').pop()} element={<Contact />} />
      </Route> */}

        {/* Direct route for file details */}
        <Route path="/settings/files/:id" element={<DashboardLayout />}>
          <Route index element={<FileDetails />} />
        </Route>

        {/* Redirect to dashboard for unknown routes */}
        <Route path={ROUTES.LOGOUT} element={<Logout />} />
        <Route path="*" element={<NotFound />} />
      </Routes>
    </>
  );
}

export default AppRoutes;
