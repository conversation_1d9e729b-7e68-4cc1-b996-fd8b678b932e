import axios, { AxiosResponse } from "axios";
import util from "unmatched/utils";

export const allParticipatsFact = (params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return axios
    .get(`${util.apiUrls.EXIT_PARTIPANT}`, config)
    .then(({ data }: AxiosResponse) => {
      if (!data) return { data: [], totalPages: 0 };
      const { results, count_pages, count_items } = data || {};
      if (!results) return { data: [], totalPages: 0 };
      return {
        data, 
        //: results.map((item: any) => {
        //   return {
        //     possibleRaters: item.possible_raters,
        //     actualRaters: item.actual_raters,
        //     inProgressRaters: item.in_progress_raters,
        //     participationPercent: item.participation_percent,
        //     group: item.group,
        //   };
        // }),
        totalPages: count_pages,
        totalElements: count_items,
      };
    });
};
