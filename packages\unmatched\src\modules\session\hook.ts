// import util from "../../utils";
import _session from "../../utils/session";
import { Session, User } from "../../types";
import {
  useDispatch,
  // useHistory,
  useSelector,
  useState,
} from "../../hooks";
import { getSessionFact, getClientInfoFact, patchClientInfoFact } from "./api";
import actions from "./slice";
import util from "@/unmatched/utils";
import { useNavigate } from "react-router";

export default function useSession() {
  const [sessionLoaded, setSessionLoaded] = useState(false);
  const session: Session = useSelector((state: any) => state.session);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  // const history = useHistory();

  const fetchSession = async (params?: any) => {
    dispatch(actions.setLoading(true));
    if (isLoggedIn()) {
      const { onUnAuthorize, onSuccess, onError } = params || {};
      try {
        const response = await getSessionFact();
        // Safely call Beacon if it exists
        const beacon = (window as any).Beacon;
        if (typeof beacon === 'function') {
          beacon("identify", {
            name: response.firstName + " " + response.lastName,
            email: response.email,
          });
          beacon("config", { 
            docsEnabled: true,
          });
        }
        dispatch(actions.setUser(response));
        getClientInfo();
        if (onSuccess) onSuccess(response);
      } catch (error: any) {
        dispatch(actions.setLoading(false));

        // console.log(error.status)
        if (error.status === 401) {
          _session.onUnAuthorize();
          if (onUnAuthorize) {
            onUnAuthorize(error);
            const beacon = (window as any).Beacon;
            if (typeof beacon === 'function') {
              beacon("logout");
            }
            beacon("config", {
              docsEnabled: false,
            });
            beacon('navigate', '/ask/chat/')
          }
        } else {
          dispatch(actions.setError(error));
        }
        if (onError) onError(error);
      }
    } else {
      getClientInfo();
      // const beacon = (window as any).Beacon;
      // if (typeof beacon === 'function') {
      //   beacon("logout");
      // }
      // beacon("config", {
      //   docsEnabled: false,
      // });
      // beacon('navigate', '/ask/chat/')
      dispatch(actions.reset());
    }
  };

  const getClientInfo = () => {
    getClientInfoFact().then(
      (data: any) => {
        setSessionLoaded(true);
        dispatch(actions.setLoading(false));
        dispatch(actions.setClient(data));
      },
      () => {
        dispatch(actions.setLoading(false));
      }
    );
  };

  const patchClientInfo = (data: any) => {
    return patchClientInfoFact(data).then(
      (data: any) => {
        dispatch(actions.setLoading(false));
        setSessionLoaded(true);
        dispatch(actions.setClient(data));
      },
      () => {
        dispatch(actions.setLoading(false));
      }
    );
  };

  const login = (payload: {
    token: string;
    expiry: string;
    user: User;
    redirectUrl?: string | null;
    app?: string | null;
    redirectToFeedback?: boolean;
    page?: string;
    oidc?: boolean;
  }) => {
    const { oidc, token, user, expiry, redirectUrl, app, redirectToFeedback, page } =
      payload;
    _session.login(token, expiry);
    dispatch(actions.setToken(token));
    dispatch(actions.setExpiry(expiry));
    dispatch(actions.setUser(user));
    const beacon = (window as any).Beacon;
    if (typeof beacon === 'function') {
      beacon("identify", {
        name: user.firstName + " " + user.lastName,
        email: user.email,
      });
      beacon("config", { 
        docsEnabled: true,
      });
    }
    if (app) {
      window.location.href = `${window.location.origin}/${app}/`;
      return;
    }

    if (redirectUrl) {
      // only magic link rediret
      // history.push(redirectUrl);
      window.location.href = `${window.location.origin}/${app}/#${redirectUrl}`;
      return;
    }

    if (redirectToFeedback) {
      window.location.href = `${window.location.origin}${page}/`;
      return;
    }
    if (oidc) {
      window.location.href = `/`
    } 
    navigate('/');
    // window.location.href = `${window.location.origin}/launcher/`;
    
  };

  const resetSession = () => {
    dispatch(actions.reset());
    _session.logout();
  };

  const logout = () => {
    // history.push();
    window.location.href = `${util.appUrls.logout}`;
  };

  const isLoggedIn = () => !!_session.getToken();

  return {
    ...session,
    login,
    logout,
    fetchSession,
    resetSession,
    isLoggedIn,
    sessionLoaded,
    getClientInfo,
    patchClientInfo,
  };
}
