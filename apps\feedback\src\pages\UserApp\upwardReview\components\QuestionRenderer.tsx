import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@repo/ui/components/card';
import { Button } from '@repo/ui/components/button';
import { Textarea } from '@repo/ui/components/textarea';
import { Input } from '@repo/ui/components/input';
import { RadioGroup, RadioGroupItem } from '@repo/ui/components/radio-group';
import { Checkbox } from '@repo/ui/components/checkbox';
import { Label } from '@repo/ui/components/label';
import { Badge } from '@repo/ui/components/badge';
import { Separator } from '@repo/ui/components/separator';
import { MessageSquare, Star } from 'lucide-react';

interface Question {
  id: number;
  title: string;
  type: string;
  feedback?: string;
  hasFeedback: boolean;
  options?: any[];
  response?: any;
}

interface QuestionRendererProps {
  question: Question;
  questionNumber: number;
  onUpdate: (response: any, feedback?: string) => void;
  isSaving: boolean;
}

const QuestionRenderer: React.FC<QuestionRendererProps> = ({
  question,
  questionNumber,
  onUpdate,
  isSaving
}) => {
  const [response, setResponse] = useState(question.response || '');
  const [feedback, setFeedback] = useState(question.feedback || '');
  const [showFeedback, setShowFeedback] = useState(!!question.feedback);

  const handleResponseChange = (newResponse: any) => {
    setResponse(newResponse);
    onUpdate(newResponse, feedback);
  };

  const handleFeedbackChange = (newFeedback: string) => {
    setFeedback(newFeedback);
    onUpdate(response, newFeedback);
  };

  const renderRatingScale = () => {
    const ratings = [
      { value: 5, label: 'Excellent' },
      { value: 4, label: 'Good' },
      { value: 3, label: 'Average' },
      { value: 2, label: 'Below Average' },
      { value: 1, label: 'Poor' }
    ];

    return (
      <div className="space-y-4">
        <RadioGroup
          value={response?.toString()}
          onValueChange={(value) => handleResponseChange(parseInt(value))}
          className="space-y-3"
        >
          {ratings.map((rating) => (
            <div key={rating.value} className="flex items-center space-x-3">
              <RadioGroupItem value={rating.value.toString()} id={`rating-${rating.value}`} />
              <Label 
                htmlFor={`rating-${rating.value}`}
                className="flex items-center space-x-2 cursor-pointer"
              >
                <div className="flex">
                  {Array.from({ length: 5 }, (_, i) => (
                    <Star
                      key={i}
                      className={`h-4 w-4 ${
                        i < rating.value
                          ? 'fill-yellow-400 text-yellow-400'
                          : 'text-gray-300'
                      }`}
                    />
                  ))}
                </div>
                <span>{rating.label}</span>
              </Label>
            </div>
          ))}
        </RadioGroup>
      </div>
    );
  };

  const renderRadioOptions = () => {
    if (!question.options) return null;

    return (
      <RadioGroup
        value={response?.toString()}
        onValueChange={(value) => handleResponseChange(value)}
        className="space-y-3"
      >
        {question.options.map((option) => (
          <div key={option.id} className="flex items-center space-x-3">
            <RadioGroupItem value={option.value?.toString() || option.text} id={`option-${option.id}`} />
            <Label htmlFor={`option-${option.id}`} className="cursor-pointer">
              {option.text}
            </Label>
          </div>
        ))}
      </RadioGroup>
    );
  };

  const renderCheckboxOptions = () => {
    if (!question.options) return null;

    const selectedValues = Array.isArray(response) ? response : [];

    return (
      <div className="space-y-3">
        {question.options.map((option) => (
          <div key={option.id} className="flex items-center space-x-3">
            <Checkbox
              id={`checkbox-${option.id}`}
              checked={selectedValues.includes(option.value || option.text)}
              onCheckedChange={(checked) => {
                const newValues = checked
                  ? [...selectedValues, option.value || option.text]
                  : selectedValues.filter(v => v !== (option.value || option.text));
                handleResponseChange(newValues);
              }}
            />
            <Label htmlFor={`checkbox-${option.id}`} className="cursor-pointer">
              {option.text}
            </Label>
          </div>
        ))}
      </div>
    );
  };

  const renderTextarea = () => (
    <Textarea
      value={response}
      onChange={(e) => handleResponseChange(e.target.value)}
      placeholder="Please provide your response..."
      className="min-h-[120px] resize-none"
    />
  );

  const renderInput = () => (
    <Input
      value={response}
      onChange={(e) => handleResponseChange(e.target.value)}
      placeholder="Enter your response..."
    />
  );

  const renderRanking = () => {
    if (!question.options) return null;

    return (
      <div className="space-y-3">
        <p className="text-sm text-muted-foreground">
          Drag and drop to rank these items from most important (1) to least important ({question.options.length})
        </p>
        <div className="space-y-2">
          {question.options.map((option, index) => (
            <div
              key={option.id}
              className="flex items-center space-x-3 p-3 border rounded-lg bg-card"
            >
              <Badge variant="outline">{index + 1}</Badge>
              <span>{option.text}</span>
            </div>
          ))}
        </div>
      </div>
    );
  };

  const renderQuestionContent = () => {
    switch (question.type.toLowerCase()) {
      case 'rating':
      case 'scale':
        return renderRatingScale();
      case 'radio':
      case 'single_choice':
        return renderRadioOptions();
      case 'checkbox':
      case 'multiple_choice':
        return renderCheckboxOptions();
      case 'textarea':
      case 'paragraph':
      case 'long_text':
        return renderTextarea();
      case 'input':
      case 'text':
      case 'short_text':
        return renderInput();
      case 'ranking':
        return renderRanking();
      default:
        return (
          <div className="p-4 border border-dashed rounded-lg text-center text-muted-foreground">
            Question type "{question.type}" not yet implemented
          </div>
        );
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Badge variant="outline">Q{questionNumber}</Badge>
              {question.hasFeedback && (
                <Badge variant="secondary" className="text-xs">
                  <MessageSquare className="h-3 w-3 mr-1" />
                  Feedback Optional
                </Badge>
              )}
            </div>
            <CardTitle className="text-lg leading-relaxed">
              {question.title}
            </CardTitle>
          </div>
          {isSaving && (
            <Badge variant="outline" className="animate-pulse">
              Saving...
            </Badge>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Question Response */}
        <div className="space-y-4">
          {renderQuestionContent()}
        </div>

        {/* Feedback Section */}
        {question.hasFeedback && (
          <>
            <Separator />
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Label className="text-sm font-medium">
                  Additional Comments (Optional)
                </Label>
                {!showFeedback && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowFeedback(true)}
                  >
                    <MessageSquare className="h-4 w-4 mr-2" />
                    Add Comment
                  </Button>
                )}
              </div>
              
              {showFeedback && (
                <div className="space-y-3">
                  <Textarea
                    value={feedback}
                    onChange={(e) => handleFeedbackChange(e.target.value)}
                    placeholder="Please provide any additional comments or feedback..."
                    className="min-h-[80px] resize-none"
                  />
                  <div className="flex justify-end space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setShowFeedback(false);
                        setFeedback('');
                        handleFeedbackChange('');
                      }}
                    >
                      Remove
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
};

export default QuestionRenderer;
