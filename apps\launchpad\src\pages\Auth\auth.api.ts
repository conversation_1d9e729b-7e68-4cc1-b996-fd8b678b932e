// import axios from "axios";
// import api from "unmatched/utils/api";
// import API_URLS from "unmatched/utils/urls/api-urls";
import axiosInstance from "@/lib/axios";
import util from "../../unmatched/utils";

const { apiUrls, api } = util;

const API_URLS = apiUrls;

interface ReqPayload {
  email: string;
}

interface PerfMagic extends ReqPayload {
  token: string;
  surveyIndex?: string;
}

interface SetPayload {
  email: string;
  token: string;
  password: string;
  oldPassword?: string;
}

export const activationFact = (data: SetPayload, params?: any, meta?: any) => {
  const config = api.getConfigurations(params, meta);

  return axiosInstance.post(
    `${API_URLS.SET_ACTIVATION}`,
    {
      user_email: data.email,
      token: data.token,
      password: data.password,
    },
    config
  );
};

export const resetFact = (data: SetPayload, params?: any, meta?: any) => {
  const config = api.getConfigurations(params, meta);

  return axiosInstance.post(
    `${API_URLS.SET_RESET}`,
    {
      user_email: data.email,
      token: data.token,
      password: data.password,
    },
    config
  );
};

export const setPasswordFact = (data: SetPayload, params?: any, meta?: any) => {
  const config = api.getConfigurations(params, meta);
  return axiosInstance.post(
    `${API_URLS[!data.oldPassword ? "SET_PASSWORD" : "CHANGE_PASSWORD"]}`,
    {
      token: data.token,
      new_password: data.password,
      ...(data.oldPassword && { old_password: data.oldPassword }),
    },
    config
  );
};

export const reqActivationFact = (
  data: ReqPayload,
  params?: any,
  meta?: any
) => {
  const config = api.getConfigurations(params, meta);

  return axiosInstance.post(
    `${API_URLS.REQ_ACTIVATION}`,
    {
      user_email: data.email,
    },
    config
  );
};

export const reqResetREQFact = (data: ReqPayload, params?: any, meta?: any) => {
  const config = api.getConfigurations(params, meta);

  return axiosInstance.post(
    `${API_URLS.REQ_RESET}`,
    {
      user_email: data.email,
    },
    config
  );
};

export const requestMagicLinkFact = (
  data: ReqPayload,
  params?: any,
  meta?: any
) => {
  const config = api.getConfigurations(params, meta);

  return axiosInstance.post(
    `${API_URLS.REQ_MAGIC_LINK}`,
    {
      user_email: data.email,
    },
    config
  );
};

export const performMagicLinkLoginFact = (
  data: PerfMagic,
  params?: any,
  meta?: any
) => {
  const config = api.getConfigurations(params, meta);

  return axiosInstance.post(
    `${API_URLS.PERFORM_MAGIC_LINK_LOGIN}`,
    {
      user_email: data.email,
      token: data.token,
      ...(data.surveyIndex && { survey_index: data.surveyIndex }),
    },
    config
  );
};

export const getBEToken = (data: any, params?: any, meta?: any) => {
  const config = api.getConfigurations(params, meta);


  return axiosInstance.post(
    'https://alpha.unmatched.app/api/v2/auth/sso/perform/',
    {
      token: data.token,
    },
    config
  );
};
