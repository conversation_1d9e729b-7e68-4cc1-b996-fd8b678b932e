import ROUTES from "@/routes";
import SettingsRoutes from "./settings/settings.route";
import DashboardLayout from "./Dashboard.layout";
import Launcher from "./Launcher/Launcher";
import { FileDetails } from "./settings/files/FileDetails";

const DashboardRoute = [
  {
    path: ROUTES.ROOT,
    layout: () => <DashboardLayout />,
    children: [
      {
        name: "Launchpad",
        path: ROUTES.ROOT,
        protected: true,
        component: () => <Launcher />,
      },
      {
        name: "Settings",
        path: ROUTES.SETTINGS.ROOT,
        protected: true,
        children: SettingsRoutes,
        // component: () => <SettingsLayout />,
      },
    ],
  },
  // Add a direct route for file details
  {
    path: ROUTES.SETTINGS.FILE_DETAILS,
    layout: () => <DashboardLayout />,
    children: [
      {
        name: "File Details",
        path: ROUTES.SETTINGS.FILE_DETAILS,
        protected: true,
        component: FileDetails,
      },
    ],
  },
  ...SettingsRoutes,
];

export default DashboardRoute;
