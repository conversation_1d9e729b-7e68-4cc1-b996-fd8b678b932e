import { useState } from "react";
import { Accordion, Modal, useAccordionToggle } from "react-bootstrap";
import styled from "styled-components";

const AppsIconWrap = styled.div`
  padding: 17px 0px;
  display: flex;
  justify-content: center;
  border-bottom: 1px solid #2c3851;
  width: 80%;
  margin: auto;
  cursor: pointer;
`;

const AppsHead = styled.div`
  padding: 17px;
  display: flex;
  justify-content: flex-start;
  border-bottom: 1px solid #2c3851;
  width: 100%;
  margin: auto;
  cursor: pointer;
`;

const AccordianWrap = styled.div`
  padding: 18px;
`;

function CustomToggle({ children, eventKey }: any) {
  const decoratedOnClick = useAccordionToggle(eventKey, () =>
    {
      console.log({eventKey});
    }
  );

  return <div onClick={decoratedOnClick}>{children}</div>;
}

const bgMap: any = {
  0: "#3BB195",
  1: "#B1893B",
  2: "#AE3BB1",
  3: "#B1503B",
};

export function Apps({ client, images }: any) {
  const [show, setShow] = useState(false);
  const [activeKey, setActiveKey] = useState("0");
  const { APPS, LOGO, ARROWD, ARROWR } = images;

  return (
    <>
      <AppsIconWrap onClick={() => setShow(true)}>
        <img width={25} src={APPS} />
      </AppsIconWrap>
      <Modal
        show={show}
        onHide={() => setShow(false)}
        dialogClassName="apps-modal"
        aria-labelledby="example-custom-modal-styling-title"
        backdropClassName="custom-backdrop"
        className="apps-modal-wrap"
      >
        <Modal.Body>
          <AppsHead>
            <img onClick={() => setShow(false)} width={25} src={APPS} />
            <img style={{ marginLeft: 50 }} src={LOGO} />
          </AppsHead>
          <AccordianWrap>
            <div className="apps-text">Apps</div>
            <Accordion defaultActiveKey="0" onSelect={(activeKey: any) => {
              setActiveKey(activeKey);
            }}>
              {client?.features?.map((feature: any, index: string) => {
                return (
                  <div key={index}>
                    <CustomToggle
                      setActiveKey={setActiveKey}
                      eventKey={`${index}`}
                    >
                      <div style={{ display: "flex", margin: "10px 0" }}>
                        <div
                          className="color-block"
                          style={{ background: bgMap[index] }}
                        />
                        <div style={{ cursor: "pointer" }}>
                          <div className="app-name">
                            {feature.name}{" "}
                            <img style={{ marginLeft: 2 }} src={activeKey == index ? ARROWD : ARROWR} />
                          </div>
                          <div className="app-desc">
                            {feature.description || "Performance & Feedback"}
                          </div>
                        </div>
                      </div>
                    </CustomToggle>
                    <Accordion.Collapse eventKey={`${index}`}>
                      <div style={{ color: "#fff", margin: "0  0 12px 0px" }}>
                        {feature.features?.map(
                          (nestedFeature: any, index: number) => {
                            return (
                              <a
                                href={`/${nestedFeature.slug}/`}
                                className="feat-link"
                                key={index}
                              >
                                {nestedFeature.name}
                              </a>
                            );
                          }
                        )}
                      </div>
                    </Accordion.Collapse>
                  </div>
                );
              })}
            </Accordion>
          </AccordianWrap>
        </Modal.Body>
        <Modal.Footer>
          <a href="/" className="app-btn">
            Open Launchpad
          </a>
        </Modal.Footer>
      </Modal>
    </>
  );
}
