import ModalHeader from "pages/AdminApp/ModalHeader";
import {
  Text,
  Layout,
  Button,
  Div,
  CustomModal as Modal,
} from "unmatched/components";

export function AggregateSuccessModal(props: any) {
  const { loading, goTo } = props;

  return (
    <>
      <Modal
        {...props}
        onHide={() => {
          props.onHide();
        }}
        backdrop="static"
        centered
        size="lg"
        children={
          <>
            <ModalHeader
              title="Download Reports"
              onHide={() => {
                props.onHide();
              }}
            />
            <Modal.Body style={{ height: "auto" }}>
              <Text.H2 className="pb-2 text-capitalize ">
                Your download request is being processed.
              </Text.H2>
              <Text.P1 className="pb-4">
                Reports are being processed and will be ready to download in
                ‘download requests’ tab. Please check back in sometime.
              </Text.P1>
            </Modal.Body>
            <Modal.Footer>
              <Div className="w-100">
                <Layout.Row>
                  <Layout.Col className="col-7"></Layout.Col>
                  <Layout.Col>
                    <Button
                      className="float-right"
                      type="button"
                      variant="primary"
                      size="lg"
                      onClick={() => {
                        goTo("DOWNLOADS", true);
                        props.onHide();
                      }}
                      disabled={loading}
                    >
                      Go to download requests
                    </Button>
                  </Layout.Col>
                </Layout.Row>
              </Div>
            </Modal.Footer>
          </>
        }
      />
    </>
  );
}
