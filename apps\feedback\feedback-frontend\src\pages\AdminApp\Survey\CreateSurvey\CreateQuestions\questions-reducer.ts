// Helpers
import { cloneDeep } from "lodash";
import { ErrorType } from "unmatched/types";
import util from "unmatched/utils";
// import { generateReducer, getXHRState, setState } from "unmatched/utils/store";
// Constants
// Actions
import { TYPES } from "./questions-actions";
import * as stateTypes from "./question-state-types";
// console.log(util.store);

const { generateReducer, getXHRState, setState } = util.store;

interface StateType {
  isLoading: boolean;
  sections: stateTypes.SectionsInterface;
  questions: stateTypes.QuestionsInterface;
  rankings: stateTypes.RankingInterface;
  radios: stateTypes.RadioInterface;
  checkboxs: stateTypes.CheckboxInterface;
  dropdowns: stateTypes.DropdownInterface;
  inputs: stateTypes.InputInterface;
  error: ErrorType;
}

const getBaseState = () => {
  return {
    list: [],
    content: {},
  };
};

const initialState: StateType = {
  ...getXHRState(),
  sections: getBaseState(),
  questions: getBaseState(),
  rankings: {
    ...getBaseState(),
    options: getBaseState(),
  },
  dropdowns: {
    ...getBaseState(),
    options: getBaseState(),
  },
  inputs: {
    ...getBaseState(),
  },
  checkboxs: {
    ...getBaseState(),
    options: getBaseState(),
  },
  radios: {
    ...getBaseState(),
    options: getBaseState(),
  },
};

const ACTIONS = {
  [TYPES.META_LOAD]: (state: StateType, isLoading: boolean) =>
    setState(state, {
      isLoading,
    }),
  [TYPES.META_GET_SUCCESS]: (state: StateType, payload: any) =>
    setState(state, {
      isLoading: false,
      ...payload,
    }),
  [TYPES.META_GET_FAILURE]: (state: StateType, payload: any) =>
    setState(state, {
      error: payload,
      isLoading: false,
    }),
  [TYPES.SET_SECTION]: (state: StateType, payload: any) =>
    setState(state, {
      sections: {
        list: payload.list || state.sections.list,
        content: {
          ...state.sections.content,
          ...payload.content,
        },
      },
    }),
  [TYPES.SET_QUESTION]: (state: StateType, payload: any) =>
    setState(state, {
      questions: {
        list: payload.list || state.questions.list,
        content: {
          ...state.questions.content,
          ...payload.content,
        },
      },
    }),
  [TYPES.SET_RANKING]: (state: StateType, payload: any) =>
    setState(state, {
      rankings: {
        list: payload.list || state.rankings.list,
        content: {
          ...state.rankings.content,
          ...payload.ranking,
        },
      },
    }),
  [TYPES.SET_CHECKBOX]: (state: StateType, payload: any) =>
    setState(state, {
      checkboxs: {
        list: payload.list || state.checkboxs.list,
        content: {
          ...state.checkboxs.content,
          ...payload.checkbox,
        },
      },
    }),
  [TYPES.SET_RADIO]: (state: StateType, payload: any) =>
    setState(state, {
      radios: {
        list: payload.list || state.radios.list,
        content: {
          ...state.radios.content,
          ...payload.radio,
        },
      },
    }),
  [TYPES.SET_DROPDOWN]: (state: StateType, payload: any) =>
    setState(state, {
      dropdowns: {
        list: payload.list || state.dropdowns.list,
        content: {
          ...state.dropdowns.content,
          ...payload.dropdown,
        },
        options: {
          list: payload.options.list || state.dropdowns.options.list,
          content: {
            ...state.dropdowns.options.content,
            ...payload.options.content,
          },
        },
      },
    }),
  [TYPES.SET_RANKING]: (state: StateType, payload: any) =>
    setState(state, {
      inputs: {
        list: payload.list || state.inputs.list,
        content: {
          ...state.inputs.content,
          ...payload.input,
        },
      },
    }),
  [TYPES.RESET]: (state: StateType, payload: any) => {
    return setState(state, payload || cloneDeep(initialState));
  },
};

export default generateReducer(ACTIONS, initialState);
