import React from "react";
import styled from "styled-components";
import { Div, Layout, Tab, Nav } from "unmatched/components";
import appUrls from "unmatched/utils/urls/app-urls";
import useCustomLayout from "../../../Shared/custom-layout-hook";
// import Emailstats from "../ReportViews/EmailStats/EmailStats";
import { useQuery, useHistory, useParams } from "unmatched/hooks";
import { requestNewReport } from "../../reports-api";
import useToastr from "unmatched/modules/toastr/hook";
import EngagementReports from "../ReportViews/EngagementReports/EngagementReports";
import DataDump from "../ReportViews/DataDump/DataDump";
import AdvancedReports from "./AdvancedReports/AdvancedReports";
import Downloads from "./Download/Download";

const TABS = {
  INDIVIDUAL: "INDIVIDUAL",
  DATADUMP: "DATADUMP",
  EMAIL_STATS: "EMAIL_STATS",
  DOWNLOADS: "DOWNLOADS",
  ADVANCED_REPORTS: "ADVANCED_REPORTS"
};

const NavItem = styled(Nav.Item)`
  font-size: 14px;
  padding-top: 15px;
  .active {
    background: #ebeff7 !important;
    position: relative;
    color: #2f2f2f !important;
    font-weight: 600;
    border-radius: 0;
  }
`;

const NavLink = styled(Nav.Link).attrs({
  className: "text-muted",
})``;

type Props = {
  survey: any;
};

export default function EngagementSurveyReport({ survey }: Props) {
  const layout = useCustomLayout();
  const toastr = useToastr();
  const queryParams = useQuery();
  const params = useParams<any>();
  const history = useHistory();
  const [activeTab, setActiveTab] = React.useState(
    queryParams.get("filter") || TABS.INDIVIDUAL
  );

  const [loaders, setLoaders] = React.useState({
    generate: false,
  });
  const onTabSelect = (filter: string) => {
    history.push(appUrls.admin.reports.getSurveyReportsUrl(survey.id, filter));
    setActiveTab(filter);
  };
  const generateReport = async () => {
    setLoaders({ ...loaders, generate: true });
    try {
      const response = await requestNewReport({ index: params.id });
      toastr.onSucces({
        title: "Report Generation Requested",
        content:
          "Reports generatation request has been sent with request id: " +
          response.data.taskId,
      });
      setLoaders({ ...loaders, generate: false });
    } catch (err) {
      setLoaders({ ...loaders, generate: false });
    }
  };

  return (
    <Tab.Container defaultActiveKey={activeTab}>
      <Layout.Sidebar
        className="dark-sidebar"
        hasHeader={false}
        style={{ marginLeft: layout.sidebar.marginLeft, zIndex: 1000 }}
        width={layout.sidebar.width}
      >
        {/* <Div className="pl-3 py-2 border-bottom"> */}
          {/* <Link style={{ color: '#fff' }} to={appUrls.admin.reports.surveyList}>
            <Icon icon="far fa-chevron-left" /> Surveys
          </Link> */}
        {/* </Div> */}
        <Nav variant="pills" className="flex-column">
          <NavItem>
            <NavLink
              className="dark-nav-link text-white"
              eventKey={TABS.INDIVIDUAL}
              onSelect={(_filter: string) => onTabSelect(_filter)}
            >
              Reports
            </NavLink>
          </NavItem>
          <NavItem>
            <NavLink
              className="dark-nav-link text-white"
              eventKey={TABS.DATADUMP}
              onSelect={(_filter: string) => onTabSelect(_filter)}
            >
              Data Dump
            </NavLink>
          </NavItem>
          <NavItem>
            <NavLink
              className="dark-nav-link text-white"
              eventKey={TABS.DOWNLOADS}
              onSelect={(_filter: string) => onTabSelect(_filter)}
            >
              Downloads
            </NavLink>
          </NavItem>
          <NavItem>
            <NavLink
              className="dark-nav-link text-white"
              eventKey={TABS.ADVANCED_REPORTS}
              onSelect={(_filter: string) => onTabSelect(_filter)}
            >
              Advanced Reports
            </NavLink>
          </NavItem>
          {/* <NavItem>
            <NavLink
              className="dark-nav-link text-white"
              eventKey={TABS.EMAIL_STATS}
              onSelect={(_filter: string) => onTabSelect(_filter)}
            >
              Email Statistics
            </NavLink>
          </NavItem> */}
        </Nav>
      </Layout.Sidebar>
      <Div className="" style={{ marginLeft: layout.container.marginLeft }}>
        <Tab.Content>
          <Tab.Pane className="bg-white" eventKey={TABS.INDIVIDUAL}>
            <EngagementReports
              survey={survey}
              layout={layout}
              surveyId={params.id}
              generateReport={generateReport}
              loaders={loaders}
            />
          </Tab.Pane>
          <Tab.Pane className="bg-white" eventKey={TABS.DATADUMP}>
            <DataDump
              type="xlsx"
              survey={survey}
              layout={layout}
              surveyId={params.id}
            />
          </Tab.Pane>
          <Tab.Pane className="bg-white" eventKey={TABS.DOWNLOADS}>
            <Downloads
              survey={survey}
              layout={layout}
              surveyId={params.id}
              activeTab={activeTab}
            />
          </Tab.Pane>
          <Tab.Pane className="bg-white" eventKey={TABS.ADVANCED_REPORTS}>
            <AdvancedReports survey={survey} layout={layout} />
          </Tab.Pane>
          {/* <Tab.Pane className="bg-white" eventKey={TABS.EMAIL_STATS}>
            <Emailstats
              survey={survey}
              layout={layout}
              surveyId={params.id}
              generateReport={generateReport}
              loaders={loaders}
            />
          </Tab.Pane> */}
        </Tab.Content>
      </Div>
    </Tab.Container>
  );
}
