import React, { useEffect, useRef } from "react";
import {
  Div,
  Layout,
  FormControl,
  Button,
  Table,
  Text,
  FormGroup,
  CustomModal as Modal,
  // ComboFilter,
  Icon,
  MultiSelect,
  // OverlayTrigger,
  // Tooltip,
  ScrollToBottom,
} from "unmatched/components";
import util from "unmatched/utils";
import useSidebar from "../../Shared/sidebar-hook";
import SurveyStatus from "./SurveyStatus";
import ICONS from "assets/icons/icons";
import data from "../../../../../assets/images/images";
import { useHistory, useTable } from "unmatched/hooks";
import useToastr from "unmatched/modules/toastr/hook";
import {
  getSetSurveyUserResponseActionFact,
  getSetSurveyUserResponseFact,
  getSurveyAllUserFact,
  getSurveyPairsFact,
  setAddUserToSurveyFact,
} from "../upward-review-api";
import { getSurveyInfoFactV2 } from "../../dashboard-api";
import { useParams } from "react-router-dom";
import useFilter from "pages/CommonFilters/hook";
import { useDebounce, useWindowSize } from "react-use";
import styled from "styled-components";
import { OverlayTrigger, Tooltip } from "react-bootstrap";
import { debounce, get, keys, map } from "lodash";
import surveyTakeCore from "unmatched/survey/take/components";
import useUtil from "../../Shared/hooks/util-hook";
import BackToDashboard from "../../Shared/BackToDashboard";
import { getStats } from "../../SurveyList/surveys-api";

const { TakeSurveyContainer } = surveyTakeCore;

interface User {
  key: string;
  firstName: string;
  lastName: string;
  emp_id: string;
  email: string;
  location: string;
  department: string;
  first_name: string;
  last_name: string;
  id: number;
  survey_response?: string;
  metadata?: any;
  survey: string | number;
  response_id?: string;
  target: {
    first_name: string;
    last_name: string;
    emp_id: string;
    metadata: any;
  };
}
interface methodIn {
  title: string;
  endpoint: string;
}

// const COHARTS = [
//   { key: "department", title: "Department" },
//   { key: "location", title: "Location" },
// ];

const Method = (option: any) => {
  type methodType = {
    [key: string]: methodIn;
  };
  const methodObject: methodType = {
    TODO: {
      title: "To Do",
      endpoint: "/",
    },
    PROG: {
      title: "In Progress",
      endpoint: "/",
    },
    SUBM: {
      title: "Completed",
      endpoint: "/",
    },
    DECL: {
      title: "Declined Contacts",
      endpoint: "/",
    },
    ADD: {
      title: "Add Reviewee",
      endpoint: "/",
    },
  };
  return methodObject[option] ?? "Method Not Found";
};

// const FILTER_DATA = [
//   { key: "Option1", title: "Option1" },
//   { key: "Option2", title: "Option2" },
// ];

// const getPairFact = () =>
//   new Promise((resolve: Function) => {
//     window.setTimeout(() => {
//       resolve();
//     }, 400);
//   });

const Pairings = () => {
  const dashboardUtil = useUtil();
  const layout = useSidebar();
  const history = useHistory();
  const toastr = useToastr();
  const { id } = useParams<any>();
  const tableMeta = useTable({ page: 1, size: 15 });
  const multiSelectInputRef = useRef(null);
  // const { showToast } = useToastr();
  const [selectAll, setSelectAll] = React.useState(false);
  const [isLoading, setLoading] = React.useState(true);
  const [isTableLoading, setTableLoading] = React.useState(true);
  const [users, setUsers] = React.useState<Array<User>>([]);
  // const [filters, setFilters] = React.useState({
  //   search: "",
  // });
  const [selected, setSelected] = React.useState<Array<string | number>>([]);
  const [selectedUserMeta, setSelectedUserMeta] = React.useState<Array<any>>(
    []
  );

  const [ordering, setOrdering] = React.useState("first_name");

  const media = useWindowSize();

  const [showSidebar, setShowSidebar] = React.useState(false);

  const [surveyInfo, setSurveyInfo] = React.useState<any>({
    title: "",
    isPairsEditable: false,
  });

  // const [selectedUser, setSelectedUser] = React.useState({
  //   empId: "",
  //   state: ModalState.VIEW,
  // });

  // console.log(selectedUser.empId);
  // const [metas] = React.useState<any>([]);
  const filtersState = useFilter(id, true);
  const [statMap, setStatMap] = React.useState<any>({});
  const [parameters, setParameters] = React.useState<any>({
    index_id: id,
    status: "TODO",
    page_size: 10,
    search: undefined,
    // appliedMetaFilters: {},
  });

  const [columnsData, setColumnsData] = React.useState<any>([]); // deepscan-disable-line

  useEffect(() => {
    setColumnsData(getColumns());
  }, [filtersState.filters, parameters.status, users, selectAll]);

  const getColumnsData = () => {
    const coumnsList = keys(columnsData);
    return map(coumnsList, (key: string) => ({
      ...get(columnsData, key),
      key,
    }));
  };

  // const [,] = useDebounce(
  //   () => {
  //     if (
  //       selectedMeta.applied === undefined ||
  //       selectedMeta.cohert === "" ||
  //       !selectedMeta.cohert
  //     ) {
  //       // setSelectedMeta((prevState) => ({...prevState, applied: "" }));
  //       return;
  //     }
  //     return setParameters((prev: any) => ({
  //       ...prev,
  //       appliedMetaFilters: { [selectedMeta.cohert]: selectedMeta.applied },
  //     }));
  //   },
  //   100,
  //   [selectedMeta]
  // );

  // const [,] = useDebounce(
  //   () => {
  //     if (filters.search !== "" || filters.search !== undefined) {
  //       // return setParameters((prev: any) => ({
  //       //   ...prev,
  //       //   search: undefined,
  //       // }));
  //       return setParameters((prev: any) => ({
  //         ...prev,
  //         search: filters.search,
  //       }));
  //     }
  //     return
  //   },
  //   1000,
  //   [filters]
  // );

  const getFilters = () => {
    filtersState.getFilters();
  };
  // const getCohertSelected = (cohert: string) => {
  //   if (cohert === undefined || cohert === null) {
  //     return "";
  //   }
  //   return metas.filter((meta: any) => meta.key === cohert)[0]?.title ?? "";
  // };

  // const getCohertValues = (cohert: string) => {
  //   if (cohert === undefined || cohert === null) {
  //     return [];
  //   }
  //   return metas.filter((meta: any) => meta.key === cohert)[0]?.value ?? [];
  // };
  // const cohart = {
  //   cohart: {
  //     options: metas,
  //     selected: getCohertSelected(selectedMeta.cohert),
  //   },
  //   applied: {
  //     options: getCohertValues(selectedMeta.cohert),
  //     selected: selectedMeta.applied,
  //   },
  // };

  const [surveyFilters, setSurveyFilters] = React.useState<any>({});
  const [isLoaded, setIsLoaded] = React.useState(false);

  React.useEffect(() => {
    getFilters();
    async function call() {
      try {
        await getSurveyInfo();
        setLoading(false);
      } catch (err) {
        setLoading(false);
      }
    }
    call();
    //eslint-disable-next-line
  }, []);

  useDebounce(
    () => {
      setUsers([]);
      setSelected([]);
      setSelectedUserMeta([]);
      getUsers();
      setSelectAll(false);
    },
    400,
    [parameters]
  );

  const getSurveyInfo = async () => {
    const surveyInfoResponse = await getSurveyInfoFactV2(id);
    setSurveyInfo(surveyInfoResponse.data);
    getGeneralStats([surveyInfoResponse.data])
  };

  const getGeneralStats = async (surveys: any) => {
    const statObj = await Promise.all(surveys.map(async (s: any) => {
      const stat = await getStats('index', s.id, 'general')
      return new Promise((resolve) => resolve({ [s.id]: stat?.data }));
    }));
    setStatMap(statObj.reduce((acc: any, el: any) => {
      const [k, v] = Object.entries(el)[0];
      acc[k] = v;
      return acc;
    }, {}));
  }

  const getUsers = async (
    page?: number,
    _filters?: any,
    lOrdering: string = ordering
  ) => {
    try {
      setTableLoading(true);
      const userDataResponse =
        parameters.status !== "ADD"
          ? await getSurveyPairsFact({
              ...parameters,
              ...filtersState.getParams(_filters || filtersState.selected),
              page,
              ordering: lOrdering,
              exclude_disabled: true,
              page_size: tableMeta.size,
            })
          : await getSurveyAllUserFact({
              index_id: id,
              page,
              page_size: tableMeta.size,
              search: parameters.search,
              ...filtersState.getParams(_filters || filtersState.selected),
              ordering: lOrdering,
              // ...parameters.appliedMetaFilters,
            });
      tableMeta.updatePagination({
        totalPages: userDataResponse.data.count_pages,
        totalItems: userDataResponse.data.count_items
      });
      setUsers(userDataResponse.data.results);
      const metaItems = userDataResponse.data.metadata_info.labels;
      const metaValues = userDataResponse.data.metadata_info.values;
      const items: any = {};
      metaItems.map(
        (item: any) =>
          (items[item.field] = {
            label: item.display_name,
            values: metaValues[item.field],
          })
      );
      if (!isLoaded) {
        setIsLoaded(true);
        setSurveyFilters(items);
      }

      setTableLoading(false);
    } catch (err) {
      setTableLoading(false);
    }
  };

  const onSurveyStart = debounce(async (
    survey: number | string,
    pairing: number,
    response?: string
  ) => {
    if (response && survey) {
      history.push(
        util.appUrls.user.dashboard.upwardReview.getTakeSurveyUrl(
          response || "",
          survey || ""
        )
      );
    } else {
      try {
        const setResponse = await getSetSurveyUserResponseFact({
          survey,
          pairing,
          // status: "PROG",
        });
        history.push(
          util.appUrls.user.dashboard.upwardReview.getTakeSurveyUrl(
            setResponse.data.id,
            setResponse.data.survey
          )
        );
      } catch (err: any) {
        toastr.onError(err);
      }
    }
  }, 500);

  const shouldNotStartSurvey = () =>
    ["DECL", "ADD"].some((el) => parameters.status === el);

  // const onOptionsSelect = (_selected: any, type: string) => {
  //   setFilters((_filtes: any) => {
  //     const current = _.get(_filtes, type);
  //     return {
  //       ..._filtes,
  //       [type]: {
  //         ...current,
  //         selected: _selected,
  //       },
  //     };
  //   });
  // };

  const [showDecline, setShowDecline] = React.useState<any>({
    pairing: 0,
    name: "",
    status: false,
    survey: 0,
    responseID: null,
  });
  const [showMultiDecline, setShowMultiDecline] = React.useState(false);

  const onDecline = debounce(async () => {
    const createResponse = async () => {
      const response = await getSetSurveyUserResponseFact({
        survey: showDecline.survey,
        pairing: showDecline.pairing,
      });
      return response.data.id;
    };
    const resID = showDecline.responseID
      ? showDecline.responseID
      : await createResponse();
    try {
      await getSetSurveyUserResponseActionFact(
        {
          survey: showDecline.survey,
          pairing: showDecline.pairing,
          resourcetype: "SurveyResponseUpward",
          // status: "DECL",
        },
        resID,
        "DECL"
      );
      await getUsers();
      await getSurveyInfo();
      toastr.onSucces({
        title: "Pairing Declined",
        content: "Selected pairing was declined successfully.",
      });

      setShowDecline({
        pairing: 0,
        name: "",
        status: false,
        survey: 0,
        responseID: null,
      });
    } catch (err: any) {
      new Error(err?.message || "Error");
    }
  }, 500);
  const onMultiDecline = async () => {
    await Promise.all(
      selectedUserMeta.map(async (item) => {
        const createResponse = async () => {
          const response = await getSetSurveyUserResponseFact({
            survey: item.survey,
            pairing: item.pairing,
          });
          return response.data.id;
        };
        const resID = item.response_id
          ? item.response_id
          : await createResponse();
        await getSetSurveyUserResponseActionFact(
          {
            survey: item.survey,
            pairing: item.pairing,
            // status: "DECL",
          },
          resID,
          "DECL"
        );
      })
    );
    setSelectedUserMeta([]);
    setSelected([]);
    await getUsers();
    await getSurveyInfo();
    setShowMultiDecline(false);
    toastr.onSucces({
      title: "Pairings Declined",
      content: "Selected pairings was declined successfully.",
    });
  };

  const onPairAdded = async () => {
    try {
      // for (let index = 0; index < selected.length; index++) {
      //   await setAddUserToSurveyFact({
      //     index: id,
      //     target: selected[index],
      //   });
      // }
      await Promise.all(
        selected.map(async (item) => {
          await setAddUserToSurveyFact({
            index: id,
            target: item,
          });
        })
      );
      await getSurveyInfo();
      setParameters({ ...parameters, status: "TODO" });
      toastr.onSucces({
        title: "New Pairings Added",
        content: "Selected pairing were added successfully.",
      });
    } catch (err: any) {
      toastr.onError(err);
    }
  };

  const onPairReinstate = async (
    survey: string | number,
    pairing: string | number | undefined,
    responseID: string
  ) => {
    try {
      await getSetSurveyUserResponseActionFact(
        {
          survey,
          pairing,
          // status: "TODO",
        },
        responseID,
        "TODO"
      );
      await getSurveyInfo();
      setParameters({ ...parameters, status: "TODO" });
      toastr.onSucces({
        title: "Reinstated the contact",
        content: "Selected pairing were added back successfully.",
      });
    } catch (err: any) {
      new Error(err?.message || "Error");
    }
  };
  const onMultiPairReinstate = async () => {
    try {
      await Promise.all(
        selectedUserMeta.map(async (item) => {
          const createResponse = async () => {
            const response = await getSetSurveyUserResponseFact({
              survey: item.survey,
              pairing: item.pairing,
            });
            return response.data.id;
          };
          const resID = item.response_id
            ? item.response_id
            : await createResponse();
          await getSetSurveyUserResponseActionFact(
            {
              survey: item.survey,
              pairing: item.pairing,
              // status: "TODO",
            },
            resID,
            "TODO"
          );
        })
      );
      await getUsers();
      await getSurveyInfo();
      setParameters({ ...parameters, status: "TODO" });
      toastr.onSucces({
        title: "Reinstated the contacts",
        content: "Selected pairings were added back successfully.",
      });
    } catch (err: any) {
      new Error(err?.message || "Error");
    }
  };

  // const getUsers = async () => {
  //   try {
  //     // setLoading(true);
  //     // await getPairFact();
  //     // setUsers(JSON_DATA);
  //     // setLoading(false);
  //   } catch (err) {
  //     // setLoading(false);
  //   }
  // };

  const onCheckAll = (checked: boolean) => {
    // debugger;
    // console.log(checked);
    setSelectAll(checked);
    setSelected(() => (checked ? users.map((item: any) => item.id) : []));
    setSelectedUserMeta(() =>
      checked
        ? users.map((item: any) => {
            return { pairing: item.id, survey: item.survey };
          })
        : []
    );
    // const u = users.map((item: any) => {
    //   return { pairing: item.id, survey: item.survey };
    // });
    // console.log(users);
  };

  const onSelectUser = (item: User) => {
    if (checkSelected(item)) {
      setSelected((_selected: any) => {
        const copy = _selected;
        const index = copy.findIndex((o: any) => o === item.id);
        copy.splice(index, 1);
        return [...copy];
      });
      setSelectedUserMeta((i) => {
        return i.filter((_item: any) => _item.pairing !== item.id);
      });
    } else {
      setSelected((_selected: any) => {
        const copy = _selected;
        copy.push(item.id);
        return [...copy];
      });
      setSelectedUserMeta((_i): any => {
        const items = _i;
        items.push({
          pairing: item.id,
          survey: item.survey,
          response_id: item.survey_response,
        });
        return [...items];
      });
    }
  };

  const checkSelected = (item: User) => {
    return selected.includes(item.id);
  };

  // const setSearch = (search: string) => {
  //   setFilters((_filters) => ({
  //     // ..._filters,
  //     search,
  //   }));
  // };

  // const onSearch = (search: string) => {
  //   setSearch(search);
  // };

  // const onSingleDecline = async () => '';
  // const onModalHide = () => {
  //   setSelectedUser({
  //     empId: "",
  //     state: ModalState.VIEW,
  //   });
  // };

  const onPageSelect = (page: number) => {
    // setFilters((_filters) => ({
    //   ..._filters,
    //   page,
    // }));
    tableMeta.updatePagination({
      page,
    });
    getUsers(page);
  };

  const getMetaColumns = () => {
    // let arr: any = [];
    // .map((item: any) => {
    //   if (item.key) {
    //     return arr.push(item.key);
    //   } else return 0;
    // });
    return Object.keys(filtersState.filters);
  };

  const getRows = () => {
    return users.map((item: User, index: number) => {
      const checked = checkSelected(item);
      const isEven = index % 2 === 0 || index === 0;
      return (
        <Table.Row
          even={!isEven}
          key={`${0}-${Buffer.from(Math.random().toString())
            .toString("base64")
            .substring(8, 15)}`}
          selected={checked}
        >
          <Table.Data width="30px">
            <FormGroup>
              <FormControl.Checkbox>
                <FormControl.Checkbox.Input
                  onChange={() => onSelectUser(item)}
                  checked={checked}
                />
              </FormControl.Checkbox>
            </FormGroup>
          </Table.Data>
          <Table.Data width="70px">
            <Text.P1
              className={shouldNotStartSurvey() ? "" : "pointer"}
              onClick={() => {
                if (shouldNotStartSurvey()) return;
                onSurveyStart(item.survey, item.id, item.survey_response);
              }}
            >
              {tableMeta.page * 15 + index + 1 - 15}.
            </Text.P1>
          </Table.Data>
          <Table.Data>
            {parameters.status === "DECL" ? (
              <>
                <Text.P1 className="text-left" style={{ minWidth: 100 }}>
                  <>
                    {item?.target?.first_name?.length > 0 ||
                    item?.target?.last_name?.length > 0
                      ? item?.target?.first_name + " " + item?.target?.last_name
                      : "-"}
                  </>
                </Text.P1>
              </>
            ) : (
              <Button
                variant="link"
                className="p-0 fs-12 m-0 border-0 text-left text-truncate"
                // className={shouldNotStartSurvey() ? "" : "pointer"}
                style={{ minWidth: 80 }}
                onClick={() => {
                  if (shouldNotStartSurvey()) return onSelectUser(item);
                  onSurveyStart(item.survey, item.id, item.survey_response);
                }}
              >
                {parameters.status === "ADD" ? (
                  <>
                    {item?.first_name?.length > 0 || item?.last_name?.length > 0
                      ? item?.first_name + " " + item?.last_name
                      : "-"}
                  </>
                ) : (
                  <>
                    {item?.target?.first_name?.length > 0 ||
                    item?.target?.last_name?.length > 0
                      ? item?.target?.first_name + " " + item?.target?.last_name
                      : "-"}
                  </>
                )}
              </Button>
            )}
          </Table.Data>
          {getMetaColumns().map((_i: any) => (
            <Table.Data>
              <Text.P1
                className={shouldNotStartSurvey() ? "" : "pointer"}
                onClick={() => {
                  if (shouldNotStartSurvey()) return;
                  onSurveyStart(item.survey, item.id, item.survey_response);
                }}
              >
                {parameters.status === "ADD" ? (
                  <>
                    {item?.metadata && item?.metadata[_i]
                      ? item?.metadata[_i]
                      : "-"}
                  </>
                ) : (
                  item?.target?.metadata[_i] ?? "-"
                )}
              </Text.P1>
            </Table.Data>
          ))}
          {/* <Table.Data>
            <Text.P1
              className={parameters.status === "DECL" ? "" : "pointer"}
              onClick={() => {
                if (parameters.status === "DECL") return <></>;
                onSurveyStart(item.survey, item.id, item.survey_response);
              }}
            >
              {item.location}
            </Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1
              className={parameters.status === "DECL" ? "" : "pointer"}
              onClick={() => {
                if (parameters.status === "DECL") return <></>;
                onSurveyStart(item.survey, item.id, item.survey_response);
              }}
            >
              {item.department}
            </Text.P1>
          </Table.Data> */}
          {parameters.status === "ADD" ? (
            ""
          ) : (
            <Table.Data width={surveyInfo.isDeclinable ? "130px" : "75px"}>
              <Text.P1
                className=""
                style={{ minWidth: surveyInfo.isDeclinable ? 105 : 75 }}
              >
                {parameters.status === "DECL" ? (
                  <OverlayTrigger
                    key="bottom"
                    placement="bottom"
                    overlay={
                      <Tooltip id="`tooltip-bottom" className="fs-10">
                        Reinstate this person
                      </Tooltip>
                    }
                  >
                    <span className="d-inline-block">
                      <Button
                        variant="link"
                        className="p-0 m-0"
                        onClick={() =>
                          onPairReinstate(
                            item.survey,
                            item.id,
                            item.survey_response || ""
                          )
                        }
                      >
                        <Icon icon="far fa-plus-square fs-16" />
                      </Button>
                    </span>
                  </OverlayTrigger>
                ) : (
                  <>
                    <Button
                      variant="link"
                      className="p-0 mt-0 ml-3 mr-5"
                      onClick={() => {
                        if (shouldNotStartSurvey()) return;
                        onSurveyStart(
                          item.survey,
                          item.id,
                          item.survey_response
                        );
                      }}
                    >
                      <OverlayTrigger
                        key="bottom"
                        placement="bottom"
                        overlay={
                          <Tooltip id="tooltip-bottom" className="fs-10">
                            Rate this person
                          </Tooltip>
                        }
                      >
                        <span className="d-inline-block">
                          <Icon icon="fas fa-arrow-right" />
                        </span>
                      </OverlayTrigger>
                    </Button>
                    {surveyInfo.isDeclinable && (
                      <OverlayTrigger
                        key="bottom"
                        placement="bottom"
                        overlay={
                          <Tooltip id="`tooltip-bottom" className="fs-10">
                            Decline to rate
                          </Tooltip>
                        }
                      >
                        <span className="d-inline-block">
                          <Button
                            variant="link"
                            className="p-0 m-0"
                            onClick={() => {
                              setShowDecline({
                                pairing: item.id,
                                survey: item.survey,
                                responseID: item.survey_response,
                                name: `${
                                  item.target.first_name.length > 0 ||
                                  item.target.last_name.length > 0
                                    ? item.target.first_name +
                                      " " +
                                      item.target.last_name
                                    : "-"
                                }`,
                                status: true,
                              });
                            }}
                          >
                            <ICONS.CrossButton width={20} height={20} />
                          </Button>
                        </span>
                      </OverlayTrigger>
                    )}
                  </>
                )}
              </Text.P1>
            </Table.Data>
          )}
        </Table.Row>
      );
    });
  };

  const getColumns = () => {
    const action = () => {
      if (parameters?.status === "ADD") {
        return [];
      }
      return [{ key: 7, label: "Actions", hasSort: false }];
    };
    const metasColums = () => {
      // var arr: any = [];
      // metas.map((item: any) => {
      //   if (item.key) {
      //     return arr.push({ key: item.key, label: item.title, hasSort: true });
      //   } else return 0;
      // });
      return Object.keys(filtersState.filters).map((item) => {
        const value = util.lib.get(filtersState.filters, item);
        return { key: item, label: value.label, hasSort: false };
      });
    };

    const basic = [
      {
        key: 1,
        renderItem: () => (
          <FormGroup className="pb-1">
            <FormControl.Checkbox>
              <FormControl.Checkbox.Input
                checked={selectAll}
                // onChange={(evt: any) => console.log(users)}
                onChange={(evt: any) => onCheckAll(evt.target.checked)}
              />
            </FormControl.Checkbox>
          </FormGroup>
        ),
        hasSort: false,
      },
      { key: 2, label: "No.", hasSort: false },
    ];

    return [
      ...basic,
      {
        key: 3,
        label: "Name",
        sortKey: "first_name",
        hasSort: true,
        sortValue: "asc",
      },
      ...metasColums(),
      // { key: 5, label: "Location", hasSort: true },
      // { key: 6, label: "Department", hasSort: true },
      ...action(),
    ];
  };

  const isSelected = () => {
    if (
      selected.length &&
      (parameters.status === "TODO" ||
        parameters.status === "PROG" ||
        parameters.status === "SUBM" ||
        parameters.status === "DECL")
    ) {
      return (
        <div className="d-flex flex-direction-row align-items-center justify-content-end col-12 col-md-5">
          <Text.P1 className="text-primary">
            {selected.length} User{selected.length > 1 ? "s" : ""} Selected
          </Text.P1>
          {parameters.status !== "DECL" ? (
            <>
              {surveyInfo.isDeclinable && (
                <Button
                  onClick={() => setShowMultiDecline(true)}
                  className="ml-2"
                  variant="outline-danger"
                  disabled={!surveyInfo.isDeclinable}
                >
                  Decline
                </Button>
              )}
            </>
          ) : (
            <Button
              className="ml-2"
              variant="primary"
              disabled={selectedUserMeta.length === 0}
              onClick={onMultiPairReinstate}
            >
              Reinstate
            </Button>
          )}
        </div>
      );
    } else if (selected.length && parameters.status === "ADD") {
      return (
        <div className="d-flex flex-direction-row align-items-center justify-content-end mr-3">
          <Text.H3 className="text-primary">
            {selected.length} User{selected.length > 1 ? "s" : ""} Selected
          </Text.H3>
          <Button
            variant="primary"
            className="ml-2"
            size="lg"
            onClick={onPairAdded}
          >
            Add
          </Button>
        </div>
      );
    }
    return <></>;
  };

  const onSearch = (e: any) => {
    setParameters((p: any) => ({ ...p, search: e.target.value }));
  };

  const getFilterLayout = () => {
    return (
      <>
        <Div className="row justify-content-between">
          {/* <Layout.Col xl={9} lg={8} md={12}>
            <ComboFilter
              filters={filtersState.filters}
              selected={filtersState.selected}
              onFilterSelect={(_selected: any) => {
                filtersState.onSelect(_selected);
                getUsers(1, _selected);
              }}
              onSubmit={() => {
                // getUsers(1, filtersState.selected);
              }}
            />
          </Layout.Col>
          <Layout.Col className="py-1 px-0" xl={3} lg={4} md={4} xs={12}>
            {isSelected()}
          </Layout.Col> */}
          <Div className="col-md-6 row my-2 mx-0">
            <Icon className="mr-2 mt-2" icon="fas fa-filter " />
            <FormGroup className="mb-0">
              <FormControl.Select
                value={Object.keys(filtersState.selected || {})[0]}
                placeholder="Select filter"
              >
                {Object.entries(surveyFilters).map(([k, v]: any) => (
                  <FormControl.SelectItem
                    key={k}
                    onSelect={() => {
                      filtersState.onSelect({ [k]: [] });
                      (
                        multiSelectInputRef?.current as any
                      )?.select?.clearValue?.();
                      getUsers(1, {});
                    }}
                  >
                    {v.label}
                  </FormControl.SelectItem>
                ))}
              </FormControl.Select>
            </FormGroup>
            <Div className="pr-0 col" style={{ width: "100%", maxWidth: 240 }}>
              <MultiSelect
                key={(Object.keys(filtersState.selected) as any)[0]}
                ref={multiSelectInputRef}
                options={(
                  surveyFilters[
                    (Object.keys(filtersState.selected) as any)[0]
                  ] || { values: [] }
                ).values.map((sF: any) => ({
                  value: sF,
                  label: sF,
                }))}
                placeholder="All"
                className={`${media.width >= 768 ? "ml-2" : ""}`}
                onSelect={(selected: any) => {
                  const relevantFilter = (
                    Object.keys(filtersState.selected) as any
                  )[0];
                  const params = {
                    [relevantFilter]: selected.map((s: any) => s.value),
                  };
                  filtersState.onSelect(params);
                  getUsers(1, params);
                }}
              />
            </Div>
          </Div>
          <Div className="col-md-6 my-2 mx-0 row justify-content-end pr-3">
            {media.width >= 768 ? isSelected() : ""}
            <Div className="col-12 col-md-5 px-0 mx-0 mb-2 pt-2">
              <FormControl.Search
                placeholder="Search for name, email or emp id"
                value={parameters.search || ""}
                onChange={onSearch}
                style={{
                  maxWidth: media.width >= 768 ? "20rem" : "100%",
                  width: "100%",
                }}
              />
            </Div>
            {media.width <= 768 ? isSelected() : ""}
          </Div>
        </Div>
      </>
    );
  };

  return (
    <Div>
      {showSidebar || media.width >= 768 ? (
        <>
          <Layout.Sidebar
            className={`bg-white border-right ${
              media.width >= 768 ? "" : "position-fixed"
            }`}
            hasHeader
            style={{ marginLeft: layout.sidebar.marginLeft, zIndex: 999 }}
            width={layout.sidebar.width}
          >
            <Div>
              <BackToDashboard />
              {/* filters={filters} */}
              <SurveyStatus
                toDo={statMap[surveyInfo.id]?.TODO ? statMap[surveyInfo.id]?.TODO : 0}
                inProgress={statMap[surveyInfo.id]?.PROG ? statMap[surveyInfo.id]?.PROG : 0}
                declined={statMap[surveyInfo.id]?.DECL ? statMap[surveyInfo.id]?.DECL : 0}
                completed={statMap[surveyInfo.id]?.SUBM ? statMap[surveyInfo.id]?.SUBM : 0}
                isPairsEditable={surveyInfo.isPairsEditable}
                selected={parameters.status}
                isLoading={isLoading}
                setParameters={(f: any) => {
                  setTableLoading(true);
                  setParameters(f);
                  setIsLoaded(false);
                }}
                parameters={parameters}
                setShowSidebar={setShowSidebar}
                isDeclinable={surveyInfo.isDeclinable}
              />
            </Div>

            {/* <Filters /> */}
          </Layout.Sidebar>
          {showSidebar || media.width <= 768 ? (
            <Overlay onClick={() => setShowSidebar(false)} />
          ) : (
            ""
          )}
        </>
      ) : (
        // <Div
        //   className="px-3 py-2 position-fixed bg-white shadow border cursor-pointer"
        //   style={{ top: 60, left: 0, zIndex: 999 }}
        //   onClick={() => setShowSidebar(true)}
        // >
        //   <Icon icon="far fa-bars" />
        // </Div>
        <></>
      )}
      <Div
        style={{
          marginLeft: media.width >= 768 ? layout.container.marginLeft : "",
        }}
      >
        <TakeSurveyContainer
          title={`${surveyInfo.title} - ${Method(parameters.status).title}`}
          surveyType={util.enums.Survey.Upward}
          setShowSidebar={setShowSidebar}
          ScrollToBottom={ScrollToBottom}
          color={dashboardUtil.getSurveyColor(util.enums.Survey.Upward)}
        >
          <Layout.Container fluid className="px-0">
            {getFilterLayout()}

            {isTableLoading || users.length >= 1 ? (
              <Table
                columns={isTableLoading ? [] : getColumnsData()}
                isLoading={isTableLoading}
                rows={users}
                customRows
                render={() => getRows()}
                hasPagination
                activePage={tableMeta.page}
                pages={tableMeta.totalPages}
                onPageSelect={onPageSelect}
                onSort={(item: any) => {
                  const label = util.label.getSortingLabel(
                    item.sortKey,
                    item.sortValue
                  );
                  setColumnsData((_columns: any) =>
                    tableMeta.resetColumns(_columns, item)
                  );
                  setOrdering(label);
                  getUsers(tableMeta.page, filtersState.selected, label);
                }}
                size={tableMeta.size}
                totalItems={tableMeta.totalItems}
              />
            ) : (
              ""
            )}

            {!isTableLoading && users.length === 0 ? (
              <Div
                className="d-flex flex-column align-items-center"
                style={{ height: "calc(100vh - 120px)", padding: "150px 0" }}
              >
                <img src={data.PAIRICON} alt="" />
                <Text.InputLabel
                  className="pb-4 text-center text-secondary"
                  style={{ margin: 30, maxWidth: 600 }}
                >
                  There are no items to show.{" "}
                  {surveyInfo.isPairsEditable ? (
                    <>
                      <Button
                        variant="link"
                        onClick={() =>
                          setParameters({ ...parameters, status: "ADD" })
                        }
                        className="px-0"
                      >
                        Click here
                      </Button>{" "}
                      add a new reviewee.
                    </>
                  ) : (
                    "Please contact your admin to add a new reviewee."
                  )}
                </Text.InputLabel>
              </Div>
            ) : (
              ""
            )}
          </Layout.Container>
        </TakeSurveyContainer>
      </Div>
      <Modal
        show={showDecline.status}
        size="md"
        centered
        animation={false}
        onHide={() =>
          setShowDecline({
            pairing: 0,
            name: "",
            status: false,
            survey: 0,
            responseID: null,
          })
        }
      >
        <Modal.Header closeButton>
          <Modal.Title>Decline Pairing</Modal.Title>
        </Modal.Header>
        <Modal.Body size="sm">
          <Text.P1>Are you sure you want to decline this pairing?</Text.P1>
          <Text.P1 className="font-weight-bold mt-3">
            {showDecline.name} will be moved to declined list.
          </Text.P1>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="outline-danger" size="sm" onClick={onDecline}>
            Decline
          </Button>
        </Modal.Footer>
      </Modal>
      <Modal
        show={showMultiDecline}
        animation={false}
        size="md"
        centered
        onHide={() => setShowMultiDecline(false)}
      >
        <Modal.Header closeButton>
          <Modal.Title>Decline Pairing</Modal.Title>
        </Modal.Header>
        <Modal.Body size="sm">
          <Text.P1>Are you sure you want to decline this pairing?</Text.P1>
          <Text.P1 className="font-weight-bold mt-3">
            {selected.length} target{selected.length > 1 ? "s" : ""} will be
            moved to declined list.
          </Text.P1>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="outline-danger" size="sm" onClick={onMultiDecline}>
            Decline
          </Button>
        </Modal.Footer>
      </Modal>
    </Div>
  );
};

export default Pairings;
const Overlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(6px);
  z-index: 998;
  height: 100vh;
  width: 100vw;
  cursor: pointer;
`;
