import React from "react";
// Badge
import {
  // Card,
  Div,
  Text,
  Layout,
  // Span,
  // Icon,
  PageContainer,
  Placeholder,
  Table,
  ComboBasicFilter,
  FormControl,
} from "unmatched/components";
// import styled from "styled-components";
import CustomHeader from "pages/AdminApp/Shared/CustomHeader/CustomHeader";
import appUrls from "unmatched/utils/urls/app-urls";
import icons from "assets/icons/icons";
import { useDebounce, useTable } from "unmatched/hooks";
import TABS from "../tabs.interface";
import {
  addUserToExitSurvey,
  deleteUserToExitSurvey,
  updateUserToExitSurvey,
  getUserExitSurvey,
} from "../../CreateSurvey/AddParticipants/PairingsManagement/pairings-api";
import {
  Button,
  DatePicker,
  Dropdown,
  Icon,
  CustomModal as Modal,
  util,
} from "@unmatchedoffl/ui-core";
import { searchUsersFact } from "pages/AdminApp/DataLoad/dataload-api";
import useToastr from "unmatched/modules/toastr/hook";
import { OverlayTrigger, Spinner, Tooltip } from "react-bootstrap";
import {
  downloadParticipantCSV,
  // downloadParticipantReport,
  downloadParticipantReportPDF,
} from "pages/AdminApp/Reports/reports-api";
import { keys, map, get } from "lodash";

const { Paste } = icons;

const Participants = (props: any) => {
  // meta
  // const { survey, layout, globalLoad } = props;
  const { survey, layout, indexId, metas, globalLoad, tab } = props;
  const tableMeta = useTable({});

  const toast = useToastr();
  const [metaFields, setMetaFields] = React.useState([
    {
      title: "",
      key: "",
      value: [],
    },
  ]);
  const [selectedMeta, setSelectedMeta] = React.useState<any>({
    cohert: "",
    applied: undefined,
    isSet: false,
  });

  const [modifyDate, setModifyDate] = React.useState<any>({
    id: null,
    date: null,
  });

  const [deleteModal, setDeleteModal] = React.useState<any>({
    show: false,
    id: null,
  });

  const [users, setUsers] = React.useState([]);
  const [surveyDate, setSurveyDate] = React.useState(null);
  const [selectedUsers, setSeletedUsers] = React.useState<any[]>([]);
  const [selectedUser, setSeletedUser] = React.useState<any>(null);
  const [addedUsers, setAddedUsers] = React.useState<any>([]);
  const [search, setSearch] = React.useState<string | null>(null);
  const [searchAllUser, setSearchAllUser] = React.useState<string | null>(null);
  const [apiLoading, setAPILoading] = React.useState(false);
  const [show, setShow] = React.useState(false);
  const [ordering, setOrdering] = React.useState<string>('');


  const getColumns = () => {
    const coumnsList = keys(columnsData);
    return map(coumnsList, (key: string) => ({
      ...get(columnsData, key),
      key,
    }));
  };

  const apiCall = async (orderin?: any) => {
    if (survey.type === util.enums.Survey.Engagement) {
      return;
    }
    const users = await getUserExitSurvey({
      index: indexId,
      [selectedMeta.cohert]: selectedMeta.applied,
      search,
      ...(orderin && { ordering: orderin })
    });
    setAddedUsers(users.data);
    tableMeta.setLoading(false);
  };

  useDebounce(
    () => {
      if (selectedMeta.applied === undefined && selectedMeta.isSet === false)
        return;
      apiCall();
      // itemCall();
    },
    1000,
    [selectedMeta, search]
  );

  const onDateUpdated = async () => {
    const deadline =
      util.date.getFormatedTime(modifyDate.date, "yyyy-MM-dd") + "T00:00:00Z";

    try {
      await updateUserToExitSurvey(modifyDate.id, {
        deadline,
        index: indexId,
      });
      setModifyDate({
        date: null,
        id: null,
      });
      let users = addedUsers;
      users = users.map((user: any) => {
        if (user.id === modifyDate.id) {
          return { ...user, deadline };
        } else {
          return user;
        }
      });
      setAddedUsers(users);
      toast.onSucces("Deadline updated successfully.");
    } catch (error) {
      toast.errorToast(error);
    }
  };

  React.useEffect(() => {
    tableMeta.setLoading(true);
    if (!globalLoad) {
      setMetaFields(metas);
      apiCall();
      setSelectedMeta({
        ...selectedMeta,
        cohert: metas[0]?.key || "",
        isSet: true,
      });
    }
    //eslint-disable-next-line
  }, [metas, globalLoad]);

  React.useEffect(() => {
    if (!globalLoad && selectedMeta.isSet && tab === TABS.Participant) {
      // getReportCount();
    }
    //eslint-disable-next-line
  }, [metas, selectedMeta, globalLoad, tab]);

  const findUser = async (search: string | null) => {
    const _d = await searchUsersFact("", { search });
    setUsers(_d.data.results);
  };

  const userAddAPI = async () => {
    // if (!selectedUser.id) {
    //   return toast.errorToast("No user selected.");

    let call = 1;
    let _users = [...selectedUsers];
    setAPILoading(true);
    while (call <= selectedUsers.length + 1) {
      if (call === selectedUsers.length + 1) {
        setAPILoading(false);
        apiCall();
        setShow(false);
        return setSeletedUsers([]);
      }
      const _c = call - 1;
      try {
        const deadline =
          util.date.getFormatedTime(selectedUsers[_c].deadline, "yyyy-MM-dd") +
          "T00:00:00Z";
        await addUserToExitSurvey({
          index: indexId,
          rater: selectedUsers[call - 1].user.id,
          deadline,
        });
        _users = _users.map((_user: any, i: number) => {
          if (i === _c) {
            return { ..._user, state: 2 };
          }
          return _user;
        });
        // console.log(_users);
        setSeletedUsers(_users);
        call++;
      } catch (error) {
        // setAPILoading(false);
        _users = _users.map((_user: any, i: number) => {
          if (i === _c) {
            return { ..._user, state: 3 };
          }
          return _user;
        });
        setSeletedUsers(_users);
        console.log(error);
        call++;
        toast.onError(error);
      }
    }
  };

  const removeUser = async (userID: number) => {
    try {
      await deleteUserToExitSurvey(userID, { index: indexId });
      let users = [...addedUsers];
      users = users.filter((_item) => {
        return _item.id !== userID;
      });
      setAddedUsers(users);
      setDeleteModal({
        show: false,
        id: null,
      });
      toast.onSucces("Deleted successfully.");
    } catch (error) {
      toast.onError(error);
    }
  };

  const removeTempUsers = async (userID: number) => {
    let users = [...selectedUsers];
    users = users.filter((_item) => {
      return _item.user.id !== userID;
    });
    setSeletedUsers(users);
  };

  useDebounce(
    () => {
      if (searchAllUser !== null) {
        findUser(searchAllUser);
      } else {
        return;
      }
    },
    200,
    [searchAllUser]
  );

  const getColumnsData = () => {
    return [
      // {
      //   key: 1,
      //   renderItem: getCheckAllTemplate,
      //   hasSort: false,
      // },
      { key: 1, label: "No.", hasSort: false },
      // { key: 3, label: getCohertSelected(selectedMeta.cohert), hasSort: false },
      { key: 2, label: "First Name", hasSort: true, sortKey: "rater__first_name" },
      { key: 3, label: "Last Name", hasSort: true, sortKey: "rater__last_name" },
      { key: 4, label: "Email", hasSort: true, sortKey: "email" },
      { key: 5, label: "End Date", hasSort: true, sortKey: "deadline" },
      { key: 6, label: "Completion", hasSort: false },
      { key: 8, label: "Submission Status", hasSort: false },
      { key: 7, label: "Actions", hasSort: false },
    ];
  };

  const [columnsData, setColumnsData] = React.useState<any>(getColumnsData()); // deepscan-disable-line REFERENCE_BEFORE_LEXICAL_DECL

  const status = (sts: string) => {
    const actions: any = {
      TODO: "To Do",
      PROG: "In Progress",
      SUBM: "Completed",
    };

    return actions[sts] ?? "";
  };

  const getRowsTemplate = () => {
    return addedUsers.map((item: any, index: number) => {
      // const checked = checkSelected(item);
      const isEven = index % 2 === 0 || index === 0;
      return (
        <Table.Row even={!isEven} key={item.id}>
          {/* <Table.Data width="30px">
            <FormGroup>
              <FormControl.Checkbox>
                <FormControl.Checkbox.Input
                  onChange={() => onSelectDept(item)}
                  onClick={(evt: any) => evt.stopPropagation()}
                  checked={checked}
                />
              </FormControl.Checkbox>
            </FormGroup>
          </Table.Data> */}
          <Table.Data width="70px">
            <Text.P1>{tableMeta.page * 10 - 10 + index + 1}.</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.rater_details.first_name}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.rater_details.last_name}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.rater_details.email}</Text.P1>
          </Table.Data>
          <Table.Data>
            {item.id === modifyDate.id ? (
              <>
                <DatePicker
                  selected={modifyDate.date}
                  onSelect={(date: Date) => date}
                  onChange={(date: Date) =>
                    setModifyDate({
                      date,
                      id: item.id,
                    })
                  }
                  customInput={
                    <FormControl.Date placeholder="date" className="bg-white" />
                  }
                />
                <Button
                  variant="link"
                  className="pt-2 px-2"
                  onClick={onDateUpdated}
                >
                  <Icon icon="far fa-check text-success" />
                </Button>
                <Button
                  variant="link"
                  className="pt-2 px-2"
                  onClick={() =>
                    setModifyDate({
                      date: null,
                      id: null,
                    })
                  }
                >
                  <Icon icon="far fa-times-circle text-danger" />
                </Button>
              </>
            ) : (
              <Text.P1>
                {util.date.getBrowserTime(item.deadline, "dd MMM, yyyy")}
                <Button
                  variant="link"
                  className="pt-0 px-2"
                  onClick={() =>
                    setModifyDate({
                      // ...modifyDate,
                      date: new Date(item.deadline),
                      id: item.id,
                    })
                  }
                >
                  <Icon icon="far fa-pencil" />
                </Button>
              </Text.P1>
            )}
          </Table.Data>
          <Table.Data>
            <Text.P1>{item?.completion?.toString()}%</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{status(item.status)}</Text.P1>
          </Table.Data>
          <Table.Data>
            {/* <Text.P1>-</Text.P1> */}
            <Div className="position-relative">
              <Button
                variant="link"
                className="p-0 fs-16 text-danger"
                onClick={
                  () => {
                    setDeleteModal({
                      show: true,
                      id: item.id,
                    });
                  } //removeUser(item.id)
                }
              >
                <Icon icon="far fa-times-circle" />
              </Button>
              {(item.status === "PROG" || item.status === "SUBM") &&
                new Date() > new Date(item.deadline) &&
                survey.type === "SurveyIndexExit" && (
                  <OverlayTrigger
                    key="bottom"
                    placement="bottom"
                    overlay={
                      <Tooltip id="tooltip-bottom">
                        Download XLSX Data.
                      </Tooltip>
                    }
                  >
                    <div className="d-inline">
                      <Button
                        type="button"
                        className="py-1 text-muted"
                        onClick={() => {
                          downloadParticipantCSV(
                            {},
                            {},
                            item.id,
                            `${survey.name}_${item.rater_details?.last_name}_${item.rater_details?.first_name}`
                          );
                        }}
                        variant="link"
                      >
                        <Icon icon="fal fa-file-excel" />
                      </Button>
                    </div>
                  </OverlayTrigger>
                )}
              {(item.status === "PROG" || item.status === "SUBM") &&
                new Date() > new Date(item.deadline) &&
                survey.type === "SurveyIndexExit" && (
                  <OverlayTrigger
                    key="bottom"
                    placement="bottom"
                    overlay={
                      <Tooltip id="tooltip-bottom">
                        Download PDF Report.
                      </Tooltip>
                    }
                  >
                    <div className="d-inline">
                      <Button
                        className="py-1 text-muted px-1"
                        onClick={() => {
                          downloadParticipantReportPDF(
                            {},
                            {
                              survey_index: indexId,
                              rater: item.rater,
                              fileName: `${survey.name}_${item.rater_details?.last_name}_${item.rater_details?.first_name}`,
                            }
                          );
                        }}
                        variant="link"
                      >
                        <Icon icon="fal fa-file-pdf" />
                      </Button>
                    </div>
                  </OverlayTrigger>
                )}
            </Div>
          </Table.Data>
        </Table.Row>
      );
    });
  };

  const getCohertSelected = (cohert: string) => {
    if (cohert === undefined || cohert === null) {
      return "";
    }
    return (
      metaFields.filter((meta: any) => meta.key === cohert)[0]?.title ?? ""
    );
  };

  const getCohertValues = (cohert: string) => {
    if (cohert === undefined || cohert === null) {
      return [];
    }
    return (
      metaFields.filter((meta: any) => meta.key === cohert)[0]?.value ?? []
    );
  };
  const filters = {
    showUnflagged: false,
    cohart: {
      options: metaFields,
      selected: getCohertSelected(selectedMeta.cohert),
    },
    applied: {
      options: getCohertValues(selectedMeta.cohert),
      selected: selectedMeta.applied,
    },
  };

  return (
    <>
      <Div className="Single-Survey">
        <CustomHeader
          style={{ marginLeft: layout.marginLeft }}
          title={
            <Layout.Flex>
              <Layout.FlexItem>
                <Text.H1 className="pb-2">
                  {globalLoad ? <Placeholder width="col-12" /> : survey.name}
                </Text.H1>
              </Layout.FlexItem>
              <Layout.FlexItem className="pl-2">
                {/* <Badge variant={meta.variant}>{meta.title}</Badge> */}
              </Layout.FlexItem>
            </Layout.Flex>
          }
          breadcrumbs={[
            {
              label: "Surveys",
              icon: <Paste className="grey-icon__svg" />,
              route: appUrls.admin.survey.default,
            },
            { label: "Manage Survey" },
            { label: survey.name },
          ]}
        />
        <PageContainer className="pt-4 px-4">
          <Layout.Row>
            <Layout.Col xl={5} lg={7} md={6}>
              <ComboBasicFilter
                cohart={filters.cohart}
                applied={filters.applied}
                onCohartUpdate={(e: string) =>
                  setSelectedMeta({
                    ...selectedMeta,
                    cohert: e,
                    applied: undefined,
                  })
                }
                onAppliedUpdate={(e: any) =>
                  setSelectedMeta({ ...selectedMeta, applied: e.title })
                }
                isAppliedShown={true}
              />
            </Layout.Col>
            <Layout.Col
              xl={7}
              lg={5}
              md={6}
              className="d-flex justify-content-end"
            >
              <Div style={{ maxWidth: 300, width: "100%" }}>
                <FormControl.Search
                  placeholder="Search for name, email or emp id"
                  value={search}
                  onChange={(evt: any) => setSearch(evt.target.value)}
                  // onSearch={() => void}
                />
              </Div>
              <Button
                variant="outline-primary"
                className="ml-2"
                onClick={() => setShow(true)}
              >
                Add Participants
              </Button>
            </Layout.Col>
          </Layout.Row>
          <Div className="pt-4">
            <Table
              columns={getColumns()}
              isLoading={tableMeta.isLoading}
              rows={addedUsers}
              customRows
              render={() => getRowsTemplate()}
              hasPagination
              activePage={tableMeta.page}
              pages={tableMeta.totalPages}
              onPageSelect={(d: any) => {
                tableMeta.onPageSelect(d);
                // getReportCount(d);
              }}
              onSort={(item: any) => {
                const label = util.label.getSortingLabel(
                  item.sortKey,
                  item.sortValue
                );
                setColumnsData((_columns: any) =>
                  tableMeta.resetColumns(_columns, item)
                );
                setOrdering(label);
                apiCall(label);
              }}
            />
          </Div>
        </PageContainer>
      </Div>
      <Modal
        // {...props}
        onHide={() => setShow(false)}
        centered
        size="lg"
        show={show}
        children={
          <>
            <Modal.Header closeButton>
              <Modal.Title>Add Participants</Modal.Title>
            </Modal.Header>
            <>
              <Modal.Body>
                <Div className="row" style={{ maxWidth: 750 }}>
                  <Div className="col-5 position-relative">
                    <Text.P1 className="pb-2 text-muted">
                      Email address of participant
                    </Text.P1>
                  </Div>
                  <Div className="col-4">
                    <Text.P1 className="pb-2 text-muted">
                      Keep open till
                    </Text.P1>
                  </Div>
                  <Div className="col-3 pt-4"></Div>
                </Div>
                {selectedUsers.map((_sU: any) => (
                  <Div
                    className="row"
                    style={{ maxWidth: 750 }}
                    key={_sU.user.id}
                  >
                    {/* {JSON.stringify(_sU)} */}
                    <Div className="col-5 position-relative">
                      <Text.P1 className="py-2">{_sU.user.email}</Text.P1>
                    </Div>
                    <Div className="col-2">
                      <Text.P1 className="py-2">
                        {util.date.getFormatedTime(
                          _sU.deadline,
                          "dd MMM, yyyy"
                        )}
                      </Text.P1>
                    </Div>
                    <Div className="col-4 pt-1">
                      {!apiLoading && _sU.state === 0 && (
                        <Button
                          variant="link"
                          className="p-0 text-danger"
                          onClick={() => removeTempUsers(_sU.user.id)}
                        >
                          <Icon icon="far fa-times-circle mr-2" />
                          <span className="fs-12">Remove</span>
                        </Button>
                      )}
                      {apiLoading === true &&
                        _sU.state !== 2 &&
                        _sU.state !== 3 && (
                          <Spinner animation="border" size="sm" />
                        )}

                      {_sU.state === 2 && (
                        <Icon icon="far fa-check-circle text-success" />
                      )}
                      {_sU.state === 3 && (
                        <Icon icon="far fa-exclamation-circle text-danger" />
                      )}
                    </Div>
                  </Div>
                ))}
                <Div className="row pt-4" style={{ maxWidth: 750 }}>
                  <Div className="col-5 position-relative">
                    <Dropdown style={{ position: "static" }}>
                      <Dropdown.Toggle
                        variant="link"
                        className="px-0 py-0 mb-3 w-100"
                        id="dropdown-basic"
                        disabled={false}
                      >
                        <FormControl.Text
                          type="text"
                          onChange={(e: any) =>
                            setSearchAllUser(e.target.value)
                          }
                          placeholder="Enter name or email id"
                          value={searchAllUser}
                        />
                      </Dropdown.Toggle>

                      {users.length > 0 && (
                        <Dropdown.Menu
                          className="shadow w-100 mt-2 py-0"
                          style={{ zIndex: 999 }}
                          align="right"
                        >
                          {users.map((item: any, i: number) => (
                            <Dropdown.Item
                              key={i}
                              className={`py-2 fs-12 fw-300 d-block`}
                              as={Button}
                              onClick={() => {
                                setSeletedUser(item);
                                setSearchAllUser(item.email);
                              }}
                            >
                              {item.email}
                            </Dropdown.Item>
                          ))}
                        </Dropdown.Menu>
                      )}
                    </Dropdown>
                  </Div>
                  <Div className="col-3">
                    <DatePicker
                      minDate={new Date()}
                      selected={surveyDate}
                      onChange={(date: any) => {
                        setSurveyDate(date);
                      }}
                      className="w-100"
                      placeholderText={"Choose Date"}
                      customInput={
                        <FormControl.Date placeholder={"Choose Date"} />
                      }
                    />
                  </Div>
                  <Div className="col-4">
                    <Button
                      disabled={
                        !selectedUser?.id || surveyDate === null || apiLoading
                      }
                      onClick={() => {
                        setSeletedUsers([
                          ...selectedUsers,
                          {
                            user: selectedUser,
                            deadline: surveyDate,
                            state: 0,
                          },
                        ]);
                        setSeletedUser({});
                        setSurveyDate(null);
                        setSearch("");
                      }}
                      variant="light"
                      className="border p-1 fs-18"
                    >
                      <Icon icon="far fa-plus-square" />
                    </Button>
                    {/* <Spinner animation="border" role="status" size="sm" /> */}
                  </Div>
                </Div>
              </Modal.Body>
              <Modal.Footer>
                <Div className="w-100 d-flex justify-content-end">
                  <Button
                    // className="float-right"
                    type="button"
                    variant="primary"
                    onClick={userAddAPI}
                    disabled={selectedUsers.length === 0 || apiLoading}
                  >
                    {"Send Invites"}
                  </Button>
                </Div>
              </Modal.Footer>
            </>
          </>
        }
      />
      <Modal
        // {...props}
        onHide={() =>
          setDeleteModal({
            show: false,
            id: null,
          })
        }
        centered
        show={deleteModal.show}
      >
        <>
          <Modal.Header closeButton>
            <Modal.Title>Delete Participant</Modal.Title>
          </Modal.Header>
          <>
            <Modal.Body size="sm">
              <Text.P1>Are you sure to delete?</Text.P1>
            </Modal.Body>
            <Modal.Footer>
              <Div className="w-100 d-flex justify-content-end">
                <Button
                  type="button"
                  variant="primary-outline"
                  onClick={() =>
                    setDeleteModal({
                      show: false,
                      id: null,
                    })
                  }
                >
                  Cancel
                </Button>
                <Button
                  // className="float-right"
                  type="button"
                  variant="danger"
                  onClick={() => removeUser(deleteModal.id)}
                >
                  Delete
                </Button>
              </Div>
            </Modal.Footer>
          </>
        </>
      </Modal>
    </>
  );
};

// SurveyTimeline.propTypes = {

// }

export default Participants;
