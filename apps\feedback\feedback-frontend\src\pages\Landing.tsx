import React, { useEffect } from "react";
import useSession from "unmatched/modules/session/hook";
import { Loader } from "unmatched/components";
import util from "unmatched/utils";
import { useHistory } from "react-router";
// import PropTypes from 'prop-types'

const Landing = () => {
  const { user, isLoggedIn } = useSession();
  const history = useHistory();

  useEffect(() => {
    if (!isLoggedIn() && util.env.isProd) {
      window.location.href = window.location.origin;
    } else if (user && user.role) {
      if (user.role === util.enums.Roles.ADMIN) {
        history.push(util.appUrls.admin.default);
      } else {
        history.push(util.appUrls.user.default);
      }
    }
  }, [user]);

  return <Loader text="Loading Session" />;
};

Landing.propTypes = {};

export default Landing;
