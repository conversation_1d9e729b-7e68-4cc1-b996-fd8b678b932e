// import * as yup from "yup";

import { z } from "zod";

const hasNumber = new RegExp("^(?=.*[0-9]).+$");
const hasAlphabet = new RegExp("^(?=.*[a-zA-Z]).+$");

// const passowrdCheck = yup
//   .string()
//   .required()
//   .min(8, "At least 8 characters")
//   .matches(hasNumber, "At least 1 letter")
//   .matches(hasAlphabet, "At least 1 number");

// const passowrdCheck  = z.object({
//   password: z.string().min(8),
//   confirmPassword: z.string().min(8),
// }).refine((data) => data.password === data.confirmPassword, {
//   message: "Passwords don't match",
//   path: ["confirmPassword"],
// });

const passwordSchema = z
  .string()
  .min(8, { message: "At least 8 characters" })
  .max(20, { message: "At most 20 characters" })
  .refine((password) => /[A-Z]/.test(password), {
    message: "At least 1 uppercase letter",
  })
  .refine((password) => /[a-z]/.test(password), {
    message: "At least 1 lowercase letter",
  })
  .refine((password) => /[0-9]/.test(password), { message: "At least 1 number" })
  ;

export interface PasswordType {
  password: string;
  confirmPassword: string;
}

export const validatePassword = (password: string) => {
  return {
    number: hasNumber.test(password),
    alphabet: hasAlphabet.test(password),
    min: password.length >= 8,
  };
};

// export default yup.object().shape({
//   password: passowrdCheck,
//   confirmPassword: yup
//     .string()
//     .required("Enter a valid password")
//     .oneOf(
//       [yup.ref("password"), null],
//       "Passwords do not match. Enter the same password as above"
//     ),
// });
export default z.object({
  password: passwordSchema,
  confirmPassword: passwordSchema,
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});


// export const ChangePasswordSchema = yup.object().shape({
//   oldPassword: yup.string().required("Old password is required."),
//   password: passowrdCheck.test("is-same", "Cannot be same as old paswword", function (pass): any {
//     const { oldPassword } = this.parent;
//     return oldPassword !== pass;
//   }),
//   confirmPassword: yup
//     .string()
//     .required("Enter a valid password")
//     .oneOf(
//       [yup.ref("password"), null],
//       "Passwords do not match. Enter the same password as above"
//     ),
// });

export const ChangePasswordSchema = z.object({
  oldPassword: passwordSchema,
  password: passwordSchema,
  confirmPassword: passwordSchema,
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
}).refine((data) => data.oldPassword !== data.password, {
  message: "Cannot be same as old paswword",
  path: ["password"],
});