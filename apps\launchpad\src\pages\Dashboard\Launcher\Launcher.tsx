import useSession from "@/unmatched/modules/session/hook";
import { Card } from "@repo/ui/components/card";
import { Text } from "@repo/ui/components/Text";
import { useEffect, useState } from "react";
import Pattern1 from "@/assets/icons/circlepattern1.svg";
import { Apps } from "../apps";
import { Handshake } from "lucide-react";

const AppCard = (props: any) => {
  return (
    <Card
      className="cursor-pointer overflow-hidden hover:scale-[1.02] transition-all duration-500 rounded-sm border border-black/10 relative"
      onClick={props.onNavigate}
      style={{ backgroundColor: props.bgColor, minHeight: "200px" }}
    >
      {/* <div className="flex w-full h-full"> */}
        <div className="  absolute z-[999] top-6 left-6">
          <Text.H2 className="text-black text-[1.6em] font-medium">
            {props.title}
          </Text.H2>
          <Text.P1 className="pt-2 text-black">{props.content}</Text.P1>
        </div>
        {/* <div className="w-full h-[120px]"> */}
          <div
            className="flex justify-center items-center absolute z-[999] -right-[80px] -top-[80px]"
            // style={{ top: 0, right: 0 }}
          >
            <img
              src={Pattern1}
              className="relative h-[250px] w-full"
              style={{ opacity: 0.05 }}
            />
            <div
              style={{ width: 60, height: 60 }}
              className="rounded-full  bg-primary-foreground flex justify-center items-center absolute shadow-xl"
            >
              {/* <Handshake /> */}
              {props.icon}
            </div>
          </div>
        {/* </div> */}
      
    </Card>
  );
};

function Launcher() {
  const session = useSession();

  const [appFeatures, setFeatures] = useState<any[]>([]);
  const [isLoaded, setLoaded] = useState(false);

  useEffect(() => {
    const features = session?.client?.features;

    if (features && !isLoaded) {
      setFeatures(
        session?.client?.features
          ?.filter((feature: any) => feature.features.length)
          .map((d: any) => {
            return {
              key: Apps.Feedback,
              title: d.name,
              bgColor: "#C5E4FC",
              content: d.description,
              link: `/${d.slug}`,
              logo: "",
              icon: <Handshake />,
            };
          })
      );
      setLoaded(true);
    }
  }, [session]);

  const filteredFeatures = appFeatures;
  // .filter((item: any) =>
  //   permissions.apps.includes(item.key)
  // );

  const navigate = (link: string) => {
    const constructed = `${window.location.origin}${link}/`;
    window.location.href = constructed;
  };

  return (
    <div className="mt-4 max-w-7xl mx-auto">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredFeatures.map((item: any, id) => {
          return (
            <div key={id} className="p-3">
              <AppCard {...item} onNavigate={() => navigate(item.link)} />
            </div>
          );
        })}
      </div>

      {/* <Layout.Row>
        <Layout.Col className="px-3 py-4" sm={12}>
          <Text.H2 className="fs-16">
            <Icon icon="far fa-rocket" /> Coming Soon!
          </Text.H2>
        </Layout.Col>
        {appComingSoonFeatures.map((item: any) => {
          return (
            <Layout.Col
              key={item.key}
              className="p-3"
              xl={3}
              lg={3}
              md="4"
              sm="6"
              style={{opacity: 0.6}}
            >
              <AppCard {...item}/>
            </Layout.Col>
          );
        })}
      </Layout.Row> */}
    </div>
  );
}

export default Launcher;
