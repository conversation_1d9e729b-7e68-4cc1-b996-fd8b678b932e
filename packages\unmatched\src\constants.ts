const CLIENT_ID = import.meta.env.CLIENT_ID || "0oaclm2yi873gxBpO5d7";
const CALLBACK_PATH = import.meta.env.CALLBACK_PATH || "/oidc/callback";
const ISSUER = import.meta.env.ISSUER || "https://dev-69056397.okta.com/oauth2/default";
const HOST = import.meta.env.HOST || window.location.host;
const REDIRECT_URI = `https://${HOST}${CALLBACK_PATH}`;
const SCOPES = import.meta.env.SCOPES || "openid profile email";
export const oktaConfig = {
  issuer: ISSUER,
  clientId: CLIENT_ID,
  redirectUri: REDIRECT_URI,
  scopes: SCOPES.split(/\s+/),
  disableHttpsCheck: true,
  pkce: false
};