import appUrls from "unmatched/utils/urls/app-urls";
import SurveyList from "./SurveyList/SurveyList";
import AgreeTerms from "./AgreeTerms/AgreeTerms";
import SelefEvaluation from "./SelfEvaluation/SelfEvaluation";
import UpwardReview from "./UpwardReview/UpwardReview";
import Engagment from "./EngagmentSurvey/EngagmentSurvey";
import Exit from "./ExitSurvey/ExitSurvey";

const routes = [
  {
    name: "Survey List",
    path: appUrls.user.dashboard.surveyList,
    isExact: false,
    isPrivate: true,
    component: SurveyList,
  },
  {
    name: "Survey Terms",
    path: appUrls.user.dashboard.getSurveyTermsUrl(":id"),
    isExact: false,
    isPrivate: true,
    component: AgreeTerms,
  },
  {
    name: "Upward Review",
    path: appUrls.user.dashboard.upwardReview.getUrl(":id"),
    isExact: false,
    isPrivate: true,
    component: UpwardReview,
  },
  {
    name: "Engagement",
    path: appUrls.user.dashboard.getEngagmentUrl(":surveyId"),
    isExact: false,
    isPrivate: true,
    component: Engagment,
  },
  {
    name: "Self Evaluation",
    path: appUrls.user.dashboard.getSelfEvaluationUrl(":id"),
    isExact: false,
    isPrivate: true,
    component: SelefEvaluation,
  },
  {
    name: "Exit Survey",
    path: appUrls.user.dashboard.getExitUrl(":id"),
    isExact: false,
    isPrivate: true,
    component: Exit,
  },
];

export default routes;
