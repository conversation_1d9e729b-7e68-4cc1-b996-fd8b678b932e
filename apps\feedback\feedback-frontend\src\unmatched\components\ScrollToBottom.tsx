import React from "react";
import { Div, Icon } from "@unmatchedoffl/ui-core";
import styled from "styled-components";
import * as Scroll from "react-scroll";
import { OverlayTrigger, Tooltip } from "react-bootstrap";

const StyledScrollToTopBottom = styled(Div)`
  background: #fcdce9;
  width: 50px;
  height: 50px;
  position: fixed;
  bottom: 120px;
  right: 16px;
  cursor: pointer;
`;

const IconWrap = styled(Div)`
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const scroll = Scroll.animateScroll;

const ScrollToBottom = () => {
  const [scrollTop, setScrollTop] = React.useState(0);
  const [isBottom, setIsBottom] = React.useState(false);

  React.useEffect(() => {
    const onScroll = (e: any) => {
      setScrollTop(e.target.documentElement.scrollTop);
      setIsBottom(checkIfBottom());
    };
    window.addEventListener("scroll", onScroll);

    return () => window.removeEventListener("scroll", onScroll);
  }, [scrollTop]);

  const checkIfBottom = () => {
    return (
      Math.abs(
        document?.documentElement?.scrollHeight -
          document?.documentElement?.clientHeight -
          document?.documentElement?.scrollTop
      ) < 1
    );
  };

  const scrollTo = () => {
    if (document?.documentElement) {
      scroll[isBottom ? "scrollToTop" : "scrollToBottom"]();
    }
  };

  return (
    <StyledScrollToTopBottom
      onClick={scrollTo}
      className="d-flex align-items-center justify-content-center shadow-sm rounded-circle"
    >
      <OverlayTrigger
        key="top"
        placement="top"
        overlay={
          <Tooltip id="`tooltip-top">
            {`Go to ${isBottom ? "first" : "last"} question`}
          </Tooltip>
        }
      >
        <IconWrap>
          {isBottom ? (
            <Icon icon="fas fa-chevron-up" />
          ) : (
            <Icon icon="fas fa-chevron-down" />
          )}
        </IconWrap>
      </OverlayTrigger>
    </StyledScrollToTopBottom>
  );
};

export default ScrollToBottom;
