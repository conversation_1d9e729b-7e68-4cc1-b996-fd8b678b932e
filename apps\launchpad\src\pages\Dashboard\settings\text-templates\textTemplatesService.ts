import axiosInstance from "@/lib/axios";
import { AxiosResponse } from "axios";

// Define types for API parameters and responses
export interface ContactInfo {
  id: string;
  fullName: string;
  email: string;
  phone: string;
  title: string;
  location: string;
  branch: string;
}

export interface ClientInfo {
  loginText?: string;
  contactText: string;
  contacts: ContactInfo[];
}

/**
 * Get client information including contact text and contact list
 */
export const getClientInfoService = async (): Promise<ClientInfo> => {
  try {
    const response: AxiosResponse = await axiosInstance.get("/staff/customers/info/");

    return {
      loginText: response.data.login_text || "",
      contactText: response.data.contact_verbiage || "",
      contacts: response.data.contact_list || [],
    };
  } catch (error) {
    console.error("Error fetching client info:", error);
    throw error;
  }
};

/**
 * Update client information including contact text and contact list
 */
export const updateClientInfoService = async (data: Partial<ClientInfo>): Promise<ClientInfo> => {
  try {
    const payload: Record<string, any> = {};

    if (data.loginText !== undefined) {
      payload.login_text = data.loginText;
    }

    if (data.contactText !== undefined) {
      payload.contact_verbiage = data.contactText;
    }

    if (data.contacts !== undefined) {
      payload.contact_list = data.contacts;
    }

    const response: AxiosResponse = await axiosInstance.patch(
      "/staff/customers/info/",
      payload,
      {
        headers: {
          "Content-Type": "application/json",
        }
      }
    );

    return {
      loginText: response.data.login_text || "",
      contactText: response.data.contact_verbiage || "",
      contacts: response.data.contact_list || [],
    };
  } catch (error) {
    console.error("Error updating client info:", error);
    throw error;
  }
};
