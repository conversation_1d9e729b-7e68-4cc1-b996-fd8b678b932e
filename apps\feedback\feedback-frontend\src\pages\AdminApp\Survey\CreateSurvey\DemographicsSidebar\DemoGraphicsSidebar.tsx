import React from "react";
// import PropTypes from 'prop-types';
import styled from "styled-components";
// import { useParams } from 'unmatched/hooks';
import { Nav, ScrollToActive } from "../../../../../unmatched/components";
import {
  getDemographicsDataFact,
  getSurveyQuestionsFact,
  getSurveySectionsFact,
} from "../../survey-api";
import { useCreateSurveyContext } from "../Provider";

const StyledNav = styled(Nav)`
  /* max-height: 300px; */
  /* overflow-y: auto; */
  /* overflow-x: hidden; */
`;

const NavItem = styled(Nav.Item)`
  font-size: 12px;
  font-weight: 800;
  color: #2f2f2f !important;
  padding-top: 5px;
  .active {
    background: #ebeff7 !important;
    position: relative;
    border-radius: 0;
    &::after {
      border-bottom: 2px solid #518cff;
      position: absolute;
      content: "";
      z-index: 999;
      width: 10%;
      left: 15px;
      bottom: 0;
    }
  }
`;

const NavLink = styled(Nav.Link).attrs({
  className: "text-muted",
})``;

const DemographicsSidebar = () => {
  const {
    survey,
    isDemoActive,
    setDemographics,
    sectionsData,
    questionsData,
    version,
  } = useCreateSurveyContext();
  const versionId = Number(version.id);

  const onDemographicsChange = (evt: any, flag: boolean) => {
    evt.preventDefault();
    evt.stopPropagation();
    setDemographics(flag);
    if (isDemoActive === flag) return;
    if (flag) {
      getDemographicsDataFact(survey.data.demographicsId).then(
        (response: any) => {
          const { sections, questions } = response;
          // demographics.onSuccess(response);
          sectionsData.setData(sections);
          questionsData.setData(questions);
        }
      );
      // getSections(versionId, flag);
    } else {
      getSections(versionId);
      getQuestions(versionId);
    }
  };

  const getSections = (id: any) => {
    getSurveySectionsFact(id).then((response: any) => {
      sectionsData.onSuccess(response);
    });
  };

  const getQuestions = (id: any) => {
    getSurveyQuestionsFact(id).then((response: any) => {
      questionsData.onSuccess(response);
    });
  };

  return (
    <StyledNav variant="pills">
      <ScrollToActive isActive={isDemoActive}>
        <NavItem>
          <NavLink
            active={isDemoActive}
            onClick={(evt: any) => onDemographicsChange(evt, true)}
          >
            Demographics
          </NavLink>
        </NavItem>
      </ScrollToActive>
      <ScrollToActive isActive={!isDemoActive}>
        <NavItem>
          <NavLink
            active={!isDemoActive}
            onClick={(evt: any) => onDemographicsChange(evt, false)}
          >
            Survey Questions
          </NavLink>
        </NavItem>
      </ScrollToActive>
    </StyledNav>
  );
};

// DemoGraphicsSidebar.propTypes = {}

export default DemographicsSidebar;
