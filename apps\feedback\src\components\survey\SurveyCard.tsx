import { ClockIcon } from './Icons';

interface SurveyCardProps {
  survey: any;
  statsMap: any;
  isOngoing: boolean;
  onViewDetails: (surveyId: string) => void;
}

export const SurveyCard = ({ survey, statsMap, isOngoing, onViewDetails }: SurveyCardProps) => {
  const getDaysLeft = (deadline: string) => {
    const deadlineDate = new Date(deadline);
    const now = new Date();
    return Math.ceil((deadlineDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const progress = statsMap[survey.id]?.percentage_completed || 0;
  const daysLeft = survey.deadline ? getDaysLeft(survey.deadline) : null;
  
  return (
    <div className="bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-shadow">
      <div className="flex flex-col gap-2 mb-2">
        <div className="flex items-start justify-between">
          <h3 className="text-lg font-medium text-gray-900">
            {survey.title || 'Survey Title'}
          </h3>
          {isOngoing && (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              Active
            </span>
          )}
        </div>
        {daysLeft !== null && daysLeft < 8 && (
          <div className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800 w-fit">
            <ClockIcon />
            <span>ENDING SOON</span>
          </div>
        )}
      </div>
      
      <p className="text-sm text-gray-600 mb-4">
        {survey.description || 'No description available'}
      </p>
      
      <div className="mb-4">
        <div className="flex justify-between text-xs text-gray-500 mb-1">
          <span>Progress</span>
          <span>{Math.round(progress)}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2 mb-3">
          <div 
            className="bg-blue-600 h-2 rounded-full" 
            style={{ width: `${progress}%` }}
          />
        </div>
        
        {/* Stats Counts */}
        <div className="grid grid-cols-3 gap-2 text-xs">
          <div className="flex flex-col items-center p-2 bg-gray-50 rounded">
            <span className="font-medium text-gray-900">
              {statsMap[survey.id]?.questions_completed || 0}
            </span>
            <span className="text-gray-500 text-xs">Completed</span>
          </div>
          <div className="flex flex-col items-center p-2 bg-gray-50 rounded">
            <span className="font-medium text-gray-900">
              {statsMap[survey.id]?.questions_left || 0}
            </span>
            <span className="text-gray-500 text-xs">Remaining</span>
          </div>
          <div className="flex flex-col items-center p-2 bg-blue-50 rounded">
            <span className="font-medium text-blue-700">
              {statsMap[survey.id]?.total_questions || 0}
            </span>
            <span className="text-blue-600 text-xs">Total</span>
          </div>
        </div>
      </div>
      
      <div className="flex justify-between items-center text-sm">
        <span className="text-gray-500">
          {survey.deadline ? `Due: ${formatDate(survey.deadline)}` : 'No deadline'}
        </span>
        <button 
          onClick={() => onViewDetails(survey.id)}
          className="text-blue-600 hover:text-blue-800 font-medium"
        >
          View Details
        </button>
      </div>
    </div>
  );
};

export const SurveyCardSkeleton = () => (
  <div className="bg-white rounded-lg border border-gray-200 p-4 animate-pulse">
    <div className="h-5 bg-gray-200 rounded w-3/4 mb-3"></div>
    <div className="h-3 bg-gray-200 rounded w-1/2 mb-4"></div>
    <div className="h-2 bg-gray-200 rounded w-full mb-2"></div>
    <div className="h-2 bg-gray-200 rounded w-5/6 mb-4"></div>
    <div className="h-8 bg-gray-200 rounded w-full"></div>
  </div>
);
