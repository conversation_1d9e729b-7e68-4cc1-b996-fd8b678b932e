import React from "react";
import { <PERSON><PERSON>, Button, Icon, Layout, Span, Text } from "unmatched/components";
import ModalHeader from "../../../../ModalHeader";

const icons = {
  DOWNLOAD: "fal fa-file-download",
  EMAIL: "fal fa-paper-plane",
};

const ViewReport = (props: any) => {
  const { show, onHide, user, onView, onDownload, onEmail } = props;
  const [activities] = React.useState([
    { id: 1, label: "Emailed Report to Pariticpant on 18-April at 10:05AM" },
    { id: 2, label: "Report download today at 09:03pm" },
  ]);

  const getListTemplate = () => {
    return (
      <Layout.Row className="border py-2 mx-0">
        <Layout.Col xl={2}>
          {" "}
          <Text.P2>{user.name}</Text.P2>{" "}
        </Layout.Col>
        <Layout.Col xl={4}>
          <Text.P2>{user.email}</Text.P2>
        </Layout.Col>
        <Layout.Col xl={3}>
          <Button className="py-0" size="sm" variant="link" onClick={onView}>
            <Icon icon="far fa-eye" /> View Report
          </Button>
        </Layout.Col>
        <Layout.Col xl={3}>
          <Button className="py-0" size="sm" variant="link">
            <Icon icon="far fa-flag" /> Flag as verified
          </Button>
        </Layout.Col>
      </Layout.Row>
    );
  };

  const getActivityTemplate = () => {
    return activities.map((activity: any) => {
      return (
        <Layout.Flex className="py-2" key={activity.id}>
          <Layout.FlexItem className="align-self-center text-primary pr-3 fs-8">
            <Span>
              <Icon icon="far fa-circle" />
            </Span>
          </Layout.FlexItem>
          <Layout.FlexItem className="align-self-center">
            <Text.P2>{activity.label}</Text.P2>
          </Layout.FlexItem>
        </Layout.Flex>
      );
    });
  };

  return (
    <Modal show={show} backdrop="static" centered size="lg">
      <ModalHeader title="Report Details" onHide={() => onHide(user)} />
      <Modal.Body>
        {getListTemplate()}
        <Text.P2 className="my-3">Activity</Text.P2>
        {getActivityTemplate()}
      </Modal.Body>
      <Modal.Footer className="text-right mt-5 border-none">
        <Button variant="outline-primary" className="mr-2" onClick={onDownload}>
          <Icon icon={icons.DOWNLOAD} className="mr-1" />
          Download
        </Button>
        <Button variant="primary" onClick={onEmail}>
          <Icon icon={icons.EMAIL} className="mr-1" />
          Email Reports
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default ViewReport;
