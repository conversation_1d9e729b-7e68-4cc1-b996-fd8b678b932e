import {
  Button,
  Div,
  FormControl,
  Icon,
  Layout,
  Text,
  util,
} from "@unmatchedoffl/ui-core";
import React from "react";
import { Dropdown } from "react-bootstrap";
// import Div from "../Div";
// import FormControl from "../Form/FormControl/FormControl";
// import Icon from "../Icon";
// import Text from "../Text/Text";
// import Button from "../Button/Button";
// import { Layout } from "..";
// import util from "../../utils";
// import Layout from "../Layout/Layout";
// interface Props {
//   cohart: any;
//   applied: any;
//   onCohartUpdate: Function;
//   onAppliedUpdate: Function;
//   hideIcon? : any;
//   isAppliedShown: boolean;
// }

const AppliedFilter = (props: any) => {
  const [searchVal, setSearchVal] = React.useState("");

  const [loading, setLoading] = React.useState(false);

  const [list, setList] = React.useState(props.values);

  const selected = util.getCommaSeperatedFromArray(props.selected);

  const onInput = (e: any) => {
    if (props.hasDynamicSearch) {
      setLoading(true);
      const temp = searchVal;
      setSearchVal(e.target.value);
      props.onSearch(e.target.value).then(
        (values: any) => {
          setLoading(false);
          setList(values);
        },
        () => {
          setLoading(false);
          setSearchVal(temp);
        }
      );
    } else {
      // console.log(props);
      setSearchVal(e.target.value);
      setList(() => {
        return props.values.filter((item: any) => {
          const lowerItem = item.toLowerCase();
          const lowerSelected = e.target.value.toLowerCase();
          return lowerSelected ? lowerItem.includes(lowerSelected) : true;
        });
      });
    }
  };

  const onSelect = (item: string) => {
    props.onUpdate([...props.selected, item]);
  };

  const onUnSelect = (item: string) => {
    props.onUpdate(props.selected.filter((i: any) => item !== i));
  };

  return (
    <Layout.Flex className="align-self-center shadow-sm rounded pl-2 mr-2 fs-12 bg-info-light">
      <Text.P2 className="align-self-center pr-1 border-right fw-600">
        {props.title}
      </Text.P2>
      <Div>
        <Dropdown>
          <Dropdown.Toggle
            variant="link"
            size="sm"
            className={"p-0 pr-1 text-dark"}
          >
            <Text.P2>
              {selected
                .split(",")
                .map((_d: string) => _d)
                .join(", ") || "Select"}
            </Text.P2>
          </Dropdown.Toggle>
          <Dropdown.Menu>
            <Div className="px-2">
              <FormControl.Text
                placeholder="Search"
                value={searchVal}
                onInput={onInput}
              />
            </Div>
            {!loading ? (
              list.map((item: any) => {
                const isChecked = props.selected.includes(item);
                // console.log(props.selected, item, props.selected.includes(item));
                return (
                  <Dropdown.Item
                    className={
                      isChecked
                        ? "bg-info-light text-left pl-2"
                        : "text-left pl-2"
                    }
                    key={item}
                    onClick={() => {
                      if (!isChecked) {
                        onSelect(item);
                      } else {
                        onUnSelect(item);
                      }
                    }}
                  >
                    <FormControl.Checkbox>
                      <FormControl.Checkbox.Input
                        checked={isChecked}
                        className="no-pointer-events"
                        onChange={() => ""}
                      />
                      <FormControl.Checkbox.Label>
                        {item || 'NA'}
                      </FormControl.Checkbox.Label>
                    </FormControl.Checkbox>
                  </Dropdown.Item>
                );
              })
            ) : (
              <Div>Loading...</Div>
            )}
          </Dropdown.Menu>
        </Dropdown>
      </Div>
      <Div className="align-self-center">
        <Button variant="link" className="py-0 pr-1" onClick={props.onRemove}>
          <Icon icon="fal fa-times" />
        </Button>
      </Div>
    </Layout.Flex>
  );
};

// const SearchableKeys = (props: any) => {
//   const [val, setVal] = React.useState("");

//   const filters = Object.keys(props.filters);

//   const [list, setList] = React.useState(filters);

//   const onInput = (e: any) => {
//     setVal(e.target.value);
//     setList(
//       filters.filter((item: any) =>
//         item.toLowerCase().includes(e.target.value.toLowerCase())
//       )
//     );
//   };

//   const getItemsTemplate = () => {
//     return list.map((key: any) => {
//       return (
//         <Dropdown.Item
//           onClick={() => {
//             setVal("");
//             props.onSelect(key);
//           }}
//         >
//           {util.lib.get(props.filters, key).label}
//         </Dropdown.Item>
//       );
//     });
//   };

//   return (
//     <Dropdown show={!!val} className="w-100">
//       <Dropdown.Toggle variant="link" block className={"p-0 no-underline"}>
//         <FormControl.Text
//           placeholder="Type for People, Department, Practice Group, etc"
//           size="lg"
//           value={val}
//           onInput={onInput}
//         />
//       </Dropdown.Toggle>
//       <Dropdown.Menu>{getItemsTemplate()}</Dropdown.Menu>
//     </Dropdown>
//   );
// };

const ComboFilterRedundant = (props: any) => {
  const { filters, selected, onFilterSelect } = props;

  const onSelect = (key: string) => {
    if (util.lib.get(selected, key)) return;
    const _selected = { ...selected, [key]: [] };
    onFilterSelect(_selected);
  };

  const onRemove = (key: string) => {
    const _selected = util.lib.omit(selected, key);
    onFilterSelect(_selected);
  };

  const onUpdate = (key: string, selectedValues: any) => {
    const _selected = { ...selected, [key]: selectedValues };
    onFilterSelect(_selected);
  };

  const getItemsTemplate = () => {
    const items = Object.keys(filters);
    return items.map((key: any) => {
      return (
        <Dropdown.Item
          key={key}
          onClick={() => onSelect(key)}
          className="fs-12 px-3"
        >
          {util.lib.get(filters, key).label}
        </Dropdown.Item>
      );
    });
  };

  const getSelectedCaseTemplate = () => {
    return (
      <Layout.Flex className={"border w-100 pl-1 flex-wrap rounded"}>
        <Layout.Flex className={"flex-wrap"}>
          {Object.keys(selected).map((key: any) => {
            const filterItem = util.lib.get(filters, key);
            const selectedItem = util.lib.get(selected, key);
            return (
                filterItem && <AppliedFilter
                title={filterItem.label}
                key={key}
                values={filterItem.values}
                onUpdate={(selectedValues: any) =>
                  onUpdate(key, selectedValues)
                }
                selected={selectedItem}
                onRemove={() => onRemove(key)}
                hasDynamicSearch={filterItem.isDynamic}
                onSearch={filterItem.onSearch}
              />
            );
          })}
          <Dropdown>
            <Dropdown.Toggle
              variant="link"
              className="fs-12 pb-0 pb-0 pt-1 my-1  px-2 d-flex align-items-center"
            >
              <Icon
                icon="fal fa-plus-circle"
                className="text-primary fs-12 mr-2"
              />
              Add a filter
            </Dropdown.Toggle>
            <Dropdown.Menu>{getItemsTemplate()}</Dropdown.Menu>
          </Dropdown>
        </Layout.Flex>
        {!util.lib.isEmpty(selected) ? (
          <Button
            variant="link"
            className="ml-auto align-self-center"
            onClick={() => {
              onFilterSelect({});
            }}
          >
            <Icon
              icon="fas fa-times-circle"
              className="text-grey-light fs-16"
            />
          </Button>
        ) : (
          ""
        )}
      </Layout.Flex>
    );
  };

  return (
    <Layout.Flex>
      {/* {!util.lib.isEmpty(selected) ? (
        getSelectedCaseTemplate()
      ) : (
        <SearchableKeys filters={filters} onSelect={onSelect} />
      )} */}
      {getSelectedCaseTemplate()}
      {/* <Button  onClick={() => onSubmit()}>
        <Icon icon="fal fa-arrow-right" />
      </Button> */}
    </Layout.Flex>
  );
};

ComboFilterRedundant.defaultProps = {
  selected: [],
  filters: [],
};

export default ComboFilterRedundant;
