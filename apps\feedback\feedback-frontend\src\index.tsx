import React from "react";
import ReactDOM from "react-dom";
import { Provider } from "react-redux";
// Helpers
import util from "unmatched/utils";
import { store, persistor } from "./store";
import initConfigurations from "./configurations";
// Components
import App from "./pages/App";
// Styles
import "./index.scss";
import { PersistGate } from "redux-persist/integration/react";

ReactDOM.render(
  <Provider store={store}>
    <PersistGate loading={null} persistor={persistor}>
      <App />
    </PersistGate>
  </Provider>,
  document.getElementById("root")
);

if (util.env.isProd) {
  util.api.init(`https://${window.location.hostname}/api/v2`);
} else if (util.env.apiUrl) {
  util.api.init(util.env.apiUrl);
} else {
  util.api.init("https://alpha.unmatched.app/api/v2");
}

initConfigurations();
