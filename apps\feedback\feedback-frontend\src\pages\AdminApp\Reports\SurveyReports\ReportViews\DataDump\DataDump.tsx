import {
  downloadUpwardDatadump,
  downloadDatadump,
} from "pages/AdminApp/Reports/reports-api";
import React from "react";
import {
  PageContainer,
  Icon,
  Text,
  Layout,
  Button,
  Div,
} from "unmatched/components";
import Header from "../../Header";
import useToastr from "unmatched/modules/toastr/hook";
import appUrls from "unmatched/utils/urls/app-urls";
import iconsSvgs from "assets/icons/icons";

const { Page } = iconsSvgs;

const icons = {
  DOWNLOAD: "fas fa-file-download mr-2",
  EMAIL: "fal fa-paper-plane",
  CHECKED: "fal fa-check-circle",
  LOADER: "far fa-circle-notch",
};

export default function DataDump({ survey, layout, type = "zip" }: any) {
  const toastr = useToastr();
  const [isLoading, setIsLoading] = React.useState(false);

  const onDownload = async () => {
    setIsLoading(true);
    try {
      type === "zip"
        ? await downloadUpwardDatadump(
            { index_id: survey.id },
            { name: survey.name }
          )
        : await downloadDatadump(
            { index_id: survey.id },
            { name: survey.name }
          );
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
      toastr.errorToast("Something went wrong.");
    }
  };

  const getDataDumpTemplate = () => {
    return (
      <>
        <Text.P1 className="py-2 mb-3">
          Download the entire survey data as{" "}
          {type === "zip" ? "a zip file" : "an excel sheet"}.
        </Text.P1>
        <Div>
          <Button onClick={onDownload} disabled={isLoading}>
            <Icon icon={icons.DOWNLOAD} /> Download{" "}
            {type === "zip" ? ".zip" : ".xlsx"}
          </Button>
        </Div>
      </>
    );
  };

  return (
    <PageContainer>
      <Header
        survey={survey}
        layout={layout}
        breadcrumbs={[
          {
            label: "Reports",
            icon: <Page className="grey-icon__svg" />,
            route: appUrls.admin.reports.default,
          },
          { label: "Data Dump" },
          { label: survey.name },
        ]}
      />
      <Layout.Container fluid>
        <Text.H3 className="pt-5">Download Survey Data</Text.H3>
        {getDataDumpTemplate()}
      </Layout.Container>
    </PageContainer>
  );
}
