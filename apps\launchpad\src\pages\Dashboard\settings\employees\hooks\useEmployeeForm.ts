import { useState, useEffect } from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import { 
  FormattedMetadataField, 
  getRequiredUserFieldService,
  addEmployeeService,
  updateEmployeeService,
  AddEmployeePayload,
  Employee
} from "../employeesService";

interface UseEmployeeFormProps {
  metadataFields: FormattedMetadataField[];
  onSuccess: (data: AddEmployeePayload) => void;
  employee?: Employee | null; // For edit mode
  isEditMode?: boolean;
}

export const useEmployeeForm = ({
  metadataFields,
  onSuccess,
  employee = null,
  isEditMode = false
}: UseEmployeeFormProps) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [requiredFields, setRequiredFields] = useState<string[]>([]);
  const [isLoadingRequiredFields, setIsLoadingRequiredFields] = useState(false);

  // Create dynamic schema based on metadata fields
  const dynamicSchema = z.object({
    emp_id: z.string().optional(),
    email: z.string().email({ message: "Please enter a valid email address" }),
    first_name: z.string().min(1, { message: "First name is required" }),
    last_name: z.string().min(1, { message: "Last name is required" }),
    // Add dynamic fields from metadata as optional
    ...Object.fromEntries(
      metadataFields.map((field) => [field.field, z.string().optional()])
    ),
  });

  // Create form with dynamic schema
  const form = useForm<z.infer<typeof dynamicSchema>>({
    resolver: zodResolver(dynamicSchema),
    defaultValues: {
      emp_id: employee?.empId || "",
      email: employee?.email || "",
      first_name: employee?.firstName || "",
      last_name: employee?.lastName || "",
      // Dynamic fields will be initialized with values from employee or empty strings
      ...Object.fromEntries(
        metadataFields.map((field) => [
          field.field, 
          employee?.metadata?.[field.field] || ""
        ])
      ),
    },
  });

  // Reset form when metadata fields or employee changes
  useEffect(() => {
    // Update form default values when metadata fields or employee changes
    form.reset({
      emp_id: employee?.empId || "",
      email: employee?.email || "",
      first_name: employee?.firstName || "",
      last_name: employee?.lastName || "",
      ...Object.fromEntries(
        metadataFields.map((field) => [
          field.field, 
          employee?.metadata?.[field.field] || ""
        ])
      ),
    });
  }, [metadataFields, employee, form]);

  // Fetch required fields
  useEffect(() => {
    const fetchRequiredFields = async () => {
      setIsLoadingRequiredFields(true);
      try {
        const fields = await getRequiredUserFieldService();
        setRequiredFields(fields);
      } catch (error) {
        console.error("Error fetching required fields:", error);
        // Set default required fields if fetch fails
        setRequiredFields([
          "emp_id",
          "email",
          "first_name",
          "last_name",
        ]);
      } finally {
        setIsLoadingRequiredFields(false);
      }
    };
    
    fetchRequiredFields();
  }, []);

  // Handle form submission
  const onSubmit = async (formData: z.infer<typeof dynamicSchema>) => {
    try {
      setIsSubmitting(true);

      // Format the data according to the API requirements
      const employeeData: AddEmployeePayload = {
        emp_id: formData.emp_id === "" ? undefined : formData.emp_id,
        email: formData.email || "",
        first_name: formData.first_name || "",
        last_name: formData.last_name || "",
        is_active: true,
        metadata: {}
      };

      // Extract metadata fields from the form data
      metadataFields.forEach(field => {
        const fieldName = field.field;
        const fieldValue = formData[fieldName as keyof typeof formData];
        if (fieldValue) {
          employeeData.metadata[fieldName] = fieldValue;
        }
      });

      // Call the API to add or update the employee
      if (isEditMode && employee) {
        await updateEmployeeService(employee.id, employeeData);
        toast.success(`Employee updated successfully`, {
          description: `${formData.first_name} ${formData.last_name} has been updated.`,
        });
      } else {
        await addEmployeeService(employeeData);
        toast.success(`Employee added successfully`, {
          description: `${formData.first_name} ${formData.last_name} has been added to the system.`,
        });
      }

      // Call the parent component's callback
      onSuccess(employeeData);

      // Reset the form
      form.reset();
    } catch (error: unknown) {
      console.error(`Error ${isEditMode ? 'updating' : 'adding'} employee:`, error);
      
      // Extract error message from the server response
      let errorMessage = "An unexpected error occurred";
      
      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as any;
        
        // Check for different error response formats
        if (axiosError.response?.data?.e && typeof axiosError.response.data.e === 'string') {
          errorMessage = axiosError.response.data.e;
        } else if (axiosError.response?.data?.email) {
          const emailError = axiosError.response.data.email;
          if (Array.isArray(emailError) && emailError.length > 0 && typeof emailError[0] === 'string') {
            errorMessage = emailError[0];
          } else if (typeof emailError === 'string') {
            errorMessage = emailError;
          }
        } else if (axiosError.response?.data?.detail && typeof axiosError.response.data.detail === 'string') {
          errorMessage = axiosError.response.data.detail;
        } else if (axiosError.response?.data?.message && typeof axiosError.response.data.message === 'string') {
          errorMessage = axiosError.response.data.message;
        } else if (typeof axiosError.response?.data === 'string') {
          errorMessage = axiosError.response.data;
        } else if (axiosError.response?.data && typeof axiosError.response.data === 'object') {
          // Try to find any string property that might contain an error message
          for (const key in axiosError.response.data) {
            const value = axiosError.response.data[key];
            if (typeof value === 'string') {
              errorMessage = value;
              break;
            } else if (Array.isArray(value) && value.length > 0 && typeof value[0] === 'string') {
              errorMessage = value[0];
              break;
            }
          }
        }
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }

      // Show error message
      toast.error(errorMessage, {
        description: "Please correct the error and try again.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    form,
    onSubmit: form.handleSubmit(onSubmit),
    isSubmitting,
    requiredFields,
    isLoadingRequiredFields,
  };
};
