// import appUrls from "unmatched/utils/urls/app-urls";
import ADMIN_URLS from "@/app.routes";
import icons from "@/assets/icons/icons";

const {
  Graph, //Cog, Group,
  Page,
  Paste,
  Email,
} = icons;

const data = [
  // {
  //   id: 1,
  //   title: "Dashboard",
  //   icon: <Chart />,
  //   route: appUrls.admin.home,
  //   children: [],
  // },
  {
    id: 2,
    title: "Survey",
    icon: <Paste />,
    route: ADMIN_URLS().SURVEY.default,
    children: [],
  },
  {
    id: 7,
    title: "Emails",
    icon: <Email />,
    route: ADMIN_URLS().EMAIL,
    children: [],
  },
  {
    id: 3,
    title: "Analytics",
    icon: <Graph />,
    route: ADMIN_URLS().ANALYTICS.default,
    children: [
      // {
      //   id: 1,
      //   checkActive: `${appUrls.admin.analytics.default}/stats`,
      //   route: appUrls.admin.analytics.statistics.default,
      //   title: "Statistics",
      // },
      {
        id: 1,
        checkActive: false,
        route: ADMIN_URLS().ANALYTICS.statistics.default,
        title: "360, Upward & Self",
        type: "label",
      },
      {
        id: 2,
        checkActive: `${ADMIN_URLS().ANALYTICS.default}/people`,
        route: ADMIN_URLS().ANALYTICS.people.list,
        title: "People Analytics",
      },
      {
        id: 3,
        checkActive: `${ADMIN_URLS().ANALYTICS.default}/aggregate`,
        route: ADMIN_URLS().ANALYTICS.getAggregateUrl(),
        title: "Aggregate Analytics",
      },
      {
        id: 6,
        checkActive: `${ADMIN_URLS().ANALYTICS.default}/ranking`,
        route: ADMIN_URLS().ANALYTICS.geRankingListUrl(),
        title: "Ranking List",
      },
      {
        id: 4,
        checkActive: false,
        route: "",
        title: "Engagement",
        type: "label",
      },
      {
        id: 5,
        checkActive: `${ADMIN_URLS().ANALYTICS.default}/engagement`,
        route: ADMIN_URLS().ANALYTICS.engagement.default,
        title: "Engagement Analytics",
      },
      {
        id: 6,
        checkActive: false,
        route: "",
        title: "Exit Analytics",
        type: "label",
      },
      {
        id: 7,
        checkActive: `${ADMIN_URLS().ANALYTICS.default}/exit`,
        route: ADMIN_URLS().ANALYTICS.exit.default,
        title: "Exit Analytics",
      },
    ],
  },
  {
    id: 4,
    title: "Reports",
    icon: <Page />,
    route: ADMIN_URLS().REPORTS.default,
    children: [],
  },
  // {
  //   id: 5,
  //   title: "Settings",
  //   icon: <Cog />,
  //   route: appUrls.admin.settings.getURL(),
  //   children: [],
  // },
  // {
  //   id: 6,
  //   title: "Data Load",
  //   icon: <Group />,
  //   route: appUrls.admin.dataLoad.default,
  //   children: [],
  // },
];

export default data;
