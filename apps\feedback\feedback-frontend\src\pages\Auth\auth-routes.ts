import Activation from "./Activation/Activation";
// import AccountInformation from "./AccountInformation/AccountInformation";
import Login from "./Login/Login";
import ResetPassword from "./ResetPassword/ResetPassword";
import RequestPassword from "./RequestPassword/RequestPassword";
import Terms from "./Terms";
import Confidentiality from "./Confidentiality";
import PrivacyPolicy from "./PrivacyPolicy";
import appUrls from "unmatched/utils/urls/app-urls";
import MagicLink from "./MagicLink/MagicLink";

const routes = [
  {
    name: "Activation",
    path: appUrls.auth.getActivationUrl(":email", ":token"),
    isExact: false,
    component: Activation,
  },
  // {
  //   name: "Account Information",
  //   path: appUrls.auth.accountInformation,
  //   isExact: false,
  //   component: AccountInformation,
  // },
  {
    name: "<PERSON><PERSON>",
    path: appUrls.auth.login,
    isExact: false,
    component: Login,
  },
  {
    name: "Reset Password",
    path: appUrls.auth.getResetPasswordUrl(":email", ":token"),
    isExact: false,
    component: ResetPassword,
  },
  {
    name: "Request password change",
    path: appUrls.auth.requestPassword,
    isExact: false,
    component: RequestPassword,
  },
  {
    name: "Terms",
    path: appUrls.auth.terms,
    isExact: false,
    component: Terms,
  },
  {
    name: "Privacy Policy",
    path: appUrls.auth.privacyPolicy,
    isExact: false,
    component: PrivacyPolicy,
  },
  {
    name: "Confidentiality",
    path: appUrls.auth.confidentiality,
    isExact: false,
    component: Confidentiality,
  },
  {
    name: "Magic Link",
    path: appUrls.auth.getMagicLinkUrl(":email", ":token"),
    isExact: false,
    component: MagicLink,
  },
];

export default routes;
