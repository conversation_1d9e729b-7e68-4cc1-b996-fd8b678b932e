// import { features } from "@unmatchedoffl/ui-core";

// const { toastrModule } = features.default;
import toastrHook from './hook';
import { createSlice } from "@reduxjs/toolkit";

interface Toastr {
  title: string;
  variant: string;
  delay: number;
  content: string;
}

interface StateType {
  toastrs: Array<Toastr>;
}

const initialState: StateType = {
  toastrs: [],
};

const toastrSlice = createSlice({
  name: "toastr",
  initialState,
  reducers: {
    add: (state: StateType, { payload }: any) => {
      state.toastrs = [...state.toastrs, payload];
    },
    remove: (state: StateType, { payload }: any) => {
      state.toastrs = state.toastrs.filter(
        (item: any, index: number) => payload !== index
      );
    },
    reset: (state: StateType) => {
      state.toastrs = [];
    },
  },
});

const _toastrReducer = toastrSlice.reducer;
const actions = toastrSlice.actions;

// export default toastrSlice.actions;

const toastrModule = {
    hook: toastrHook,
    slice: {
        reducer: _toastrReducer,
        actions,
    },
}

export const toastrReducer = toastrModule.slice.reducer;

export default toastrModule.slice.actions;
