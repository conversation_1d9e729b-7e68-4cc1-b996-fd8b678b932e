import { Div } from "@unmatchedoffl/ui-core";
import { PureComponent } from "react";
import ReactDiffViewer, { DiffMethod } from "react-diff-viewer";

export class DiffViewer extends PureComponent {
  render = () => {
    return (
      <Div>
        <Div className="row">
          <Div className="input-label col-6 col-sm-6">
            Original Comment
          </Div>
          <Div className="input-label col-6 col-sm-6">
            Edit Comment
          </Div>
        </Div>
        <Div className="row diff-view-main-wrap">
          <Div className="diff-viewer-wrap col-12 col-sm-12 mt-2">
            <ReactDiffViewer
              hideLineNumbers
              oldValue={this.props.oldComment}
              newValue={this.props.newComment}
              splitView={true}
              compareMethod={DiffMethod.WORDS}
            />
          </Div>
        </Div>
      </Div>
    );
  };
}
