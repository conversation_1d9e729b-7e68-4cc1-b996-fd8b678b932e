import axios, { AxiosResponse } from "axios";
import api from "unmatched/utils/api";
import API_URLS from "unmatched/utils/urls/api-urls";

export const getUpwardZippedReport = async (params: any, meta?: any) => {
  const config = api.getConfigurations(params, {
    ...meta,
    // responseType: "blob",
  });
  return axios.get(API_URLS.GET_UPWARD_REPORTS, config).then((response) => {
    const { data } = response;
    window.open(data.file, "_blank");
    // const url = window.URL.createObjectURL(new Blob([response.data]));
    // const link = document.createElement("a");
    // link.href = url;
    // link.setAttribute("download", `${params.index_id}.zip`);
    // document.body.appendChild(link);
    // return link.click();
  });
};

export const getReportsDownloadRequests = async (params?: any) => {
  const { surveyId, ...restParams } = params;
  const config = api.getConfigurations(restParams, {});
  return axios
    .get(`${API_URLS.REQUEST_REPORTS_DOWNLOAD}?index_id=${surveyId}`, config)
    .then((response) => {
      const { data } = response;
      return data;
    });
};

export const getEngagementReportsDownloadRequests = async (params?: any) => {
  const { surveyId, ...restParams } = params;
  const config = api.getConfigurations(restParams, {});
  return axios
    .get(`${API_URLS.GENERATE_ZIPPED_ENGAGEMENT_REPORTS}?index_id=${surveyId}`, config)
    .then((response) => {
      const { data } = response;
      return data;
    });
};

export const getUpwardReportsByIdFact = (params: any, meta?: any) => {
  const config = api.getConfigurations(params, meta);
  return axios
    .get(API_URLS.GET_UPWARD_REPORTS, config)
    .then(({ data }: AxiosResponse) => {
      if (!data) return { data: [], totalPages: 0 };
      const { results, count_pages, count_items } = data || {};
      if (!results) return { data: [], totalPages: 0 };
      return {
        data: results.map((item: any) => ({
          id: item.target_details.id,
          firstName: item.target_details.first_name,
          lastName: item.target_details.last_name,
          email: item.target_details.email,
          empId: item.target_details.emp_id,
          file: item.file,
          updatedAt: item.updated_at,
          uploadedOn: item.uploaded_on,
        })),
        totalPages: count_pages,
        totalElements: count_items,
      };
    });
};
export const getFirmReportsByIdFact = (params: any, meta?: any) => {
  const config = api.getConfigurations(params, meta);
  return axios
    .get(API_URLS.GET_FIRM_REPORTS, config)
    .then(({ data }: AxiosResponse) => {
      if (!data) return { data: [], totalPages: 0 };
      const { results, count_pages, count_items } = data;
      if (!results) return { data: [], totalPages: 0 };
      return {
        data: results.map((item: any) => ({
          id: item.id,
          title: item.index_details.title,
          file: item.file,
          updatedAt: item.updated_at,
          uploadedOn: item.uploaded_on,
        })),
        totalPages: count_pages,
        totalElements: count_items,
      };
    });
};

export const requestNewReport = (data: any, params?: any, meta?: any) => {
  const config = api.getConfigurations(params, meta);
  return axios
    .post(
      API_URLS.REQUEST_UPWARD_REGEN_REPORT,
      {
        survey_index: data.index,
      },
      config
    )
    .then(({ data }: AxiosResponse) => {
      return {
        data: {
          taskId: data.task_id,
        },
      };
    });
};

export const requestNewFirmReport = (data: any, params?: any, meta?: any) => {
  const config = api.getConfigurations(params, meta);
  return axios
    .post(
      API_URLS.REQUEST_FIRM_REGEN_REPORT,
      {
        survey_index: data.index,
      },
      config
    )
    .then(() => {
      return {
        data: {
          success: true,
        },
      };
    });
};
export const downloadDatadump = (params?: any, query?: any) => {
  const config = api.getConfigurations(params, {
    headers: {
      // 'Content-Type': 'blob',
    },
    responseType: "blob",
  });
  return axios
    .get(`${API_URLS.GET_ENGAGEMENT_DATADUMP}`, config)
    .then((response) => {
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", `${query.name}_datadump.xlsx`);
      document.body.appendChild(link);
      link.click();
    });
};

export const downloadUpwardDatadump = (params?: any, query?: any) => {
  const config = api.getConfigurations(params, {
    headers: {
      // 'Content-Type': 'blob',
    },
    responseType: "blob",
  });

  return axios.get(`${API_URLS.GET_UPWARD_DATADUMP}`, config).then(
    (response) => {
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", `${query.name}_datadump.zip`);
      document.body.appendChild(link);
      link.click();
    },
    (err: any) => err
  );
};

export const downloadParticipantCSV = (
  params?: any,
  query?: any,
  id?: any,
  filename?: any
) => {
  const config = api.getConfigurations(params, {
    headers: {
      // 'Content-Type': 'blob',
    },
    responseType: "blob",
  });

  return axios
    .post(
      `/survey/admin/timed-participants/${id}/download_responses/`,
      {},
      config
    )
    .then(
      ({ data }) => {
        const url = window.URL.createObjectURL(new Blob([data]));
        const link = document.createElement("a");
        link.href = url;
        link.setAttribute("download", `${filename}.xlsx`);
        document.body.appendChild(link);
        link.click();
      },
      (err: any) => err
    );
};

export const downloadParticipantReportPDF = (params?: any, payload?: any) => {
  const config = api.getConfigurations(params, {
    headers: {
      // 'Content-Type': 'blob',
    },
    responseType: "blob",
  });

  return axios.post(`/report/exit/target/`, { ...payload }, config).then(
    ({ data }) => {
      const url = window.URL.createObjectURL(new Blob([data]));
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", `${payload.fileName}.pdf`);
      document.body.appendChild(link);
      link.click();
    },
    (err: any) => err
  );
};

export const get360ReportLists = (params: any, meta?: any) => {
  const config = api.getConfigurations(params, meta);
  return axios
    .get(API_URLS.GET_360_REPORT_LISTS, config)
    .then(({ data }: AxiosResponse) => {
      if (!data) return { data: [], totalPages: 0 };
      const { results, count_pages, count_items } = data;
      if (!results) return { data: [], totalPages: 0 };
      return {
        data: results.map((_dt: any) => {
          const { name, id, report_stats } = _dt;
          return {
            title: name,
            id: id,
            reportStats: {
              totalEligible: report_stats?.total_eligible || 0,
              totalReports: report_stats?.total_reports || 0,
            },
          };
        }),
        totalPages: count_pages,
        totalElements: count_items,
      };
    });
};

export const get360ReportDetail = (id: string, params?: any, meta?: any) => {
  const config = api.getConfigurations(params, meta);
  return axios
    .get(API_URLS.ADMIN_360_REPORT_INFO(id), config)
    .then(({ data }: AxiosResponse) => {
      return {
        name: data.name,
        id: data.id,
        startDate: data.start_date,
        endDate: data.end_date,
        type: data.resourcetype,
      };
    });
};

export const get360ReportsByIdFact = (params: any, meta?: any) => {
  const config = api.getConfigurations(params, meta);
  return axios
    .get(API_URLS.GET_360_REPORTS, config)
    .then(({ data }: AxiosResponse) => {
      if (!data) return { data: [], totalPages: 0 };
      const { results, count_pages, count_items } = data;
      if (!results) return { data: [], totalPages: 0 };
      return {
        data: results.map((item: any) => ({
          id: item?.id,
          title: item?.target_details?.metadata?.title,
          firstName: item?.target_details?.first_name,
          lastName: item?.target_details?.last_name,
          email: item?.target_details?.email,
          file: item?.file,
          updatedAt: item?.updated_at,
          uploadedOn: item?.uploaded_on,
        })),
        totalPages: count_pages,
        totalElements: count_items,
      };
    });
};

export const requestNew360Report = (data: any, params?: any, meta?: any) => {
  const config = api.getConfigurations(params, meta);
  return axios
    .post(
      API_URLS.REQUEST_360_REGEN_REPORT,
      {
        survey_index_group: data.index,
      },
      config
    )
    .then(({ data }: AxiosResponse) => {
      return {
        data: {
          taskId: data.task_id,
        },
      };
    });
};

export const requestReportDownload = (data: any, cb: any) => {
  const config = api.getConfigurations(undefined, undefined);
  return axios
    .post(
      API_URLS.REQUEST_REPORTS_DOWNLOAD,
      {
        ...data,
      },
      config
    )
    .then((res: AxiosResponse) => {
      cb?.(res);
    });
};

export const getZippedReports = (data: any, params?: any, meta?: any) => {
  const config = api.getConfigurations(params, meta);
  return axios
    .post(
      API_URLS.REQUEST_360_REGEN_REPORT,
      {
        survey_index_group: data.index,
      },
      config
    )
    .then(({ data }: AxiosResponse) => {
      return {
        data: {
          taskId: data.task_id,
        },
      };
    });
};

export const generateZippedEngagementReports = (data: any, params?: any, meta?: any) => {
  const config = api.getConfigurations(params, meta);
  return axios
    .post(
      API_URLS.GENERATE_ZIPPED_ENGAGEMENT_REPORTS,
      {
        index: data.index,
        parameters: data.filters
      },
      config
    )
    .then(({ data }: AxiosResponse) => {
      return {
        data: {
          taskId: data.task_id,
        },
      };
    });
};

export const getAggregateReports = (params?: any, meta?: any) => {
  const config = api.getConfigurations(params, meta);
  return axios
    .get(API_URLS.AGGREGATE_REPORT, config)
    .then(({ data }: AxiosResponse) => {
      return {
        data,
      };
    });
};

export const requestAggregateReports = (
  data: any,
  params?: any,
  meta?: any
) => {
  const config = api.getConfigurations(params, meta);
  return axios
    .post(
      API_URLS.AGGREGATE_REPORT,
      {
        survey_index: data.index,
        parent_cat: data.parent,
        child_cat: data.child,
      },
      config
    )
    .then(({ data }: AxiosResponse) => {
      return {
        data: {
          taskId: data.task_id,
        },
      };
    });
};

export const sendEmailReports = (data: any, params?: any, meta?: any) => {
  const config = api.getConfigurations(params, meta);
  return axios
    .post(
      API_URLS.SEND_REPORT,
      {
        survey_index: data.index,
        send_email: true,
        regen: true,
        ...data,
        // parent_cat: data.parent,
        // child_cat: data.child,
      },
      config
    )
    .then(({ data }: AxiosResponse) => {
      return {
        data: {
          taskId: data.task_id,
        },
      };
    });
};

export const generateBindersReport = (data: any, params?: any, meta?: any) => {
  const config = api.getConfigurations(params, meta);
  return axios
    .post(
      API_URLS.BINDERS_REPORT,
      {
        index: data.index,
        filters: data.filters,
        user_label_format: data.nameStyle,
        levels: data.selectedMetas,
        title: data.title,
      },
      config
    )
    .then(({ data }: AxiosResponse) => {
      return {
        data: {
          taskId: data.task_id,
        },
      };
    });
};


export const getBindersReportsDownloads = async (params?: any) => {
  const { surveyId, ...restParams } = params;
  const config = api.getConfigurations(restParams, {});
  return axios
    .get(`${API_URLS.BINDERS_REPORT}?index_id=${surveyId}`, config)
    .then((response) => {
      const { data } = response;
      return data;
    });
};