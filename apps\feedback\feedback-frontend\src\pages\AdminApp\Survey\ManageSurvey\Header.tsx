import React from "react";
import CustomHeader from "../../Shared/CustomHeader/CustomHeader";
import { Placeholder, Text } from "unmatched/components";
import Badge from "react-bootstrap/Badge";
// import PropTypes from 'prop-types'

const Header = (props: any) => {
  const { survey, layout, metaTemplate, isLoading, breadcrumbs } = props;

  const getHeaderTitleTemplate = () => {
    return (
      <div className="d-block">
        <Text.H1 style={{ minWidth: "320px" }}>
          {isLoading ? (
            <Placeholder width="col-lg-12 col-md-7 col-sm-12" />
          ) : (
            survey.name
          )}
        </Text.H1>
        <Text.P1 style={{ minWidth: "240px" }}>
          {isLoading ? (
            <Placeholder width="col-lg-7 mt-3  col-md-7 col-sm-12" size="2" />
          ) : (
            <Badge
              className="px-2 py-2 my-3 font-weight-normal"
              style={{ fontSize: "100%" }}
              variant="primary"
            >
              {survey.startDate} - {survey.endDate}
            </Badge>
          )}
        </Text.P1>
      </div>
    );
  };

  return (
    <CustomHeader
      style={{ marginLeft: layout.marginLeft }}
      title={getHeaderTitleTemplate()}
      information={<></>}
      metaItem={metaTemplate}
      breadcrumbs={breadcrumbs}
    />
  );
};

// Header.propTypes = {

// }

export default Header;
