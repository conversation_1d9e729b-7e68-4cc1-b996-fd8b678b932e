import React, { useEffect } from "react";
import {
  Div,
  Layout,
  // FormGroup,
  // FormControl,
  Text,
  Table,
  // Sentiment,
  Icon,
  Button,
} from "unmatched/components";
import { useTable, useXHR } from "unmatched/hooks";
// import DownloadExcel from "../../DownloadExcel";
// import { GRAPH_DATA } from "./time-series-meta";
import {
  getOverallGraphDataFact,
  getOverallTimeseriesTableFact,
} from "../aggregate-api";
// import TimeSeriesGraph from "../../TimeSeriesGraph/TimeSeriesGraph";
import useFilter from "pages/CommonFilters/hook";
import util from "unmatched/utils";
import useToastr from "unmatched/modules/toastr/hook";
import Detailed from "./Detailed";
import TimeSeriesGraphNew from "../../TimeSeriesGraph/TimeSeriesGraphNew";
import { cloneDeep } from "lodash";

const TYPES: any = {
  "360": "surveyindex360",
  upward: "surveyindexupward",
  self: "surveyindexself",
  "360Group": "surveyindex360group",
};

const defaultSelected = { id: "", key: "", title: "", values: [] };

const getFilterOptions = (_filters: any) => {
  const filters = _filters || {};
  return util.lib.entries(filters).map((item: any) => {
    const [key, value] = item;
    return {
      id: key,
      key,
      title: value.label,
      values: value.values,
    };
  });
};

const FilterLayout = (props: any) => {
  const { selected, onSelect, filters } = props;

  return (
    <Div className="py-3">
      <Layout.Row>
        <Layout.Col className="align-self-center" xl={2}>
          <Text.H3>{props.title}</Text.H3>
        </Layout.Col>
        <Layout.Col className="align-self-center" xl={2}>
          <Table.Filter
            title="Select"
            selected={selected.key}
            selectedLabel={selected.title}
            options={filters}
            onSelect={onSelect}
          />
        </Layout.Col>
        <Layout.Col className="text-right align-self-center">
          <Button
            onClcik={() => (props.onDownload ? props.onDownload() : "")}
            className="ml-auto"
            variant="outline-primary"
          >
            Download XLSX <Icon icon="fas fa-file-download" />
          </Button>
        </Layout.Col>
      </Layout.Row>
      {/* <Layout.Row className="pt-4 pb-3">
        <Layout.Col>{props.children}</Layout.Col>
        
      </Layout.Row> */}
    </Div>
  );
};

const getGraphResourceType = (type: any) => {
  if (type === "360") {
    return TYPES["360Group"];
  }
  return TYPES[type];
};

const Overall = () => {
  const tableMeta = useTable({});

  const filters = useFilter();

  let filterOptions = getFilterOptions(filters.filters);

  const [selected, setSelected]: any = React.useState({
    upward: defaultSelected,
    self: defaultSelected,
    ["360"]: defaultSelected,
  });

  const user = {
    id: 0,
    empId: "",
    name: "",
  };
  const [resources, setResources] = React.useState({
    type: "upward",
    options: ["Overall"],
    legends: [],
    selected: [],
  });
  const [years, setYears]: any = React.useState({
    upward: [],
    self: [],
    ["360"]: [],
  });
  const [coulmns, setColumns]: any = React.useState({
    upward: [],
    self: [],
    ["360"]: [],
  });
  // const [selfYears, setSelfYears]: any = React.useState([]);

  const toastr = useToastr();

  const graph = useXHR({
    defaultResponse: {
      data: [],
      labels: [],
    },
  });

  const [selectedTimeGraphCompareList, setSelectedTimeGraphCompareList] =
    React.useState<any>({});

  const filtersState = useFilter();

  useEffect(() => {
    getGraphData();
  }, [selectedTimeGraphCompareList]);
  

  const getGraphData = async (filters?: any, type?: string) => {
    graph.setLoading(true);
    const raterGroups =
      (filters && filters["rater_groups"]) ||
      (resources.type === "360" ? resources.selected : []);
    const payload = {
      compare_list: Object.values(selectedTimeGraphCompareList),
      ...(filters || {}),
    };
    if (raterGroups.length) {
      payload["rater_groups"] = raterGroups;
    }
    return getOverallGraphDataFact(
      user.id,
      payload,
      getGraphResourceType(type || resources.type)
    ).then(
      (response: any) => {
        graph.onSuccess(response.graph);
        if (!util.lib.keys(payload).length) {
          filtersState.onSelect(response.defaultFilters);
        }
        setResources((_resources: any) => {
          return {
            ..._resources,
            type: type || resources.type,
            options: util.lib.uniq([...resources.options, ...response.groups]),
            legends: response.legends,
          };
        });
        return true;
      },
      (err: any) => {
        toastr.onError(err);
        graph.setLoading(false);
      }
    );
  };

  const getDynamicColumns = (type: any, meta?: any) => {
    const options =
      filterOptions.find((item: any) => item.key === meta)?.values || [];
    return options.map((item: any) => {
      return {
        id: item,
        key: item,
        label: `${item} Average`,
      };
    });
  };

  const getTableData = (type: string, meta?: any) => {
    getOverallTimeseriesTableFact(meta, getGraphResourceType(type)).then(
      (response: any) => {
        setYears((_years: any) => {
          // debugger;
          return {
            ..._years,
            [type]: response.data,
          };
        });
        setColumns((_columns: any) => {
          return {
            ..._columns,
            [type]: getDynamicColumns(type, meta),
          };
        });
      },
      (err: any) => {
        console.log(err); // eslint-disabled-line
      }
    );
  };

  // const onFilterSelect = (data: any) => {
  //   getGraphData({
  //     ...data,
  //     people: data.people
  //       ? data.people.map((item: any) => util.getContentFromBrackets(item))
  //       : [user.empId],
  //   });
  // };

  const onFilterSelectNew = (data: any, groupID?: any) => {
    const computedData = Object.entries(data).reduce((acc, [k, v]) => {
      if ((v as any).length) {
        (acc as any)[k] = v;
      }
      return acc;
    }, {});
    setSelectedTimeGraphCompareList((s: any) => {
      let newList = { ...s, [groupID]: computedData };
      newList = Object.entries(newList).reduce((acc, [k, v]) => {
        if (Object.keys(v as any).length) {
          (acc as any)[k] = v;
        }
        return acc;
      }, {});
      return newList;
    });
  };

  const onFilterRemove = (id: string) => {
    const clone = cloneDeep(selectedTimeGraphCompareList);
    delete clone[id];
    setSelectedTimeGraphCompareList(clone);
  };

  const onResourceChange = (data: any) => {
    getGraphData(
      data === "upward"
        ? {
            ...filtersState.selected,
          }
        : {
            rater_groups: ["Overall"],
            ...filtersState.selected,
          },
      data
    ).then(() => {
      setResources((_data: any) => {
        return {
          ..._data,
          selected: ["Overall"],
        };
      });
    });
  };

  const onGroupChange = (data: any) => {
    getGraphData({
      ...filtersState.selected,
      rater_groups: data,
    }).then(() => {
      setResources({
        ...resources,
        selected: data,
      });
    });
  };

  const getColumnsData = (type: string) => {
    const output = [
      { key: 1, label: "No.", hasSort: false, disableSelfLabel: true },
      {
        key: 2,
        label: "Year",
        hasSort: false,
        disableSelfLabel: true,
      },
      {
        key: 21,
        label: "Survey Title",
        hasSort: false,
        disableSelfLabel: true,
      },
      // {
      //   key: 3,
      //   label: "Reviewee Average",
      //   hasSort: false,
      // },
      { key: 41, label: "Firm Average", hasSort: false },
      ...coulmns[type],
      // { key: 5, label: "Department Average", hasSort: false },
      // { key: 6, label: "Practice Group Average", hasSort: false },
      // { key: 7, label: "Title Average", hasSort: false },
      // { key: 8, label: "Self Average", hasSort: false, disableSelf: true },
      // { key: 9, label: "Comment Sentiment", hasSort: false, disableSelf: true },
    ];
    // debugger;
    return type === "self"
      ? output
          .filter((item: any) => !item.disableSelf)
          .map((item: any) => {
            return {
              ...item,
              label: !item.disableSelfLabel ? `${item.label} Self` : item.label,
            };
          })
      : output;
  };

  const getRowsTemplate = (type: any) => {
    // const _list = (!isSelf ? years : selfYears) || [];
    return (years[type] || []).map((item: any, index: number) => {
      return (
        <Table.Row key={index}>
          <Table.Data>
            <Text.P1>{index + 1}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.year}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.name}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.firmAverage}</Text.P1>
          </Table.Data>
          {coulmns[type].map((row: any) => {
            return (
              <Table.Data>
                <Text.P1>{item[row.key] || "0"}</Text.P1>
              </Table.Data>
            );
          })}
        </Table.Row>
      );
    });
  };

  useEffect(() => {
    filtersState.getFilters(() => {
      getGraphData();
    });
    filters.getFilters((_filters: any) => {
      filterOptions = getFilterOptions(_filters);
      const [item] = filterOptions;
      if (item) {
        setSelected({
          ...selected,
          upward: item,
          self: item,
          ["360"]: item,
        });
        // onTableLoad("upward", "", item.key);
        // onTableLoad("self", "", item.key);
        getTableData("upward", item.key);
        getTableData("self", item.key);
        getTableData("360", item.key);
      }
    });
  }, []);

  return (
    <Layout.Container fluid className="pt-3">
      <Div>
        <TimeSeriesGraphNew
          // filtersState={filtersState}
          onFilterSelect={onFilterSelectNew}
          onFilterRemove={onFilterRemove}
          userName={user.name}
          data={graph.data.data}
          labels={graph.data.labels}
          isLoading={graph.isLoading}
          resourceTypes={{
            ...resources,
            onChange: onResourceChange,
            onGroupChange,
          }}
          disallowedFilters={['people']}
        />
      </Div>
      {years.upward?.length > 0 && <Div>
        <FilterLayout
          title="Time Series Table - Upward Review"
          selected={selected["upward"]}
          filters={filterOptions}
          onSelect={(item: any) => {
            setSelected({
              ...selected,
              ["upward"]: item,
            });
            getTableData("upward", item.key);
            // debugger;
          }}
        />
        <Table
          columns={getColumnsData("upward")}
          isLoading={tableMeta.isLoading}
          rows={years["upward"]}
          customRows
          render={() => getRowsTemplate("upward")}
          hasPagination
          activePage={tableMeta.page}
          pages={tableMeta.totalPages}
          onPageSelect={tableMeta.onPageSelect}
        />
      </Div>}
      {years["360"].length > 0 && <Div>
        <FilterLayout
          title="Time Series Table - 360 Degree"
          selected={selected["360"]}
          filters={filterOptions}
          onSelect={(item: any) => {
            setSelected({
              ...selected,
              ["360"]: item,
            });
            getTableData("360", item.key);
          }}
        />
        <Table
          columns={getColumnsData("360")}
          isLoading={tableMeta.isLoading}
          rows={years["360"]}
          customRows
          render={() => getRowsTemplate("360")}
          hasPagination
          activePage={tableMeta.page}
          pages={tableMeta.totalPages}
          onPageSelect={tableMeta.onPageSelect}
        />
      </Div>}
      {years.self?.length > 0 && <Div>
        <FilterLayout
          title="Time Series Table - Self Assessment"
          selected={selected["self"]}
          filters={filterOptions}
          onSelect={(item: any) => {
            setSelected({
              ...selected,
              ["self"]: item,
            });
            getTableData("self", item.key);
          }}
        />
        <Table
          columns={getColumnsData("self")}
          isLoading={tableMeta.isLoading}
          rows={years["self"]}
          customRows
          render={() => getRowsTemplate("self")}
          hasPagination
          activePage={tableMeta.page}
          pages={tableMeta.totalPages}
          onPageSelect={tableMeta.onPageSelect}
        />
      </Div>}
      <Div>
        <Detailed />
      </Div>
    </Layout.Container>
  );
};

Overall.propTypes = {};

export default Overall;
