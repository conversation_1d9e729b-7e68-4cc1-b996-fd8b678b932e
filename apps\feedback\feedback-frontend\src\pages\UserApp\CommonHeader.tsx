import React, { ReactNode } from "react";
import { Navbar, Nav, Icon } from "unmatched/components";
import { useCheckSelector } from "unmatched/hooks";
import { userMetadataSelector } from "./user-app-store";
import ToggleSidebar from "./ToggleSidebar";

interface HeaderProps {
  title: ReactNode;
  information?: ReactNode;
  metaItem?: ReactNode;
  className?: string;
  style?: any;
}

const CreateSurveyHeader = (props: HeaderProps) => {
  const { title, metaItem, style, information } = props;
  const padding = "30px 30px 10px 30px";
  const meta = useCheckSelector(userMetadataSelector);
  const marginLeft = meta.margin ? "60px" : "0px";
  const styles = { marginLeft, padding, ...style };
  const [show, setShow] = React.useState(false);

  return (
    <Navbar
      variant="light"
      className={`${props.className || ""}bg-white`}
      style={styles}
      fixed="top"
      expand="md"
    >
      <ToggleSidebar />
      <Navbar.Toggle
        onClick={() => setShow(!show)}
        className="ml-auto"
        aria-controls="responsive-navbar-nav"
      >
        <Icon icon={show ? "fal fa-minus-square" : "fal fa-plus-square"} />
      </Navbar.Toggle>
      <Navbar.Collapse id="responsive-navbar-nav">
        <Nav>{title}</Nav>
        {information && <Nav>{information}</Nav>}
        <Nav className="ml-auto">{metaItem}</Nav>
      </Navbar.Collapse>
    </Navbar>
  );
};

export default CreateSurveyHeader;
