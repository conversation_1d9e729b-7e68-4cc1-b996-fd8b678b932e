import { Outlet, Link, useLocation } from "react-router";
import { UsersIcon, FileIcon, MenuIcon, PencilIcon } from "lucide-react";
import { Button } from "@repo/ui/components/button";
import { useState } from "react";

import { ROUTES } from "@/routes";
import SharedLayout from "../Shared.Layout";

function DashboardLayout() {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const location = useLocation();

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  // Helper function to check if a route is active
  const isActive = (path: string) => {
    return location.pathname === path;
  };

  return (
    <SharedLayout>
      <div className="flex h-full w-full overflow-hidden">
        {/* Sidebar */}
        <aside
          // sidebarCollapsed ? "w-16" : "w-64"
          // }
          className={`h-full border-r bg-card flex flex-col transition-all duration-300 w-64 $`}
        >
          <nav className="flex-1 overflow-auto p-2">
            <ul className="space-y-1">
              <li>
                <Link
                  to={ROUTES.SETTINGS.EMPLOYEES}
                  className={`flex items-center gap-2 w-full px-3 py-2 text-sm rounded-md ${
                    isActive(ROUTES.SETTINGS.EMPLOYEES)
                      ? "bg-primary/10 text-primary font-medium"
                      : "text-muted-foreground hover:bg-muted hover:text-foreground"
                  } ${sidebarCollapsed ? "justify-center" : ""}`}
                >
                  <UsersIcon className="h-4 w-4 flex-shrink-0" />
                  {!sidebarCollapsed && <span>Employees</span>}
                </Link>
              </li>
              <li>
                <Link
                  to={ROUTES.SETTINGS.FILES}
                  className={`flex items-center gap-2 w-full px-3 py-2 text-sm rounded-md ${
                    isActive(ROUTES.SETTINGS?.FILES)
                      ? "bg-primary/10 text-primary font-medium"
                      : "text-muted-foreground hover:bg-muted hover:text-foreground"
                  } ${sidebarCollapsed ? "justify-center" : ""}`}
                >
                  <FileIcon className="h-4 w-4 flex-shrink-0" />
                  {!sidebarCollapsed && <span>Files</span>}
                </Link>
              </li>
              <li>
                <Link
                  to={ROUTES.SETTINGS.TEXT_TEMPLATES}
                  className={`flex items-center gap-2 w-full px-3 py-2 text-sm rounded-md ${
                    isActive(ROUTES.SETTINGS?.TEXT_TEMPLATES)
                      ? "bg-primary/10 text-primary font-medium"
                      : "text-muted-foreground hover:bg-muted hover:text-foreground"
                  } ${sidebarCollapsed ? "justify-center" : ""}`}
                >
                  <PencilIcon className="h-4 w-4 flex-shrink-0" />
                  {!sidebarCollapsed && <span>Text Templates</span>}
                </Link>
              </li>
            </ul>
          </nav>
        </aside>

        {/* Main Content */}
        <div className="flex flex-col flex-1 h-full overflow-hidden shadow">
          {/* Page Content */}
          <main className="flex-1 p-4">
            <Outlet />
          </main>
        </div>
      </div>
    </SharedLayout>
  );
}

export default DashboardLayout;
