import React from "react";
import { Input } from "@repo/ui/components/input";
import { Search } from "lucide-react";
import { ApiFile } from "../filesService";
import { Link } from "react-router";
import { ROUTES } from "@/routes";

interface FileDetailsHeaderProps {
  fileDetails: ApiFile | null;
  searchValue: string;
  onSearchChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  loadingFile: boolean;
  onDownloadFile?: () => void;
  onDownloadErrorLog?: () => void;
}

export const FileDetailsHeader: React.FC<FileDetailsHeaderProps> = ({
  fileDetails,
  searchValue,
  onSearchChange,
  loadingFile,
}) => {
  return (
    <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
      <div className="flex flex-col gap-2">
        <div className="flex items-center gap-2">
          <Link
            to={ROUTES.SETTINGS.FILES}
            className="text-sm text-blue-600 hover:text-blue-800"
          >
            ← Back to Files
          </Link>
        </div>
        <h1 className="text-2xl font-bold">
          {loadingFile ? "Loading..." : fileDetails?.title || "File Details"}
        </h1>
      </div>
      <div className="flex w-full flex-col gap-2 md:w-auto">
        <div className="relative">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
          <Input
            type="search"
            placeholder="Search employees..."
            className="w-full pl-8 md:w-[300px]"
            value={searchValue}
            onChange={onSearchChange}
          />
        </div>
      </div>
    </div>
  );
};
