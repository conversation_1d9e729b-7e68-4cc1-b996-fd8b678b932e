import React from "react";
import { Table } from "@repo/ui/components/table";
import { EmployeeTableHeader } from "../components/EmployeeTableHeader";
import { EmployeeTableBody } from "../components/EmployeeTableBody";
import { Employee } from "../useEmployees";

interface EmployeeTableProps {
  loadingUsers: boolean;
  employees: Employee[];
  handleSort: (field: keyof Employee | string) => void;
  getSortIcon: (field: keyof Employee | string) => React.ReactNode;
  onRowClick?: (employee: Employee) => void;
}

export const EmployeeTable: React.FC<EmployeeTableProps> = ({
  loadingUsers,
  employees,
  handleSort,
  getSortIcon,
  onRowClick,
}) => {
  return (
    <div className="rounded-md border">
      <Table>
        <EmployeeTableHeader
          handleSort={handleSort}
          getSortIcon={getSortIcon}
          employees={employees}
        />
        <EmployeeTableBody
          loadingUsers={loadingUsers}
          employees={employees}
          onRowClick={onRowClick}
        />
      </Table>
    </div>
  );
};
