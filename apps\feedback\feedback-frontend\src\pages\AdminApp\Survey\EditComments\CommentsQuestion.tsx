import React, { useEffect } from "react";
import { useDebounce, useTable, useXHR } from "unmatched/hooks";
import {
  Div,
  PageContainer,
  Text,
  Button,
  Icon,
  Table,
  Layout,
  // ComboMultiFilter,
  FormControl,
} from "unmatched/components";
import CustomHeader from "../../Shared/CustomHeader/CustomHeader";
import util from "unmatched/utils";
import { useHistory, useParams } from "react-router-dom";
import { getAllCommentQuestion } from "../survey-api";
import { getAdminSurveyInfoFactV2 } from "pages/UserApp/Dashboard/dashboard-api";
import icons from "assets/icons/icons";
import appUrls from "unmatched/utils/urls/app-urls";
// import PropTypes from 'prop-types';
const { Paste } = icons;

// const COHARTS = [
//   { key: "questions", title: "Questions" },
//   { key: "comments", title: "Comments" },
// ];

// const APPLIED_DATA = [
//   { key: "Option1", title: "Option1" },
//   { key: "Option2", title: "Option2" },
// ];

const Comments = () => {
  const params = useParams<any>();

  const history = useHistory();
  const tableMeta = useTable({
    totalPages: 0,
    size: 20,
    page: 1,
  });
  const [searchTerm, setSearchTerm] = React.useState<any>(null);
  const [isEditing, setEditing] = React.useState(false);
  const [survey, setSurvey] = React.useState<any>({});

  useDebounce(
    () => {
      if (isEditing) return getQuestions({});
    },
    1500,
    [searchTerm]
  );

  useEffect(() => {
    const getSurvey = async (id: any) => {
      const res = await getAdminSurveyInfoFactV2(id);
      setSurvey(res?.data || {});
    };
    getSurvey(params.id);
  }, []);

  // const [cohart] = React.useState({
  //   cohart: {
  //     options: COHARTS,
  //     selected: "Question",
  //   },
  //   applied: {
  //     options: APPLIED_DATA,
  //     selected: [],
  //   },
  // });

  const comments = useXHR({ defaultResponse: [] });

  // const survey = useXHR({
  //   defaultResponse: {
  //     name: "Upward Review March 2020",
  //     id: "",
  //   },
  // });

  const [columnsData] = React.useState<any>({
    qNo: { label: "Q.No" },
    question: { label: "Questions" },
    // comment: { label: "Comment" },
    bookmark: { label: "Total Comments" },
    reviewed: { label: "Reviewed" },
    unreviewed: { label: "Unreviewed" },
    editComment: { label: "" },
  });

  const getColumns = () => {
    const coumnsList = util.lib.keys(columnsData);
    return util.lib.map(coumnsList, (key: string) => ({
      ...util.lib.get(columnsData, key),
      key,
    }));
  };

  const getQuestions = (_params?: any) => {
    comments.setLoading(true);
    getAllCommentQuestion({
      index_id: params.id,
      _params,
      search: searchTerm?.length > 0 ? searchTerm : undefined,
    }).then((data) => {
      // console.log(data);
      comments.onSuccess(data.results);
      comments.setLoading(false);
    });
    // getQuestionsFact(_params).then(
    //   (response: any) => {
    //     comments.onSuccess(COMMENTS_DATA);
    //   },
    //   (err: any) => {
    //     comments.onError(err);
    //     toastr.onError(err);
    //   }
    // );
  };

  // const downloadComments = () => '';

  const onPageMount = () => {
    getQuestions();
  };

  React.useEffect(onPageMount, []);

  const getHeaderTitleTemplate = () => {
    return (
      <>
        <Text.H2 className="mb-2"> {survey.title}</Text.H2>
        {/* <Text.P1 className={util.getUtilClassName({ pb: 2, pt: 1 })}>
          <Link
            className={util.getUtilClassName({ textColor: "dark" })}
            to={util.appUrls.admin.survey.default}
          >
            <Icon icon="fal fa-chevron-left mr-2" /> Manage Surveys
            <Text.H3 className={util.getUtilClassName({ d: "inline" })}>
            </Text.H3>
          </Link>
        </Text.P1> */}
      </>
    );
  };

  // const getHeaderMetaTemplate = () => {
  //   return (
  //     <Button variant="outline-primary" onClick={downloadComments}>
  //       <Icon icon="fas fa-file-download" /> Download
  //     </Button>
  //   );
  // };

  const getFiltersTemplate = () => {
    return (
      <Div className="py-3 row">
        {/* <Div className="col">
          <ComboMultiFilter
            cohart={cohart.cohart}
            applied={cohart.applied}
            onCohartUpdate={() => ""}
            onAppliedUpdate={() => ""}
          />
        </Div> */}
        <Div className="col" style={{ maxWidth: 350 }}>
          <FormControl.Search
            placeholder="Search for questions"
            value={searchTerm}
            onChange={(evt: any) => {
              setEditing(true);
              setSearchTerm(evt.target.value);
            }}
          />
        </Div>
      </Div>
    );
  };

  // const getCommentCellTemplate = (item: any) => {
  //   return (
  //     <Div>
  //       <Text.P1>{item.comment}</Text.P1>
  //       <Layout.Flex>
  //         <Layout.FlexItem>
  //           <Text.P2
  //             className={util.getUtilClassName({
  //               px: 2,
  //               py: 1,
  //               bg: "grey-light",
  //             })}
  //           >
  //             Edited on {item.updatedDate}
  //           </Text.P2>
  //         </Layout.FlexItem>
  //         <Layout.FlexItem>
  //           <Button
  //             className={util.getUtilClassName({ py: 0 })}
  //             variant="link"
  //             onClick={() => onViewComment(item)}
  //           >
  //             View Original
  //           </Button>
  //         </Layout.FlexItem>
  //       </Layout.Flex>
  //     </Div>
  //   );
  // };

  // const getBookmarkCellTemplate = (item: any) => {
  //   const variant = item.bookmarked ? "primary" : "muted";
  //   const icon = item.bookmarked ? "fas fa-bookmark" : "fal fa-bookmark";
  //   return <Icon icon={icon} variant={variant} />;
  // };

  // const getReviewCellTemplate = (item: any) => {
  //   const variant = item.review ? "primary" : "muted";
  //   const icon = item.bookmarked
  //     ? "fas fa-check-circle"
  //     : "fal fa-check-circle";
  //   return <Icon icon={icon} variant={variant} />;
  // };

  return (
    <PageContainer>
      <CustomHeader
        title={getHeaderTitleTemplate()}
        metaItem={
          <></>
          //getHeaderMetaTemplate()
        }
        breadcrumbs={[
          {
            label: "Surveys list",
            icon: <Paste className="grey-icon__svg" />,
            route: `${appUrls.admin.survey.default}?filter=ENDED`,
          },
          { label: "Comment Review" },
        ]}
      />
      <Layout.Container fluid className={util.getUtilClassName({ pt: 4 })}>
        <Div>{getFiltersTemplate()}</Div>
        <Div>
          <Table
            columns={getColumns()}
            isLoading={comments.isLoading}
            type="striped"
            rows={comments.data}
            render={(item: any, index: number) => {
              return (
                <>
                  <Table.Data centered width="30px">
                    <Text.P1
                      onClick={() => {
                        util.appUrls.admin.survey.editComments.viewComments(
                          params.id,
                          item.id
                        );
                      }}
                    >
                      {index + 1}
                    </Text.P1>
                  </Table.Data>
                  <Table.Data>
                    <Text.P1
                      onClick={() => {
                        history.push(
                          util.appUrls.admin.survey.editComments.viewComments(
                            params.id,
                            item.id
                          )
                        );
                      }}
                    >
                      {item.question}
                    </Text.P1>
                  </Table.Data>
                  <Table.Data width="130px">
                    <Text.P1
                      onClick={() => {
                        history.push(
                          util.appUrls.admin.survey.editComments.viewComments(
                            params.id,
                            item.id
                          )
                        );
                      }}
                    >
                      {item.total}
                    </Text.P1>
                  </Table.Data>
                  <Table.Data width="30px">
                    {/* {getBookmarkCellTemplate(item)} */}
                    <Text.P1
                      onClick={() => {
                        history.push(
                          util.appUrls.admin.survey.editComments.viewComments(
                            params.id,
                            item.id
                          )
                        );
                      }}
                    >
                      {item.reviewed}
                    </Text.P1>
                  </Table.Data>
                  <Table.Data width="30px">
                    <Text.P1
                      onClick={() => {
                        history.push(
                          util.appUrls.admin.survey.editComments.viewComments(
                            params.id,
                            item.id
                          )
                        );
                      }}
                    >
                      {item.unreviewed}
                    </Text.P1>
                    {/* {getReviewCellTemplate(item)} */}
                  </Table.Data>
                  <Table.Data width="30px">
                    <Button
                      className={`${util.getUtilClassName({
                        p: 0,
                      })}  text-muted`}
                      onClick={() => {
                        history.push(
                          util.appUrls.admin.survey.editComments.viewComments(
                            params.id,
                            item.id
                          )
                        );
                      }}
                      variant="link"
                    >
                      <Icon icon="fal fa-pencil" />
                    </Button>
                  </Table.Data>
                </>
              );
            }}
            onPageSelect={(number: number) => {
              tableMeta.onPageSelect(number);
              getQuestions({ page: number });
            }}
            hasPagination
            activePage={tableMeta.page}
            pages={tableMeta.totalPages}
          />
        </Div>
      </Layout.Container>
    </PageContainer>
  );
};

// Comments.propTypes = {

// }

export default Comments;
