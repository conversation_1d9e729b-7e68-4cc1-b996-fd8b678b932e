import React from "react";
import {
  Text,
  Div,
  Layout,
  Button,
  CustomModal as Modal,
  Icon,
} from "unmatched/components";
import { getSampleEmployeeListDownload } from "../../dataload-api";

export default function AddEmployeeScreen1(props: any) {
  const { setScreen, loading, data } = props;
  return (
    <>
      <Modal.Body>
        <Text.P1 className="mt-3 text-justify">
          Add employees list to the platform with below fields. You can download
          a sample excel or csv file to work on the employee data and upload it
          to the platform. A few of the below fields are mandatorily required
          fields to make those employee entries valid.
        </Text.P1>
        <Text.H2 className="pb-2 mt-3">Required fields :</Text.H2>
        {data.map((_d: string, i: number) => (
          <Text.P1 className="pb-2" key={i}>
            {_d}
          </Text.P1>
        ))}
        {/* <Text.P1 className="pb-2">Rater Email</Text.P1>
        <Text.P1 className="pb-2">Rater First Name</Text.P1>
        <Text.P1 className="pb-2">Rater Last Name</Text.P1>
        <Text.P1 className="pb-2">Rater Employee ID</Text.P1>
        <Text.P1 className="pb-2">Target Email</Text.P1>
        <Text.P1 className="pb-2">Target First Name</Text.P1>
        <Text.P1 className="pb-2">Target Last Name</Text.P1>
        <Text.P1 className="pb-2">Target Email</Text.P1> */}
      </Modal.Body>
      <Modal.Footer>
        <Div
          style={{
            width: "100%",
          }}
        >
          <Layout.Row>
            <Layout.Col className="col-7">
              <Text.P1
                className="pb-0 pointer"
                style={{ fontWeight: 500 }}
                onClick={() => getSampleEmployeeListDownload({ f: "xlsx" })}
              >
                <Icon icon="far fa-arrow-alt-circle-down mr-1" />
                <u>Download sample excel(.xlsx) file</u>
              </Text.P1>
            </Layout.Col>
            <Layout.Col>
              <Button
                className="float-right"
                type="button"
                variant="primary"
                size="lg"
                onClick={() => setScreen(2)}
                disabled={loading}
              >
                Continue to file upload
              </Button>
            </Layout.Col>
          </Layout.Row>
        </Div>
      </Modal.Footer>
    </>
  );
}
