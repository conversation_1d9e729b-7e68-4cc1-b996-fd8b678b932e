import { Div } from "unmatched/components";
import { QUESTION } from "../../survey-enums";
import { useRef } from "react";
import { useOutsideClick } from "unmatched/hooks/useOutsideClick";

export const SectionOptions = (props: any) => {
  const {
    section,
    createSection,
    setOpenSectionID,
    createInstruction,
    createSpecificQuestion,
  } = props;

  const optionsWrapRef = useRef(null);

  useOutsideClick(optionsWrapRef, () => setOpenSectionID(null));

  return (
    <div ref={optionsWrapRef} className="section-options-dropdown">
      <Div
        className="opt"
        onClick={() => {
          createSection();
          setOpenSectionID(null);
        }}
      >
        New Section
      </Div>
      <hr />
      <Div
        className="opt pb-3"
        onClick={() => {
          createInstruction(section.id);
          setOpenSectionID(null);
        }}
      >
        Instructions
      </Div>
      <Div
        className="opt pb-3"
        onClick={() => {
          createSpecificQuestion(section.id, QUESTION.RADIO);
          setOpenSectionID(null);
        }}
      >
        Radio Question
      </Div>
      <Div
        onClick={() => {
          createSpecificQuestion(section.id, QUESTION.CHECKBOX);
          setOpenSectionID(null);
        }}
        className="opt pb-3"
      >
        Checkbox Question
      </Div>
      <Div
        className="opt pb-3"
        onClick={() => {
          createSpecificQuestion(section.id, QUESTION.RATING);
          setOpenSectionID(null);
        }}
      >
        Rating Question
      </Div>
      <Div
        className="opt"
        onClick={() => {
          createSpecificQuestion(section.id, QUESTION.INPUT);
          setOpenSectionID(null);
        }}
      >
        Input Question
      </Div>
    </div>
  );
};
