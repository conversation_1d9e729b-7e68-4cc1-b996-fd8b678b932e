import { filterReducer } from "./CommonFilters/slice";
import { createSlice } from "@reduxjs/toolkit";
import { persistReducer } from "redux-persist";
import storage from "redux-persist/lib/storage";
import { sessionReducer } from "unmatched/modules/session/slice";
import { toastrReducer } from "unmatched/modules/toastr/slice";
import adminReducer from "./AdminApp/admin-app-store";
import userReducer from "./UserApp/user-app-store";

const config = {
  key: "meta",
  storage,
};

const rootReducers = {
  toastr: toastrReducer,
  filters: filterReducer,
  session: sessionReducer,
  admin: adminReducer,
  user: userReducer,
  meta: persistReducer(
    config,
    createSlice({
      name: "meta",
      initialState: {
        id: 1,
      },
      reducers: {
        setFilters: (state: any) => {
          state.id += 1;
        },
        // register: () => '',
      },
    }).reducer
  ),
};

export default rootReducers;
