import React from "react";
import { Text, Layout, Button, Div } from "unmatched/components";
import { <PERSON><PERSON><PERSON><PERSON>, ModalFooter } from "./CreateDataLoad.style";

export default function SelectDataLoad(props: any) {
  const { setModalType } = props;
  return (
    <>
      <ModalBody>
        <Div
          className="d-flex align-items-center"
          style={{ minHeight: "50vh" }}
        >
          <Layout.Row>
            <Layout.Col>
              <Text.H2 className="pb-2">Add Employees</Text.H2>
              <Text.P1 className="pb-4">
                Add your employee data to publish and send out surveys. Add your
                employee data to publish and send out surveys.Add your employee
                data to publish and send out surveys.
              </Text.P1>
              <Button
                type="button"
                variant="primary"
                size="lg"
                onClick={() => setModalType("addEmployee")}
              >
                Continue
              </Button>
            </Layout.Col>
            <Layout.Col>
              <Text.H2 className="pb-2">Add Pairings</Text.H2>
              <Text.P1 className="pb-4">
                If you’ve already added your employee data, you can add pairings
                for the same. If you’ve already added your employee data, you
                can add pairings for the same.
              </Text.P1>
              <Button
                type="button"
                variant="primary"
                size="lg"
                onClick={() => setModalType("addPair")}
              >
                Continue
              </Button>
            </Layout.Col>
          </Layout.Row>
        </Div>
      </ModalBody>
      <ModalFooter></ModalFooter>
    </>
  );
}
