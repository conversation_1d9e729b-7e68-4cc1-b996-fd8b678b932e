// Node modules
import React from "react";
import _ from "lodash";
import { Redirect, Route } from "react-router-dom";
// Helpers
import { useHistory, useLayout } from "unmatched/hooks";
import DASHBOARD_ROUTES from "./admin-app-routes";
import appUrls from "unmatched/utils/urls/app-urls";
import Provider, { useAdminContext } from "./Provider";
// Components
import { Div, Layout } from "unmatched/components";
import AppRoutes from "../AppRoutes";
import Sidebar from "./Sidebar/Sidebar";
import useSession from "unmatched/modules/session/hook";
// import Header from "./Header/Header";
import util from "unmatched/utils";
import { PasswordModalContext } from "pages/App";
import styled from "styled-components";
import Profile from "./Profile/Profile";
import { Apps } from "pages/Apps/Apps";
import APPS from "assets/images/Apps.svg";
import LOGO from "assets/images/unmatched.svg";
import ARROWD from "assets/images/arrowDown.svg";
import ARROWR from "assets/images/arrowRight.svg";
// import "./layout.scss"; // eslint-disable-line

export const MiniLogoWrap = styled.div`
  padding: 20px 0;
  display: flex;
  justify-content: center;
  border-bottom: 1px solid #2c3851;
  width: 80%;
  margin: auto;
  cursor: pointer;
`;

const ProfileWrap = styled.div`
  position: absolute;
  bottom: 25px;
  width: 100%;
  text-align: center;
`;

const layoutKeys = ["width", "sidebar", "setSidebar", "setMargin"];

const AdminApp = () => {
  const context = useAdminContext();
  const layout = useLayout(_.pick(context, layoutKeys));
  const { user, client } = useSession();
  const history = useHistory();

  const isAdmin = util.canAccess(
    user?.role || "",
    util.enums?.Modules?.Administration,
    "canAccess"
  );

  const onOverlayClick = () => {
    context.setSidebar(false);
  };

  const onLogout = async () => {
    history.push(appUrls.logout);
  };

  if (!isAdmin) {
    return <Redirect to={appUrls.user.dashboard.surveyList} />;
  }

  return (
    <Div>
      {/* <Div>
        <PasswordModalContext.Consumer>
          {({ toggle }) => (
            <Header
              onLogout={onLogout}
              name={user && user.firstName ? user.firstName[0] : "U"}
              togglePasswordModal={toggle}
            />
          )}
        </PasswordModalContext.Consumer>
      </Div> */}
      <Div>
        {layout.sidebar.show ? (
          <Layout.Sidebar
            className="bg-secondary text-light"
            onOverlayClick={onOverlayClick}
            overlay={layout.hasOverlay}
            width={layout.sidebar.width}
          >
            <Apps
              client={client}
              images={{
                APPS,
                LOGO,
                ARROWD,
                ARROWR
              }}
            />
            <Sidebar />
            <ProfileWrap>
              <PasswordModalContext.Consumer>
                {({ toggle }) => (
                  <Profile
                    onLogout={onLogout}
                    name={user && user.firstName ? user.firstName[0] : "U"}
                    togglePasswordModal={toggle}
                  />
                )}
              </PasswordModalContext.Consumer>
            </ProfileWrap>
          </Layout.Sidebar>
        ) : (
          <></>
        )}
        <Div style={layout.content.styles}>
          <AppRoutes routes={DASHBOARD_ROUTES}>
            <Route exact path={appUrls.admin.default}>
              <Redirect to={appUrls.admin.survey.default} />
            </Route>
          </AppRoutes>
        </Div>
        <div id="profile-root" />
      </Div>
    </Div>
  );
};

const Wrapper = (props: any) => (
  <Provider>
    <AdminApp {...props} />
  </Provider>
);

export default Wrapper;
