import Header from "pages/UserApp/Header/Header";
import React from "react";
import util from "unmatched/utils";
import Confidentiality from "./Confidentiality";

// import PropTypes from 'prop-types'

const ConfidentialityContainer = () => {
  return (
    <div className={util.getUtilClassName({ mt: 5, pt: 4 })}>
      <Header hideOptions />
      <Confidentiality />
    </div>
  );
};

// Faq.propTypes = {

// }

export default ConfidentialityContainer;
