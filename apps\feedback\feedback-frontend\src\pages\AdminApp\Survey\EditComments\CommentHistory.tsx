import React from "react";
import { Div, Modal, Text, Card, Layout } from "unmatched/components";
import util from "unmatched/utils";
import ModalHeader from "../../ModalHeader";
// import PropTypes from 'prop-types'

const Pannel = (props: { children: React.ReactNode; title: string }) => {
  return (
    <Div className={util.getUtilClassName({ pb: 4 })}>
      <Text.H3>{props.title}</Text.H3>
      <Div className={util.getUtilClassName({ py: 2 })}>{props.children}</Div>
    </Div>
  );
};

const AuditPanel = (props: {
  comment: string;
  name: string;
  time: string;
  date: string;
  country: string;
  ip: string;
}) => {
  return (
    <Card noShadow className={util.getUtilClassName({ mb: 4 })}>
      <Card.Body>
        <Layout.Row>
          <Layout.Col></Layout.Col>
          <Layout.Col xl={10} lg={9} md={9} sm={9} xs={3}>
            <Text.H3>{props.comment}</Text.H3>
            <Text.P1>
              {props.name} | {props.time}, {props.date} | {props.country} |{" "}
              {props.ip}
            </Text.P1>
          </Layout.Col>
        </Layout.Row>
      </Card.Body>
    </Card>
  );
};

const CommentHistory = (props: any) => {
  const { onHide, show, selected } = props;
  return (
    <Modal
      dialogClassName={util.getUtilClassName({
        ml: "auto",
        mr: 0,
        py: 0,
      })}
      show={show}
      size="lg"
      scrollable
    >
      <ModalHeader title="Edit Comment" onHide={onHide} />
      <Modal.Body>
        <Pannel title="Category">{selected.category}</Pannel>
        <Pannel title="Question">{selected.question}</Pannel>
        <Pannel title="Original Comment">{selected.comment}</Pannel>
        <Pannel title="Edited Comment">{selected.comment}</Pannel>
        <Pannel title="Audit Log">
          <AuditPanel
            comment={selected.comment}
            name="Jane Doe"
            time={"13:35"}
            date={"05/06/2021"}
            country={"London, UK"}
            ip={"**************"}
          />
          <AuditPanel
            comment={selected.comment}
            name="Jane Doe"
            time={"13:35"}
            date={"05/06/2021"}
            country={"London, UK"}
            ip={"**************"}
          />
        </Pannel>
      </Modal.Body>
    </Modal>
  );
};

const dummyFunction = () => "";

CommentHistory.defaultProps = {
  onHide: dummyFunction,
  show: false,
  selected: {},
};

export default CommentHistory;
