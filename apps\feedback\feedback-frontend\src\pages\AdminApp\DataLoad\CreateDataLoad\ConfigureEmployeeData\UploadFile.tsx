import React from "react";
import {
  Text,
  Layout,
  Div,
  Button,
  FormGroup,
  FormControl,
  CustomModal as Modal,
} from "unmatched/components";
import data from "../../../../../assets/images/images";
import icons from "assets/icons/icons";
import Loader from "../../../../../assets/images/Loader";
import { useDropzone } from "react-dropzone";
import {
  // Modal,,
  FileCard,
  CancelButton,
} from "../CreateDataLoad.style";
import {
  employeeFileUploadFact,
  // patchUserUploadFact,
} from "../../dataload-api";
import useToastr from "unmatched/modules/toastr/hook";

const activeStyle = {
  borderColor: "#2196f3",
};
const rejectStyle = {
  borderColor: "#2196f3",
};

const uploadBox = {
  width: "100%",
  minHeight: "40vh",
  // border: "1px dashed #494949",
  borderColor: "rgba(0,0,0,0.1)",
  borderStyle: "dashed",
  padding: "5% 5%",
  fontSize: "14px !important",
};
const isActiveDiv = {
  background: "rgba(0,0,0,0.1)",
};

const stylee = {
  uploadText: {
    textDecoration: "underline",
    cursor: "pointer",
  },
};

const Uploadfile = (props: any) => {
  const { setScreen, setRes } = props;
  const { showToast } = useToastr();
  const [Stage, setStage] = React.useState(0);
  const [File, setFile] = React.useState<any>([]);
  const [loading, setLoading] = React.useState(false);
  // const [ file, setFile] = React.useState()

  // const [uploadResponse, setUploadResponse] = React.useState<any>({})

  const [_params, setParams] = React.useState({
    id: "",
    title: "",
    tags: [],
  });
  const onSubmit = async () => {
    // if (_params.title.length === 0) {
    //   setFile([]);
    //   setStage(0);
    // }
    if (_params.title.length === 0) {
      return showToast({
        variant: "danger",
        title: "Add title",
        content: "Title is compulsory.",
      });
    }
    try {
      setLoading(true);
      setScreen(3);
      tryUpload({..._params});
      // await patchUserUploadFact(_params.id, _params);
      
    } catch (err) {
      setLoading(false);
      setScreen(2)
      showToast({
        variant: "danger",
        title: "Something went wrong",
        content: "Try again.",
      });
    }
  };

  // React.useEffect(() => {
  //   setFile([]);
  // }, []);

  const {
    // acceptedFiles,
    // fileRejections,
    getRootProps,
    getInputProps,
    isDragActive,
    isDragAccept,
    isDragReject,
  } = useDropzone({
    accept: ".xlsx, .xls, .csv",
    maxFiles: 1,
    onDrop: (acceptedFiles) => {
      setFile(
        acceptedFiles.map((file) =>
          Object.assign(file, {
            preview: URL.createObjectURL(file),
          })
        )
      );
      if (
        acceptedFiles.map((file) =>
          Object.assign(file, {
            preview: URL.createObjectURL(file),
          })
        ).length > 0
      ) {
        setStage(2);
      }
    },
  });

  const tryUpload = async ({ title, tags} : any) => {
    // debugger
    try {
      const extArray = File[0].name.split(".");
      const ext = extArray[extArray.length - 1];
      const response = await employeeFileUploadFact({
        data: File[0],
        format: ext,
        title,
        tags
      });
      setRes({
        uploadCount: response.data.success_upload_count,
        userAdded: response.data.users_added_count,
        userUpdated: response.data.users_updated_count,
      });
      setStage(3);
      // upLoad;
    } catch (err: any) {
      console.log(err);
      setScreen(2)
      setStage(1);
      return showToast({
        variant: "danger",
        title: "Something went wrong.",
        content: err?.msg?.error,
      });
    }
  };

  const styledBox = React.useMemo(
    () => ({
      ...uploadBox,
      ...(isDragActive ? activeStyle : {}),
      ...(isDragAccept ? isActiveDiv : {}),
      ...(isDragReject ? rejectStyle : {}),
    }),
    [isDragActive, isDragReject, isDragAccept]
  );

  const viewStage = (screen: number) => {
    switch (screen) {
      case 0:
        return (
          <Upload
            getRootProps={getRootProps}
            getInputProps={getInputProps}
            styledBox={styledBox}
          />
        );
      case 1:
        return <OnUploadStart setStage={setStage} file={File[0]} />;

      case 2:
        return (
          <OnUploadFinish
            setStage={setStage}
            file={File[0]}
            _params={_params}
            setParams={setParams}
            onSubmit={onSubmit}
          />
        );

      default:
        break;
    }
  };
  return (
    <>
      <Modal.Body>
        <Text.H2 className="pb-2 text-capitalize">Add your employees</Text.H2>
        <Text.P1 className="pb-4">
          Add employees list to the platform with below fields. You can download
          a sample excel or csv file to work on the employee data and upload it
          to the platform. A few of the below fields are mandatorily required
          fields to make those employee entries valid.
        </Text.P1>
        {viewStage(Stage)}
      </Modal.Body>
      <Modal.Footer>
        {Stage === 2 ? (
          <Div className="w-100">
            <Layout.Row>
              <Layout.Col></Layout.Col>
              <Layout.Col>
                <Button
                  className="float-right"
                  type="button"
                  variant="primary"
                  size="lg"
                  disabled={loading}
                  onClick={onSubmit}
                >
                  {loading ? "Please wait..." : "Next"}
                </Button>
              </Layout.Col>
            </Layout.Row>
          </Div>
        ) : (
          ""
        )}
      </Modal.Footer>
    </>
  );
};
const Upload = (props: any) => {
  const { PAIRICON } = data;
  const { getRootProps, getInputProps, styledBox } = props;
  return (
    <>
      {/* <section className="container"> */}
      <div
        {...getRootProps({
          style: styledBox,
          className:
            "d-flex justify-content-center align-items-center flex-column text-center",
        })}
      >
        <input {...getInputProps()} />
        <Text.H3 className="pb-2 mb-3 f10">
          Simply drag & drop the file here to Upload or{" "}
          <span style={stylee.uploadText} className="text-primary">
            browse and choose your file
          </span>
        </Text.H3>
        <img src={PAIRICON} alt="" />
      </div>
    </>
  );
};
const OnUploadStart = (props: any) => {
  const { setStage, file } = props;
  React.useEffect(() => {
    // setTimeout(() => {
    //   setStage(2);
    // }, 3000);
  }, [setStage]);
  return (
    <>
      <div
        style={uploadBox}
        className="d-flex justify-content-center align-items-center flex-column text-center"
      >
        <Loader size={105} />
        <Text.H2 className="pb-2 text-primary">
          Uploading Your File, Please wait.
        </Text.H2>
        <Text.P1 className="pb-0">{file?.name}</Text.P1>
      </div>
    </>
  );
};

const OnUploadFinish = (props: any) => {
  const { file, setStage, setParams } = props;
  const { SuccessTick } = icons;

  return (
    <>
      <Text.P1 className="pb-2 font-weight-bold">1 File uploaded</Text.P1>
      <FileCard className="f-12">
        <Layout.Row className="justify-content-center align-item-center">
          <Layout.Col className="col-1">
            <SuccessTick />
          </Layout.Col>
          <Layout.Col className="col-8">{file?.name}</Layout.Col>
          <Layout.Col className="col-3">
            <CancelButton onClick={() => setStage(0)}>Remove</CancelButton>
          </Layout.Col>
        </Layout.Row>
      </FileCard>
      <Div className="mt-3" style={{ maxWidth: 300 }}>
        <FormGroup>
          <FormGroup.Label>Title *</FormGroup.Label>
          <FormControl.Text
            placeholder="Give a title for the records"
            onChange={(e: any) =>
              setParams((_d: any) => {
                return { ..._d, title: e.target.value };
              })
            }
          />
        </FormGroup>
        <FormGroup>
          <FormGroup.Label>Tags</FormGroup.Label>
          <FormControl.Text
            placeholder="Remote, NY, Banking, etc.."
            onChange={(e: any) =>
              setParams((_d: any) => {
                return { ..._d, tags: e.target.value.split(",") };
              })
            }
          />
        </FormGroup>
      </Div>
    </>
  );
};

export default Uploadfile;
