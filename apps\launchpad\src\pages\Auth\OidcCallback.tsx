// import { Loader, util } from "@unmatchedoffl/ui-core";
import { useEffect, useState } from "react";
import { useQuery, useXHR } from "../../unmatched/hooks";
import { getUserFact } from "../../unmatched/modules/session/api";
import useSession from "../../unmatched/modules/session/hook";
import { getBEToken } from "./auth.api";
import { Loader2 } from "lucide-react";

export default function OidcCallback() {
  const session = useSession();
  const query = useQuery();
  const [IDToken, setIDToken] = useState<string | null>(null);
  const user = useXHR();

  const getUserData = async (loginResponse: any) => {
    try {
      const { token } = loginResponse;
      const response = await getUserFact({ token }, user.getMetaConfig());
      onLoginSuccess(response, loginResponse);
    } catch (err: any) {
      //
    }
  };

  const onLoginSuccess = (response: any, { token, expiry }: any) => {
    session.login({
      token,
      expiry,
      user: response,
      app: query.get("app_root"),
      oidc: true,
      // ...(session.client?.homepage === "FEEDBACK" &&
      //   response.role === util.enums.Roles.USER && {
      //     redirectToFeedback: true,
      //     page: session.client?.homepagePath,
      //   }),
    });
  };

  useEffect(() => {
    const iDToken = query.get("access_token");
    if (iDToken && !IDToken) {
      setIDToken(iDToken);
    }
    const url = window.location.href;
    if (!url.includes("?id_token")) {
      window.location.href = url.replace("#/id_token", "?id_token");
    }
  }, []);

  useEffect(() => {
    if (IDToken) {
      getBEToken({ token: IDToken }).then((res: any) => {
        getUserData(res.data);
      });
    }
  }, [IDToken]);

  return (
    <div>
      <Loader2 className="animate-spin" size={125} />
      <span>Please wait...</span>
    </div>
  );
}
