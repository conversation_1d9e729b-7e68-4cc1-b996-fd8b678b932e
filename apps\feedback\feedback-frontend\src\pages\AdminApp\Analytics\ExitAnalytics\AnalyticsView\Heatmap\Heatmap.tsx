import React, { useEffect } from "react";
import {
  Div,
  Layout,
  Text,
  // FormGroup,
  // FormControl,
  Table,
  HeatMapTable,
  Button,
} from "unmatched/components";
import {
  getQuestionComparative,
  getSectionComparative,
} from "../../exit-analytics-api";
import ItemList from "pages/AdminApp/Analytics/ItemList";
import { useDebounce } from "react-use";
import { OverlayTrigger, Tooltip } from "react-bootstrap";
// import { useTable } from "unmatched/hooks";
// import { TABLE_DATA } from "./time-series-meta";
// import PropTypes from 'prop-types';

const Heatmap = (props: {
  // demographicSchema: any;
  filtersState: any;
  sectionAndQuestion: any;
  surveyInfo: any;
}) => {
  const {
    //demographicSchema,
    filtersState,
    sectionAndQuestion,
    surveyInfo,
  } = props;
  const [showItems, toggleItems] = React.useState(false);
  // const tableMeta = useTable({});
  // const [years] = React.useState(TABLE_DATA);

  // const getCheckAllTemplate = () => (
  //   <FormGroup className="pb-1">
  //     <FormControl.Checkbox>
  //       <FormControl.Checkbox.Input
  //         checked={tableMeta.selectAll}
  //         onChange={(evt: any) => onCheckAll(evt.target.checked)}
  //       />
  //     </FormControl.Checkbox>
  //   </FormGroup>
  // );

  // const checkSelected = (item: any) => {
  //   return tableMeta.isSelected(item.id);
  // };

  // const onSelect = (item: any) => {
  //   tableMeta.onSelect(item, "id");
  // };

  // const onCheckAll = (checked: boolean) => {
  //   tableMeta.onSelectAll(years, checked, "id");
  // };
  const [questions, setQuestion] = React.useState([]);

  const [itemCategoryHeatmap, setItemCategoryHeatmap] = React.useState({
    selected: "",
    placeholder: "-",
    selectedValue: "",
    options: [],
  });
  const [categoryHeatmap, setCategoryHeatmap] = React.useState({
    selected: "",
    placeholder: "-",
    selectedValue: "",
    options: [],
  });

  
  useEffect(() => {
    const options: any = [];
    Object.keys(filtersState.filters).map((key, index) => {
      return options.push({
        title: filtersState.filters[key].label,
        id: index,
        value: key,
      });
    });
    
    // // demographicSchema.map((_dt: any, index: number) => {
    //   return options.push({
    //     title: `${_dt.title} (Demographics)`,
    //     id: Object.keys(filtersState.filters).length + index,
    //     value: `demographics__${_dt.title}`,
    //   });
    // });
    setCategoryHeatmap((_d: any) => {
      return {
        ..._d,
        options,
        selectedValue: options[0]?.value || "",
        selected: options[0]?.title || "",
      };
    });
    setItemCategoryHeatmap((_d: any) => {
      return {
        ..._d,
        options,
        selectedValue: options[0]?.value || "",
        selected: options[0]?.title || "",
      };
    }); //demographicSchema
  }, [filtersState]);

  useEffect(() => {
    const arr: any = [];
    sectionAndQuestion.forEach((el: any) =>
      el.questions
        .filter((_d: any) => _d.type === "Rating")
        .map((_d: any) => arr.push(_d.key))
    );
    setQuestion(arr);
  }, [sectionAndQuestion]);

  const onOptionSelect = (fn: Function, item: any) => {
    fn((_d: any) => {
      return { ..._d, selected: item.title, selectedValue: item.value };
    });
  };

  const [sectionData, setSectionData] = React.useState({
    row: [],
    col: [],
  });
  const [itemData, setItemData] = React.useState({
    row: [],
    col: [],
  });

  const categoryCall = async () => {
    try {
      const res = await getSectionComparative({
        indexId: surveyInfo.id,
        compareGroup: categoryHeatmap.selectedValue,
      });
      setSectionData(res);
    } catch (err) {
      // console.log(err);
    }
  };

  const itemCall = async () => {
    try {
      const res = await getQuestionComparative({
        indexId: surveyInfo.id,
        compareGroup: itemCategoryHeatmap.selectedValue,
        questions,
      });
      
      setItemData(res);
    } catch (err) {
      // console.log(err);
    }
  };

  useDebounce(
    () => {
      if (itemCategoryHeatmap.selected === "" || questions.length === 0) return;
      itemCall();
    },
    500,
    [itemCategoryHeatmap, questions]
  );
  useDebounce(
    () => {
      if (categoryHeatmap.selected === "") return;
      categoryCall();
    },
    500,
    [categoryHeatmap]
  );

  // const rows = [
  //   {
  //     id: 1,
  //     label: "Question 2",
  //     firmAverage: 2,
  //     departmentAverage: 1.5,
  //     practiceGroupAverage: 4.5,
  //     titleAverage: 2,
  //     selfAverage: 1.5,
  //     year: 2019,
  //     categoryAverage: 4.5,
  //   },
  //   {
  //     id: 2,
  //     label: "Question 3",
  //     firmAverage: 2,
  //     departmentAverage: 3.5,
  //     practiceGroupAverage: 4.5,
  //     titleAverage: 2,
  //     selfAverage: 0.5,
  //     year: 2019,
  //     categoryAverage: 0.5,
  //   },
  //   {
  //     id: 3,
  //     label: "Question 4",
  //     firmAverage: 2,
  //     departmentAverage: 1.5,
  //     practiceGroupAverage: 4.5,
  //     titleAverage: 2,
  //     selfAverage: 1.5,
  //     year: 2019,
  //     categoryAverage: 4.5,
  //   },
  //   {
  //     id: 4,
  //     label: "Question 5",
  //     firmAverage: 2,
  //     departmentAverage: 1.5,
  //     practiceGroupAverage: 4.5,
  //     titleAverage: 2,
  //     selfAverage: 1.5,
  //     year: 2019,
  //     categoryAverage: 4.5,
  //   },
  // ];
  // const columns = [
  //   { id: 1, label: "" },
  //   { id: 4, label: "Department1" },
  //   { id: 5, label: "Department2" },
  //   { id: 6, label: "Department3" },
  //   { id: 7, label: "Department4" },
  // ];

  const getSectionValues = () => {
    let values: any = [];
    sectionData.col.forEach((item: any) => {
      sectionData.row.forEach((_row: any) => {
        values = [...values, _row[item.label]];
      });
    });
    return values;
  };

  const getItemValues = () => {
    let values: any = [];
    itemData.col.forEach((item: any) => {
      itemData.row.forEach((_row: any) => {
        values = [...values, _row[item.label]];
      });
    });
    return values;
  };

  return (
    <Layout.Container fluid className="pt-3">
      {showItems && (
        <Layout.Sidebar
          className="bg-light border-right shadow"
          right
          hasHeader
          style={{ zIndex: 1050 }}
          width={400}
        >
          <ItemList
            onClose={() => toggleItems(false)}
            sections={sectionAndQuestion}
            onSelect={() => ""}
            selected={questions}
            setSelected={setQuestion}
          />
        </Layout.Sidebar>
      )}
      <Div>
        {itemData.row?.length > 0 && <>
          <Text.H3 className="my-3">Item Wise Heatmap</Text.H3>
          <Div className="row my-3">
            <Div className="col-md-6 pr-0" style={{ maxWidth: 130 }}>
              {/* <Table.CompareFilter
                title="Choose questions"
                selected={itemQuestionHeatmap.selected}
                options={itemQuestionHeatmap.options}
                placeholder={itemQuestionHeatmap.placeholder}
                onSelect={(item: string) =>
                  onOptionSelect(setItemQuestionHeatmap, item)
                }
              /> */}
              <Button
                variant="outline-primary"
                className="fs-12"
                onClick={() => toggleItems(true)}
              >
                Show Items
              </Button>
            </Div>
            <Div className="col-md-6 px-0" style={{ maxWidth: 300 }}>
              <Table.CompareFilter
                title="Compare group"
                selected={itemCategoryHeatmap.selected}
                options={itemCategoryHeatmap.options}
                placeholder={itemCategoryHeatmap.placeholder}
                onSelect={(item: string) => {
                  onOptionSelect(setItemCategoryHeatmap, item);
                }}
              />
            </Div>
          </Div>
          <HeatMapTable
            columns={[{ id: 9999, label: "" }, ...itemData.col]}
            values={getItemValues()}
            rows={itemData.row}
            rowItem={(item: any) => {
              const { Data } = HeatMapTable;
              return (
                <>
                  <Data className="text-dark bg-light text-left p-2">
                    <OverlayTrigger
                      placement="bottom"
                      overlay={
                        <Tooltip id="button-tooltip">{item.label}</Tooltip>
                      }
                    >
                      {({ ref, ...triggerHandler }) => (
                        <span
                          className="text-left text-truncate d-block"
                          style={{ maxWidth: "250px" }}
                          ref={ref}
                          {...triggerHandler}
                        >
                          {item.label}
                        </span>
                      )}
                    </OverlayTrigger>
                  </Data>
                  {itemData.col.map((_dt: any, index: number) => (
                    <Data
                      value={item[_dt.label]}
                      values={getItemValues()}
                      key={index}
                    >
                      {item[_dt.label]}
                    </Data>
                  ))}
                </>
              );
            }}
          />
        </>}
        {sectionData.row?.length > 0 && <>
          <Text.H3 className="mb-3 mt-5">Category Wise Heatmap</Text.H3>
          <Div className="row my-3">
            <Div className="col-md-6" style={{ maxWidth: 300 }}>
              <Table.CompareFilter
                title="Compare group"
                selected={categoryHeatmap.selected}
                options={categoryHeatmap.options}
                placeholder={categoryHeatmap.placeholder}
                onSelect={(item: any) => {
                  onOptionSelect(setCategoryHeatmap, item);
                }}
              />
            </Div>
          </Div>
          <HeatMapTable
            columns={[{ id: 9999, label: "" }, ...sectionData.col]}
            values={getSectionValues()}
            rows={sectionData.row}
            rowItem={(item: any) => {
              const { Data } = HeatMapTable;
              return (
                <>
                  <Data className="text-dark bg-light text-left p-2">
                    <span
                      className="text-left text-truncate d-block"
                      style={{ maxWidth: "250px" }}
                    >
                      {item.label}
                    </span>
                  </Data>
                  {sectionData.col.map((_dt: any, index: number) => (
                    <Data
                      value={item[_dt.label]}
                      values={getSectionValues()}
                      key={index}
                    >
                      {item[_dt.label]}
                    </Data>
                  ))}
                </>
              );
            }}
          />
        </>}
      </Div>
    </Layout.Container>
  );
};

Heatmap.propTypes = {};

export default Heatmap;
