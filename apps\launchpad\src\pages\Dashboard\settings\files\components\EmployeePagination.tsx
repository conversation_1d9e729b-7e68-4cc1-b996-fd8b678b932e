import React from "react";
import {
  Pagin<PERSON>,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@repo/ui/components/pagination";

interface EmployeePaginationProps {
  pagination: {
    pageIndex: number;
    pageSize: number;
    pageCount: number;
    totalItems: number;
  };
  handlePaginationChange: (pageIndex: number) => void;
}

export const EmployeePagination: React.FC<EmployeePaginationProps> = ({
  pagination,
  handlePaginationChange,
}) => {
  const { pageIndex, pageCount, totalItems } = pagination;

  // Don't show pagination if there's only one page
  if (pageCount <= 1) {
    return (
      <div className="flex items-center justify-between py-4">
        <div className="text-sm text-gray-500">
          {totalItems} {totalItems === 1 ? "employee" : "employees"}
        </div>
      </div>
    );
  }

  // Generate page numbers to display
  const getPageNumbers = () => {
    const pages = [];
    const maxPagesToShow = 5;
    
    if (pageCount <= maxPagesToShow) {
      // Show all pages if there are fewer than maxPagesToShow
      for (let i = 0; i < pageCount; i++) {
        pages.push(i);
      }
    } else {
      // Always show first page
      pages.push(0);
      
      // Calculate middle pages
      const startPage = Math.max(1, pageIndex - 1);
      const endPage = Math.min(pageCount - 2, pageIndex + 1);
      
      // Add ellipsis after first page if needed
      if (startPage > 1) {
        pages.push(-1); // -1 represents ellipsis
      }
      
      // Add middle pages
      for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
      }
      
      // Add ellipsis before last page if needed
      if (endPage < pageCount - 2) {
        pages.push(-2); // -2 represents ellipsis
      }
      
      // Always show last page
      pages.push(pageCount - 1);
    }
    
    return pages;
  };

  return (
    <div className="flex items-center justify-between py-4">
      <div className="text-sm text-gray-500">
        {totalItems} {totalItems === 1 ? "employee" : "employees"}
      </div>
      <Pagination>
        <PaginationContent>
          <PaginationItem>
            <PaginationPrevious
              onClick={() => handlePaginationChange(Math.max(0, pageIndex - 1))}
              className={pageIndex === 0 ? "pointer-events-none opacity-50" : "cursor-pointer"}
            />
          </PaginationItem>
          
          {getPageNumbers().map((page, i) => (
            <PaginationItem key={i}>
              {page < 0 ? (
                <PaginationEllipsis />
              ) : (
                <PaginationLink
                  isActive={page === pageIndex}
                  onClick={() => handlePaginationChange(page)}
                  className="cursor-pointer"
                >
                  {page + 1}
                </PaginationLink>
              )}
            </PaginationItem>
          ))}
          
          <PaginationItem>
            <PaginationNext
              onClick={() => handlePaginationChange(Math.min(pageCount - 1, pageIndex + 1))}
              className={pageIndex === pageCount - 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
            />
          </PaginationItem>
        </PaginationContent>
      </Pagination>
    </div>
  );
};
