import axios, { AxiosResponse } from "axios";
import util from "unmatched/utils";
import api from "unmatched/utils/api";

const getMetaAvgValues = (avgs: any, labels: any) => {
  let output = {};
  labels.forEach((item: any) => {
    output = {
      ...output,
      [`${item.field}`]: avgs[item.field] || "-",
    };
  });
  return output;
};

export const getOverallGraphDataFact = (userId: any, data: any, type: any) => {
  const config = api.getConfigurations(
    {
      resource_type: type,
    },
    {}
  );
  return axios
    .post(`/analytics/timeseries/graph/${userId}/overall/`, data, config)
    .then((response: AxiosResponse) => {
      const { data, rating_legends, rater_groups } = response.data;
      const [item] = data;
      const labels = util.lib.keys(item?.data || {});
      if (labels.length > 9) {
        throw new Error("You cannot select more that 9 filters");
      } else {
        return {
          graph: {
            data: data.map((o: any, index: number) => {
              return {
                ...util.lib.mapValues(o.data, (val: any) => Number(val) || 0),
                year: o.year,
                id: index + 1,
              };
            }),
            labels,
          },
          groups: rater_groups || ["Overall"],
          legends: util.lib
            .entries(rating_legends)
            .map(([key, value]: any) => ({ id: key, title: value })),
        };
      }
    });
};

export const getCategoryGraphDataFact = (params: any, data: any) => {
  const config = api.getConfigurations(
    {
      resource_type: params.type,
    },
    {}
  );
  return axios
    .post(
      `/analytics/timeseries/graph/${params.userId}/category/${params.categoryId}/`,
      data,
      config
    )
    .then(({ data }: AxiosResponse) => {
      const [item] = data.data;
      return {
        data: data.data.map((o: any, index: number) => {
          return {
            ...util.lib.mapValues(o.data, (val: any) => Number(val) || 0),
            year: o.year,
            id: index + 1,
          };
        }),
        labels: util.lib.keys(item?.data || {}),
      };
    });
};

export const getItemizedGraphDataFact = (
  userId: any,
  questionId: string,
  type: string,
  data: any
) => {
  const config = api.getConfigurations(
    {
      resource_type: type,
    },
    {}
  );
  return axios
    .post(
      `/analytics/timeseries/graph/${userId}/item/${questionId}/`,
      data,
      config
    )
    .then(({ data }: AxiosResponse) => {
      const [item] = data.data;
      return {
        data: data.data.map((o: any, index: number) => {
          return {
            ...util.lib.mapValues(o.data, (val: any) => Number(val) || 0),
            year: o.year,
            id: index + 1,
          };
        }),
        labels: util.lib.keys(item?.data || {}),
      };
    });
};

export const getOverallTimeseriesTableFact = (
  userId: any,
  type: any,
  isDownload: boolean
) => {
  const config = api.getConfigurations(
    { f: isDownload ? "xlsx" : undefined },
    {}
  );
  return axios
    .get(`/analytics/timeseries/table/${userId}/${type}/`, {
      ...config,
    })
    .then(({ data }: AxiosResponse) => {
      return {
        columns: data.metadata_lables.map((item: any) => {
          return {
            key: item.field,
            label: `${item.display_name} Average`,
          };
        }),
        data: data.results.map((item: any, index: number) => {
          const id =
            item.year && item.target
              ? `${item?.target.id}-${item.year}`
              : index;
          return {
            id,
            year: item.year,
            name: item.title,
            revieweeAverage: item.overall_average,
            firmAverage: item.firm_avg,
            ...getMetaAvgValues(item.metadata_avgs, data.metadata_lables),
            // departmentAverage: item.metadata_avgs.department,
            // practiceGroupAverage: item.metadata_avgs.practice_group || "-",
            // titleAverage: item.metadata_avgs.title,
            selfAverage: item.self_avg,
            sentiment:
              typeof item.comment_sentiment === "string"
                ? item.comment_sentiment
                : "-",
          };
        }),
      };
    });
};

export const getCategoryHeatMapFact = (userId: any, filters?: any) => {
  const config = api.getConfigurations({}, {});
  const payload = {
    resource_type: filters?.types[0],
    survey_id: filters?.surveys[0],
    year: filters?.years[0],
  };
  return axios
    .post(`/analytics/timeseries/table/${userId}/category/`, payload, config)
    .then(({ data }: AxiosResponse) => {
      return {
        selectedYear: data.selected_year,
        selectedSurvey: data.selected_survey,
        data: data.data.map((item: any) => {
          let rows = {};
          const keys: any = [];
          util.lib.entries(item.metadata_avgs).forEach(([key, value]: any) => {
            keys.push(key);
            rows = {
              ...rows,
              [key]: value,
            };
          });
          const send = {
            id: item.section_id,
            name: item.name,
            revieweeAverage: item.reviewee_avg || 0,
            firmAverage: item.firm_avg,
            selfAverage: item.self_avg,
            ...rows,
            metaKeys: keys,
          };
          return send;
        }),
      };
    });
};

export const getAllSectionAndItemsFact = (
  surveyId: string,
  type: string,
  userId: string
) => {
  const config = api.getConfigurations(
    {
      survey_id: surveyId,
      resource_type: type,
      user_id: userId,
    },
    {}
  );

  return axios
    .get(`/analytics/items/`, {
      ...config,
    })
    .then(({ data }: AxiosResponse) => {
      return {
        results: data.results.map((item: any) => {
          return {
            id: item.id,
            label: item.label,
            type: item.question_type,
            options: item.options,
          };
        }),
      };
    });
};

export const getItemizedHeatMapFact = (userId: any, filters?: any) => {
  const payload = {
    resource_type: filters?.types[0],
    survey_id: filters?.surveys[0],
    year: filters?.years[0],
    question_ids: filters?.questions || [],
    f: filters?.isDownload ? "xlsx" : undefined,
  };
  const config = api.getConfigurations({}, {});
  return axios
    .post(`/analytics/timeseries/table/${userId}/item/`, payload, {
      ...config,
    })
    .then(({ data }: AxiosResponse) => {
      // const mappedSurveys = []
      //   data.surveys.map((item: any) => ({
      //     id: item.id,
      //     key: item.id,
      //     title: item.title,
      //   })) || [];
      return {
        selectedYear: data.selected_year,
        selectedSurvey: data.selected_survey,
        data: data.data.map((item: any) => {
          let rows = {};
          util.lib.entries(item.metadata_avgs).forEach(([key, value]: any) => {
            rows = {
              ...rows,
              [key]: value,
            };
          });
          const send = {
            id: item.section_id,
            name: item.name,
            label: item.label,
            revieweeAverage: item.reviewee_avg || 0,
            firmAverage: item.firm_avg,
            selfAverage: item.self_avg,
            ...rows,
          };
          return send;
        }),
      };
      // return {
      //   years:
      //     data.years.map((item: any) => ({
      //       id: item,
      //       key: item,
      //       title: item,
      //     })) || [],
      //   selectedYear: data.selected_year,
      //   surveys: mappedSurveys,
      //   selectedSurvey:
      //     mappedSurveys.find((item: any) => item.id === data.selected_survey) ||
      //     {},
      //   data: data.data.map((item: any) => ({
      //     id: item.section_id,
      //     label: item.label,
      //     // year: item.year,
      //     // title: item.index,
      //     revieweeAverage: item.reviewee_avg || 0,
      //     firmAverage: item.firm_avg,
      //     departmentAverage: item.metadata_avgs.department || 0,
      //     // practiceGroupAverage: 4.5,
      //     titleAverage: item.metadata_avgs.title,
      //     selfAverage: item.self_avg,
      //     // sentiment:
      //     //   typeof item.comment_sentiment === "string"
      //     //     ? item.comment_sentiment
      //     //     : "-",
      //   })),
      // };
    });
};

export const getDetailOverallDataFact = (userId: any, groupId?: any) => {
  const config = api.getConfigurations(
    {
      group_id: groupId,
    },
    {}
  );
  return axios
    .get(`/analytics/timeseries/table/${userId}/360/detail`, {
      ...config,
    })
    .then(({ data }: AxiosResponse) => {
      const mappedSurveys =
        data.groups.map((item: any) => ({
          id: item.id,
          key: item.id,
          title: item.name,
        })) || [];
      return {
        groups: mappedSurveys,
        group:
          mappedSurveys.find((item: any) => item.id === data.selected_group) ||
          {},
        columns: data.metadata_lables.map((item: any) => {
          return {
            key: item.field,
            label: `${item.display_name} Average`,
          };
        }),
        data: data.results.map((item: any) => ({
          id: item.id,
          raterGroup: item.rater_group || "-",
          revieweeAverage: item.overall_average || "-",
          firmAverage: item.firm_avg || "-",
          ...getMetaAvgValues(item.metadata_avgs, data.metadata_lables),
          // departmentAverage: item.metadata_avgs.department,
          // practiceGroupAverage: item.metadata_avgs.practice_group || "-",
          // titleAverage: item.metadata_avgs.title,
          selfAverage: item.self_avg,
          sentiment:
            typeof item.comment_sentiment === "string"
              ? item.comment_sentiment
              : "-",
        })),
      };
    });
};
