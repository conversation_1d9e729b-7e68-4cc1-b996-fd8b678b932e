import React from "react";
import {
  PageContainer,
  Layout,
  Text,
  TrendingCard,
  Badge,
  // Table,
  Placeholder,
  Div,
  ComboFilter,
} from "unmatched/components";
import styled from "styled-components";
import CustomHeader from "../../../Shared/CustomHeader/CustomHeader";
import <PERSON><PERSON><PERSON> from "./BarChart/BarChart";
import GRAPH_DATA from "./BarChart/graph-meta";
import AreaChart from "./AreaChart/AreaChart";
import {
  revieweeStatsFact,
  reviewerStatsFact,
  surveyStatsFact,
  surveyGraphStatsFact,
} from "./statistics-api";
import TABS from "../tabs.interface";
import useFilter from "pages/CommonFilters/hook";
import appUrls from "unmatched/utils/urls/app-urls";
import icons from "assets/icons/icons";

const { Paste } = icons;

interface GraphData {
  name: string;
  data: number[];
}
interface GraphFilter {
  cohert: string;
  value: string;
}
interface GraphState {
  filters: GraphFilter;
  data: GraphData[];
}

const StatsRow = styled(Layout.Row)`
  padding-top: 10px;
  padding-bottom: 10px;
  background: #ffffff;
  border: 1px solid #f2f2f2;
  box-sizing: border-box;
  border-radius: 10px;
`;

const CardContainer = styled(Div).attrs({ className: "shadow-sm my-3" })`
  border-radius: 10px;
  overflow: hidden;
  .card-head {
    background: #f2f2f2;
    color: #6d6d6d;
    padding: 15px;
    font-size: 12px;
  }
  .fas {
    font-size: 16px;
    color: #6d6d6d;
  }
`;

const General = (props: any) => {
  const { survey, layout, indexId, globalLoad, tab, metas } = props;

  const [isLoading, setLoading] = React.useState(true);
  const [stats, setStats] = React.useState<any>({});

  const [graphCumulativeData, setGraphCumulativeData] =
    React.useState<GraphState>({
      filters: {
        cohert: "",
        value: "",
      },
      data: [],
    });
  const [graphDailyData, setGraphDailyData] = React.useState<GraphState>({
    filters: {
      cohert: "",
      value: "",
    },
    data: [],
  });

  const [, setMetaFields] = React.useState([
    {
      title: "",
      key: "",
      value: [],
    },
  ]);
  // const [selectedCumulativeMeta, setSelectedCumulativeMeta] =
  //   React.useState<any>({
  //     cohert: "",
  //     applied: undefined,
  //     isSet: false,
  //   });

  // const [selectedIndMeta, setSelectedIndMeta] = React.useState<any>({
  //   cohert: "",
  //   applied: undefined,
  //   isSet: false,
  // });

  React.useEffect(() => {
    if (!globalLoad && tab === TABS.Statistics) {
      getInitialCall();
    }
    //eslint-disable-next-line
  }, [tab, globalLoad]);

  React.useEffect(() => {
    areaFilters.getFilters();
    barFilters.getFilters();
  }, []);

  React.useEffect(() => {
    if (!globalLoad) {
      setMetaFields(
        metas.map((meta: any) => {
          return {
            key: meta.field,
            title: meta.display_name,
            value: meta.value,
          };
        })
      );
    }
    //eslint-disable-next-line
  }, [metas, globalLoad]);

  const getInitialCall = async () => {
    setLoading(true);
    try {
      let revieweeStatsData = {};
      if (
        survey.type === "SurveyIndexUpward" ||
        survey.type === "SurveyIndex360"
      ) {
        const revieweeStats = await revieweeStatsFact({
          index_id: indexId,
        });
        revieweeStatsData = revieweeStats.data;
      }
      const reviewerStats = await reviewerStatsFact({
        index_id: indexId,
      });
      const surveyStats = await surveyStatsFact({
        index_id: indexId,
      });

      const surveyCumulativeGraph = await surveyGraphStatsFact({
        index_id: indexId,
        is_cumulative: "True",
      });

      const surveyDailyGraph = await surveyGraphStatsFact({
        index_id: indexId,
      });

      const surveyStatsData = surveyStats.data;

      const reviewerStatsData = reviewerStats.data;
      const surveyGraphDailyData = surveyDailyGraph.data;
      const surveyGraphCumulativeData = surveyCumulativeGraph.data;

      setGraphDailyData({
        ...graphDailyData,
        data: surveyGraphDailyData.data,
      });
      setGraphCumulativeData({
        ...graphCumulativeData,
        data: surveyGraphCumulativeData.data,
      });

      setStats({
        revieweeStatsData,
        reviewerStatsData,
        surveyStatsData,
      });
      setLoading(false);
    } catch (err) {
      setLoading(false);
    }
  };

  const graphCall = async (
    isCumulative: boolean,
    func?: any,
    state?: any,
    _filters?: any
  ) => {
    let params = {
      index_id: indexId,
      is_cumulative: isCumulative ? "True" : "False",
      // [cohert]: applied,
    };
    if (isCumulative) {
      params = {
        ...params,
        ...areaFilters.getParams(_filters || areaFilters.selected),
      };
    } else {
      params = {
        ...params,
        ...barFilters.getParams(_filters || barFilters.selected),
      };
    }
    const graphAPICall = await surveyGraphStatsFact(params);
    const graphData = graphAPICall.data;
    func({ ...state, data: graphData.data });
  };

  // const [category] = React.useState({
  //   selected: "",
  //   placeholder: "All",
  //   options: [
  //     { title: "All", id: 1 },
  //     { title: "Department", id: 2 },
  //     { title: "Option 3", id: 3 },
  //     { title: "Option 4", id: 4 },
  //   ],
  // });
  // const getCohertSelected = (cohert: string) => {
  //   if (cohert === undefined || cohert === null) {
  //     return "";
  //   }
  //   return (
  //     metaFields.filter((meta: any) => meta.key === cohert)[0]?.title ?? ""
  //   );
  // };
  // const getCohertValues = (cohert: string) => {
  //   if (cohert === undefined || cohert === null) {
  //     return [];
  //   }
  //   return (
  //     metaFields.filter((meta: any) => meta.key === cohert)[0]?.value ?? []
  //   );
  // };
  // const filters: any = {
  //   showUnflagged: false,
  //   cohartIndividual: {
  //     options: metaFields,
  //     selected: getCohertSelected(selectedIndMeta.cohert),
  //   },
  //   appliedIndividual: {
  //     options: getCohertValues(selectedIndMeta.cohert),
  //     selected: selectedIndMeta.applied,
  //   },
  //   cohartCumulative: {
  //     options: metaFields,
  //     selected: getCohertSelected(selectedCumulativeMeta.cohert),
  //   },
  //   appliedCumulative: {
  //     options: getCohertValues(selectedCumulativeMeta.cohert),
  //     selected: selectedCumulativeMeta.applied,
  //   },
  // };

  const areaFilters = useFilter();
  const barFilters = useFilter();

  const reviewStats = [
    {
      id: 1,
      title: "Total Reviewers",
      count: isLoading ? (
        <Placeholder size="" width="col-3" />
      ) : (
        stats?.reviewerStatsData?.total_reviewer || 0
      ),
      down: false,
      icon: "fa-users",
    },
    {
      id: 2,
      title: "Activated",
      count: isLoading ? (
        <Placeholder size="" width="col-3" />
      ) : (
        stats?.reviewerStatsData?.active_user || 0
      ),
      down: false,
      icon: "fa-badge-check",
    },
    {
      id: 3,
      title: "Not Activated",
      count: isLoading ? (
        <Placeholder size="" width="col-3" />
      ) : (
        stats?.reviewerStatsData?.inactive_user || 0
      ),
      down: true,
      icon: "fa-hourglass-start",
    },
    {
      id: 4,
      title: "Participated",
      count: isLoading ? (
        <Placeholder size="" width="col-3" />
      ) : (
        stats?.reviewerStatsData?.participated || 0
      ),
      down: true,
      icon: "fa-clipboard-check",
    },
  ];
  const surveyStats = [
    {
      id: 1,
      title: "Submitted",
      count: isLoading ? (
        <Placeholder size="" width="col-3" />
      ) : (
        stats?.surveyStatsData?.SUBM || 0
      ),
      down: false,
      icon: "fa-clipboard-check",
    },
    {
      id: 2,
      title: "Declined",
      count: isLoading ? (
        <Placeholder size="" width="col-3" />
      ) : (
        stats?.surveyStatsData?.DECL || 0
      ),
      down: false,
      icon: "fa-clipboard-check",
    },
    {
      id: 3,
      title: "Not Answered",
      count: isLoading ? (
        <Placeholder size="" width="col-3" />
      ) : (
        stats?.surveyStatsData?.TODO || 0
      ),
      down: true,
      icon: "fa-clipboard-check",
    },
    {
      id: 4,
      title: "In Progress",
      count: isLoading ? (
        <Placeholder size="" width="col-3" />
      ) : (
        stats?.surveyStatsData?.PROG || 0
      ),
      down: true,
      icon: "fa-clipboard-check",
    },
  ];
  const revieweeStats = [
    {
      id: 1,
      title: "Total Reviewees",
      count: isLoading ? (
        <Placeholder size="" width="col-3" />
      ) : (
        stats?.revieweeStatsData?.total_reviewee || 0
      ),
      down: false,
      icon: "fa-users",
    },
    {
      id: 2,
      title: "Full Reports",
      count: isLoading ? (
        <Placeholder size="" width="col-3" />
      ) : (
        stats?.revieweeStatsData?.receiving_reports || 0
      ),
      down: false,
      icon: "fa-users",
    },
    {
      id: 3,
      title: "Not Receiving Reports",
      count: isLoading ? (
        <Placeholder size="" width="col-3" />
      ) : (
        stats?.revieweeStatsData?.not_receiving || 0
      ),
      down: true,
      icon: "fa-users",
    },
    {
      id: 4,
      title: "Reports by Waiver",
      count: isLoading ? (
        <Placeholder size="" width="col-3" />
      ) : (
        stats?.revieweeStatsData?.report_by_waiver || 0
      ),
      down: true,
      icon: "fa-users",
    },
  ];

  const getHeaderTitleTemplate = () => {
    return (
      <div className="d-block">
        <Text.H1>
          {globalLoad ? <Placeholder width="col-12" /> : survey.name}
        </Text.H1>
        <Text.P1>
          {globalLoad ? (
            <Placeholder width="col-12" />
          ) : (
            <Badge
              className="px-2 py-2 my-3 font-weight-normal"
              style={{ fontSize: "100%" }}
              variant="primary"
            >
              {survey.startDate} - {survey.endDate}
            </Badge>
          )}
        </Text.P1>
      </div>
    );
  };

  return (
    <PageContainer>
      <CustomHeader
        title={getHeaderTitleTemplate()}
        style={{ marginLeft: layout.marginLeft }}
        breadcrumbs={[
          {
            label: "Surveys",
            icon: <Paste className="grey-icon__svg" />,
            route: appUrls.admin.survey.default,
          },
          { label: "Manage Survey" },
          { label: survey.name },
        ]}
      />
      <Div className="mt-4" />
      <Layout.Container fluid>
        <Div className="pt-5 pb-2"></Div>
        <StatsRow className="m-0 shadow-sm">
          {reviewStats.map((item: any, index: number) => {
            const getCountTemplate = () => {
              return item.count;
            };
            return (
              <Layout.Col
                className={`col-lg-3 d-flex align-items-center ${
                  index < reviewStats.length && index !== reviewStats.length - 1
                    ? "border-right"
                    : ""
                }`}
                key={item.id}
              >
                <TrendingCard
                  color={["#B7CFFF", "#D4F2E6", "#FFDC98", "#D4F2E6"][index]}
                  high={!item.down}
                  count={getCountTemplate()}
                  title={item.title}
                  icon={item.icon}
                />
              </Layout.Col>
            );
          })}
        </StatsRow>

        <Div className="row">
          {(survey.type === "SurveyIndexUpward" ||
            survey.type === "SurveyIndex360") && (
            <Div className="col-12 col-md-6">
              <CardContainer>
                <Text.H3 className="card-head">Reviewee Statistics</Text.H3>
                <Div className="m-0 row py-3">
                  {revieweeStats.map((item: any) => (
                    <Div
                      className={
                        // index < revieweeStats.length &&
                        // index !== revieweeStats.length - 1
                        //   ? "border-right"
                        //   : ""
                        "col-md-6 "
                      }
                      key={item.id}
                    >
                      <TrendingCard
                        color={"#fff"}
                        count={item.count}
                        title={item.title}
                        icon={item.icon}
                      />
                    </Div>
                  ))}
                </Div>
              </CardContainer>
            </Div>
          )}
          <Div className="col-12 col-md-6">
            <CardContainer>
              {" "}
              <Text.H3 className="card-head">Survey Statistics</Text.H3>
              <Div className="m-0 row py-3">
                {surveyStats.map((item: any) => {
                  return (
                    <Div
                      className={
                        // index < surveyStats.length &&
                        // index !== surveyStats.length - 1
                        //   ? "border-right"
                        //   : ""
                        "col-md-6"
                      }
                      key={item.id}
                    >
                      <TrendingCard
                        color={"#fff"}
                        count={item.count}
                        title={item.title}
                        icon={item.icon}
                      />
                    </Div>
                  );
                })}
              </Div>
            </CardContainer>
          </Div>
        </Div>

        <Div className="my-3 row">
          <Div className="col-6 col-md-6 col-lg-8">
            <Text.H3 className="py-3">Rating submissions - Cumulative</Text.H3>
          </Div>
          <Div className="col-6 col-md-6 col-lg-4 pt-2">
            {/* <Table.CompareFilter
              title="Filter By"
              selected={category.selected}
              options={category.options}
              placeholder={category.placeholder}
            /> */}
            {/* <ComboFilter
              cohart={filters.cohartCumulative}
              applied={filters.appliedCumulative}
              onCohartUpdate={(e: string) => {
                setSelectedCumulativeMeta({
                  ...selectedCumulativeMeta,
                  cohert: e,
                  applied: undefined,
                });
                if (e === "" || e === undefined) {
                  graphCall(
                    true,
                    setGraphCumulativeData,
                    graphCumulativeData,
                    undefined,
                    undefined
                  );
                }
              }}
              onAppliedUpdate={(e: string) => {
                setSelectedCumulativeMeta({
                  ...selectedCumulativeMeta,
                  applied: e,
                });
                graphCall(
                  true,
                  setGraphCumulativeData,
                  graphCumulativeData,
                  selectedCumulativeMeta.cohert,
                  e
                );
              }}
              isAppliedShown={true}
            /> */}
            <ComboFilter
              filters={areaFilters.filters}
              selected={areaFilters.selected}
              onFilterSelect={(_selected: any) => {
                areaFilters.onSelect(_selected);
                graphCall(
                  true,
                  setGraphCumulativeData,
                  graphCumulativeData,
                  _selected
                );
              }}
              onSubmit={() => {
                // graphCall(true, setGraphCumulativeData, graphCumulativeData);
                // getUsers(1, areaFilters.selected);
              }}
            />
          </Div>
        </Div>

        <StatsRow className="m-0">
          <AreaChart
            data={graphCumulativeData.data}
            labels={GRAPH_DATA.labels}
          />
        </StatsRow>

        <Div className="my-3 row">
          <Div className="col-6 col-md-6 col-lg-8">
            <Text.H3 className="py-3">Rating submissions - Daily</Text.H3>
          </Div>
          <Div className="col-6 col-md-6 col-lg-4 pt-2">
            {/* <Table.CompareFilter
              title="Filter By"
              selected={category.selected}
              options={category.options}
              placeholder={category.placeholder}
            /> */}
            {/* <ComboFilter
              cohart={filters.cohartIndividual}
              applied={filters.appliedIndividual}
              onCohartUpdate={(e: string) => {
                setSelectedIndMeta({
                  ...selectedIndMeta,
                  cohert: e,
                  applied: undefined,
                });
                if (e === "" || e === undefined) {
                  graphCall(
                    false,
                    setGraphDailyData,
                    graphDailyData,
                    undefined,
                    undefined
                  );
                }
              }}
              onAppliedUpdate={(e: string) => {
                setSelectedIndMeta({ ...selectedIndMeta, applied: e });
                graphCall(
                  false,
                  setGraphDailyData,
                  graphDailyData,
                  selectedIndMeta.cohert,
                  e
                );
              }}
              isAppliedShown={true}
            /> */}
            <ComboFilter
              filters={barFilters.filters}
              selected={barFilters.selected}
              onFilterSelect={(_selected: any) => {
                barFilters.onSelect(_selected);
                graphCall(false, setGraphDailyData, graphDailyData, _selected);
              }}
              onSubmit={() => {
                // graphCall(false, setGraphDailyData, graphDailyData);
              }}
            />
          </Div>
        </Div>

        <StatsRow className="m-0">
          <BarChart data={graphDailyData.data} labels={GRAPH_DATA.labels} />
        </StatsRow>
      </Layout.Container>
    </PageContainer>
  );
};

export default General;
