import React from "react";
import {
  PageContainer,
  Text,
  Layout,
  Table,
  Div,
  ComboBasicFilter,
} from "unmatched/components";
import { useTable } from "unmatched/hooks";
import Header from "../Header";
import TABS from "../tabs.interface";
import { participationStatsFact } from "./participation-api";
import appUrls from "unmatched/utils/urls/app-urls";
import icons from "assets/icons/icons";
import { get, keys, map } from "lodash";
import { Button, util } from "@unmatchedoffl/ui-core";
import { downloadPartcipantsFact } from "../ReportCount/report-count-api";

const { Paste } = icons;

interface Dept {
  key: string;
  possibleRaters: string;
  actualRaters: string;
  inProgressRaters: string;
  participationPercent: string;
  group: string;
}

// const COHARTS = [
//   { key: "department", title: "Department" },
//   { key: "location", title: "Location" },
// ];

// const getDepFact = () =>
//   new Promise((resolve: Function) => {
//     window.setTimeout(() => {
//       resolve();
//     }, 400);
//   });

const ParticipationReport = (props: any) => {
  const { survey, layout, indexId, metas, globalLoad, tab } = props;
  const [isLoading, setLoading] = React.useState(true);
  const [metaFields, setMetaFields] = React.useState([
    {
      title: "",
      key: "",
      value: [],
    },
  ]);
  const [selectedMeta, setSelectedMeta] = React.useState<any>({
    cohert: "",
    applied: undefined,
    isSet: false,
  });
  const tableMeta = useTable({});

  React.useEffect(() => {
    if (!globalLoad) {
      setMetaFields(metas);
      setSelectedMeta({
        ...selectedMeta,
        cohert: metas[0]?.key || "",
        isSet: true,
      });
    }
    //eslint-disable-next-line
  }, [metas, globalLoad]);

  React.useEffect(() => {
    if (
      !globalLoad &&
      selectedMeta.isSet &&
      tab === TABS.ParticipationReports
    ) {
      getParticipantReport();
    }
    //eslint-disable-next-line
  }, [metas, selectedMeta, globalLoad, tab]);

  const [Departments, setDepartments] = React.useState<Array<Dept>>([]);

  const getCohertSelected = (cohert: string) => {
    if (cohert === undefined || cohert === null) {
      return "";
    }
    return (
      metaFields.filter((meta: any) => meta.key === cohert)[0]?.title ?? ""
    );
  };
  const getCohertValues = (cohert: string) => {
    if (cohert === undefined || cohert === null) {
      return [];
    }
    return (
      metaFields.filter((meta: any) => meta.key === cohert)[0]?.value ?? []
    );
  };

  const filters = {
    showUnflagged: false,
    cohart: {
      options: metaFields,
      selected: getCohertSelected(selectedMeta.cohert),
    },
    applied: {
      options: getCohertValues(selectedMeta.cohert),
      selected: selectedMeta.applied,
    },
  };
  const getParticipantReport = async (page?: number, params?: any) => {
    try {
      setLoading(true);
      tableMeta.setPagination({
        totalPages: 0,
        page: 1,
        size: 10,
      });
      const participationStats = await participationStatsFact({
        index_id: indexId,
        parent_cat: selectedMeta.cohert,
        page_size: 10,
        page: page || 1,
        ...(ordering && { ordering: params.ordering }),
      });
      setDepartments(participationStats.data);
      tableMeta.setPagination({
        page: page || 1,
        totalPages: participationStats.totalPages,
        size: 10,
      });
      setLoading(false);
    } catch (err) {
      setLoading(false);
    }
  };

  const checkSelected = (item: Dept) => {
    return tableMeta.isSelected(item.key);
  };

  // const onSearch = (search: string) => {
  //   tableMeta.setSearch(search);
  // };

  // const onSelectDepartment = (item: Dept) => {
  //   tableMeta.onSelect(item, "empId");
  // };

  // const onCheckAll = (checked: boolean) => {
  //   tableMeta.onSelectAll(Departments, checked, "empId");
  // };

  // const onDownload = (Departments: Array<Dept>) => {};

  // const onEmail = () => '';

  // Header Templates
  const getHeaderMetaTemplate = () => {
    return (
      <>
        {/* <FormControl.Search
          value={tableMeta.search}
          style={{
            width: "400px",
          }}
          onChange={(e: any) => {
            tableMeta.setSearch(e.target.value);
          }}
          onSearch={(value: string) => {
            onSearch(value);
          }}
          placeholder="Search for employee , ID, department.."
        /> */}
      </>
    );
  };

  // TableColumn templates

  // const getCheckAllTemplate = () => (
  //   <FormGroup className="pb-1">
  //     <FormControl.Checkbox>
  //       <FormControl.Checkbox.Input
  //         checked={tableMeta.selectAll}
  //         onChange={(evt: any) => onCheckAll(evt.target.checked)}
  //       />
  //     </FormControl.Checkbox>
  //   </FormGroup>
  // );

  const getColumns = () => {
    return [
      // {
      //   key: 1,
      //   renderItem: getCheckAllTemplate,
      //   hasSort: false,
      // },
      { key: 2, label: "No.", hasSort: false },
      { key: 3, label: getCohertSelected(selectedMeta.cohert), hasSort: false },
      { key: 4, label: "Possible Raters", hasSort: true, sortValue: "asc",sortKey: "possible_raters", },
      { key: 6, label: "Actual Raters", hasSort: true, sortValue: "asc",sortKey: "actual_raters", },
      { key: 8, label: "In Progress Raters", hasSort: true, sortValue: "asc",sortKey: "in_progress_raters", },
      { key: 9, label: "Participation %", hasSort: true, sortValue: "asc",sortKey: "participation_percent", },
    ];
  };

  const [columnsData, setColumnsData] = React.useState<any>(getColumns()); // deepscan-disable-line REFERENCE_BEFORE_LEXICAL_DECL
  const [ordering, setOrdering] = React.useState("");

  const getCompColumns = () => {
    let columnsList = keys(columnsData);
    columnsList = map(columnsList, (key: string) => ({
      ...get(columnsData, key),
      key,
    }));
    return columnsList;
  };

  // Table Row Templates

  const getRowsTemplate = () => {
    return Departments.map((item: Dept, index: number) => {
      const checked = checkSelected(item);
      const isEven = index % 2 === 0 || index === 0;
      return (
        <Table.Row even={!isEven} key={index} selected={checked}>
          {/* <Table.Data width="30px">
            <FormGroup>
              <FormControl.Checkbox>
                <FormControl.Checkbox.Input
                  onChange={() => onSelectDepartment(item)}
                  onClick={(evt: any) => evt.stopPropagation()}
                  checked={checked}
                />
              </FormControl.Checkbox>
            </FormGroup>
          </Table.Data> */}
          <Table.Data width="70px">
            <Text.P1>{tableMeta.page * 10 - 10 + index + 1}.</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.group}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.possibleRaters}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.actualRaters}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.inProgressRaters}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.participationPercent}%</Text.P1>
          </Table.Data>
        </Table.Row>
      );
    });
  };
  // if (!Departments.length && !isLoading) {
  //   return <ReportsNoDataFound />;
  // }

  const onDownloadTargetResponses = async () => {
    await downloadPartcipantsFact({
      index_id: indexId,
    });
  };

  return (
    <PageContainer>
      <Header
        survey={survey}
        layout={layout}
        isLoading={globalLoad}
        metaTemplate={getHeaderMetaTemplate()}
        breadcrumbs={[
          {
            label: "Surveys",
            icon: <Paste className="grey-icon__svg" />,
            route: appUrls.admin.survey.default,
          },
          { label: "Manage Survey" },
          { label: survey.name },
        ]}
      />
      {/* tableMeta.selected.length && */}
      <div className="mt-5" />
      <Layout.Container className="pt-5" fluid>
        <Layout.Row>
          <Layout.Col md={4}>
            <ComboBasicFilter
              cohart={filters.cohart}
              applied={filters.applied}
              onCohartUpdate={(e: string) =>
                setSelectedMeta({
                  ...selectedMeta,
                  cohert: e,
                  applied: undefined,
                })
              }
              onAppliedUpdate={(e: string) =>
                setSelectedMeta({ ...selectedMeta, applied: e })
              }
              isAppliedShown={false}
            />
          </Layout.Col>
        </Layout.Row>
        <Div className="pt-4">
          <Table
            columns={getCompColumns()}
            isLoading={isLoading}
            rows={Departments}
            customRows
            render={() => getRowsTemplate()}
            hasPagination
            activePage={tableMeta.page}
            pages={tableMeta.totalPages}
            onPageSelect={(d: any) => {
              tableMeta.onPageSelect(d);
              getParticipantReport(d);
            }}
            onSort={(item: any) => {
              const label = util.label.getSortingLabel(
                item.sortKey,
                item.sortValue
              );
              setColumnsData((_columns: any) => {
                return Object.values(tableMeta.resetColumns(_columns, item));
              });
              setOrdering(label);
              getParticipantReport(tableMeta.page, { ordering: label });
            }}
          />
        </Div>
      </Layout.Container>
      <hr />
        <Text.P1 className="text-muted">
        Download an excel file with participation stats up to date.
        </Text.P1>
        <Button className="mt-2" onClick={onDownloadTargetResponses}>
          Download
        </Button>
    </PageContainer>
  );
};

export default ParticipationReport;
