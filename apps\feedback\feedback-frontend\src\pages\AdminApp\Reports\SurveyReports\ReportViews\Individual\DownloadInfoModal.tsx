import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Text,
  Div
} from "unmatched/components";
import ModalHeader from "../../../../ModalHeader";

export default function DownloadInfoModal(props: any) {
  const { show, onHide } = props;

  return (
    <Modal show={show} backdrop="static" centered size="lg">
      <ModalHeader
        title="Download Reports"
        onHide={() => {
          onHide();
        }}
      />
      <Modal.Body>
        <Div className="p-4">
          <Text.H3>Your download request is being processed.</Text.H3>
          <Text.P1>
            Reports are being processed and will be ready to download in
            ‘download requests’ tab. Please check back in sometime.
          </Text.P1>
        </Div>
      </Modal.Body>

      <Modal.Footer className="text-right my-4 border-none">
        <Button
          variant="primary"
          className="mr-2"
            onClick={() => {
                props.onHide();
                props.onTabSelect('DOWNLOADS', true);
              }
            }
        >
          {/* <Icon icon={icons.DOWNLOAD} className="mr-1" /> */}
          Go to download requests
        </Button>
      </Modal.Footer>
    </Modal>
  );
}
