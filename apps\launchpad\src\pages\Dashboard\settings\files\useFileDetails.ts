import { useEffect, useState } from "react";
import {
  getFileDetailsService,
  getFileEmployeesService,
  ApiFile,
  Employee,
  File
} from "./filesService";
import { getNextSortDirection } from "./utils/sortingUtils";

export type SortDirection = "asc" | "desc";

export const useFileDetails = (fileId: string | undefined) => {
  // Handle undefined fileId
  if (!fileId) {
    return {
      loadingFile: false,
      loadingEmployees: false,
      fileDetails: null,
      employees: [],
      pagination: {
        pageIndex: 0,
        pageSize: 10,
        pageCount: 0,
        totalItems: 0,
      },
      sorting: {
        field: "",
        direction: "asc" as SortDirection,
      },
      searchQuery: "",
      handleSearch: () => {},
      handleSort: () => {},
      handlePaginationChange: () => {},
    };
  }
  const [loadingFile, setLoadingFile] = useState(false);
  const [loadingEmployees, setLoadingEmployees] = useState(false);
  const [fileDetails, setFileDetails] = useState<ApiFile | null>(null);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
    pageCount: 0,
    totalItems: 0,
  });
  const [sorting, setSorting] = useState<{ field: string; direction: SortDirection }>({
    field: "",
    direction: "asc",
  });
  const [searchQuery, setSearchQuery] = useState("");

  // Fetch file details
  const getFileDetails = async () => {
    if (!fileId) return;

    try {
      setLoadingFile(true);
      const response = await getFileDetailsService(fileId);
      setFileDetails(response);
      setLoadingFile(false);
      return response;
    } catch (err) {
      console.error("Error fetching file details:", err);
      setLoadingFile(false);
    }
  };

  // Fetch employees in the file
  const getEmployees = async (params?: Record<string, unknown>) => {
    if (!fileId) return;

    try {
      // Clear employees state before fetching new data
      setEmployees([]);
      setLoadingEmployees(true);

      const response = await getFileEmployeesService(fileId, params || {});

      setLoadingEmployees(false);
      setEmployees(response.results);
      setPagination(prev => ({
        ...prev,
        pageCount: response.totalPages,
        totalItems: response.totalElements
      }));

      return response;
    } catch (err) {
      console.error("Error fetching employees:", err);
      setLoadingEmployees(false);
    }
  };

  // Handle search
  const handleSearch = (query: string) => {
    // Clear employees state before changing search
    setEmployees([]);

    setSearchQuery(query);
    setPagination(prev => ({
      ...prev,
      pageIndex: 0 // Reset to first page on new search
    }));
  };

  // Handle sorting
  const handleSort = (field: keyof Employee | string) => {
    // Clear employees state before changing sorting
    setEmployees([]);

    setSorting(prev => {
      const direction = prev.field === field
        ? getNextSortDirection(prev.direction)
        : "asc";

      return {
        field,
        direction,
      };
    });
  };

  // Handle pagination change
  const handlePaginationChange = (newPageIndex: number) => {
    // Clear employees state before changing pagination
    setEmployees([]);

    setPagination(prev => ({
      ...prev,
      pageIndex: newPageIndex,
    }));
  };

  // Effect to fetch file details on mount
  useEffect(() => {
    if (fileId) {
      getFileDetails();
    }
  }, [fileId]);

  // Effect to fetch employees when pagination, sorting, or search changes
  useEffect(() => {
    if (fileId) {
      const params: Record<string, unknown> = {
        page: pagination.pageIndex + 1,
        page_size: pagination.pageSize,
      };

      if (searchQuery) {
        params.search = searchQuery;
      }

      if (sorting.field && sorting.direction) {
        const prefix = sorting.direction === "desc" ? "-" : "";
        params.ordering = `${prefix}${sorting.field}`;
      }

      getEmployees(params);
    }
  }, [pagination.pageIndex, pagination.pageSize, sorting, searchQuery, fileId]);

  return {
    loadingFile,
    loadingEmployees,
    fileDetails,
    employees,
    pagination,
    sorting,
    searchQuery,
    handleSearch,
    handleSort,
    handlePaginationChange,
  };
};
