@import "./assets/fontawesome/css/all.css";
@import "~@unmatchedoffl/ui-core/dist/esm/styles/scoped-utils";
@import "~@unmatchedoffl/ui-core/dist/esm/styles/font-families";
@import "~@unmatchedoffl/ui-core/dist/esm/styles/utilities.scss";
@import "~bootstrap/scss/bootstrap.scss";
@import "~@unmatchedoffl/ui-core/dist/esm/styles/index.scss";
@import "./unmatched/styles/sidebar-menu.scss";

html {
  scroll-behavior: smooth;
}

body {
  background-color: #fbfbfb;
  font-family: "Inter";
}

.mt-60 {
  margin-top: 45px;
}

.fixed-top {
  z-index: 1052;
}

.dropdown-menu {
  max-height: 300px;
  overflow-y: auto;
}

hr {
  border-top: 1px solid #f2f2f2;
}

.dropdown-toggle::after {
  display: none;
  background: #fcfcfc;
  /* text-white-medium */

  border: 1px solid #f2f2f2;
  box-sizing: border-box;
  border-radius: 2px;

  // border-top: 0px solid !important;
  // border-right: 0px solid #0000 !important;
  // border-bottom: 0px !important;
  // border-left: 0px solid #0000 !important;
}
.text-black {
  color: #000 !important;
}
.modal-70w {
  width: 80%;
  max-width: none !important;
}
.recharts-tooltip-label,
.recharts-tooltip-item,
tspan {
  font-size: 12px;
}
.recharts-default-tooltip {
  background-color: #fcfcfc !important;
  border-radius: 5px;
  padding: 10px;
  box-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
}
.bg-user-dark {
  background: #14171d;
}
.text-secondary-title {
  color: #6d6d6d;
}
a {
  &:hover {
    text-decoration: none !important;
  }
}
.border-botom-3 {
  border-bottom-width: 3px !important;
}

.f12 {
  font-size: 0.75rem !important;
}
.f14 {
  font-size: 0.875rem !important;
}

.modal {
  backdrop-filter: blur(3px);
}
.modal-content {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -2px rgba(0, 0, 0, 0.1);
}
// /* width */
// ::-webkit-scrollbar {
//   width: 0.5rem;
// }

// /* Track */
// ::-webkit-scrollbar-track {
//   background: #f1f1f1;
// }

// /* Handle */
// ::-webkit-scrollbar-thumb {
//   background: #ccc;
// }

// /* Handle on hover */
// ::-webkit-scrollbar-thumb:hover {
//   background: #ccc;
// }
.modal-backdrop {
  z-index: 1050 !important;
}
.pointer {
  cursor: pointer;
}

.select-none,
td,
tr,
th {
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.quill-pre p {
  white-space: pre;
}

.survey-quill .ql-editor {
  border-radius: 4px;
  background-color: #fcfcfc;
  font-size: 14px;
  font-weight: 500;
  padding: 6px 10px;
  color: #2f2f2f;
  min-height: 300px;
}

.custom-reminder .ql-editor {
  min-height: 180px;
}

.custom-reminder .ql-container.ql-snow {
  border-bottom: 1px solid #ccc !important;
}

.survey-quill .ql-container.ql-snow {
  border-radius: 4px;
  border-bottom: 0px;
}

.survey-quill .ql-toolbar.ql-snow {
  display: none;
}

.survey-quill-subject .ql-editor {
  border-radius: 4px;
  background-color: #fcfcfc;
  font-size: 14px;
  font-weight: 500;
  padding: 6px 10px;
  color: #2f2f2f;
  min-height: 38px;
}

.survey-quill-subject .ql-container.ql-snow {
  border-radius: 4px;
}

.survey-quill-subject .ql-toolbar.ql-snow {
  display: none;
}

.email-body-foot {
  border-bottom: 1px solid #ccc;
  border-right: 1px solid #ccc;
  border-left: 1px solid #ccc;
}

//  new dark sidebar css

.dark-sidebar {
  background: #344361 !important;
}

.text-white {
  color: #fff !important;
}

.dark-nav-link {
  width: 220px !important;
  margin-left: 15px !important;
}

.font-weight-bold6 {
  font-weight: 600 !important;
}

.survey-sidebar hr {
  background: #c4c4c4;
  opacity: 0.1;
  width: 180px;
}

.text-muted.nav-link.active {
  background-color: #344361 !important;
  box-shadow: none;
  font-weight: 600 !important;
  background-color: rgb(255 255 255 / 10%) !important;
  box-shadow: 0px 0px 4px rgb(0 0 0 / 15%);
  border-radius: 2px;
  color: #fff !important;
}

.grey-icon__svg path {
  stroke: #6d6d6d;
}

i.far.fa-ellipsis-h {
  color: #fff !important;
}

.blue__svg path {
  stroke: #518cff !important;
}

.closepass {
  position: absolute;
  right: 5px;
  top: 5px;
}

// common beautifications css starts

.um-border {
  border: 1px solid #cbcbcb !important;
  box-shadow: 0px 1px 4px rgb(0 0 0 / 10%);
}

.surveys-list .dropdown-toggle {
  border: 1px solid #cbcbcb !important;
  box-shadow: 0px 1px 4px rgb(0 0 0 / 10%);
}
.search-container input {
  border: 1px solid #cbcbcb !important;
  box-shadow: 0px 1px 4px rgb(0 0 0 / 10%);
}

.dropdown-menu {
  .show {
    background: #ffffff;
    border: 1px solid #f0f0f1;
    box-shadow: 0px 2px 10px rgba(0, 0, 0, 0.15);
    border-radius: 3px;
  }
  .cursor-pointer div {
    padding-left: 5px;
    padding-right: 5px;
  }
}

 .dropdown-toggle.btn {
  border: 1px solid #cbcbcb !important;
  box-shadow: 0px 1px 4px rgb(0 0 0 / 10%) !important;
}
.dropdown-menu{
  border: none !important;
  box-shadow: 0px 1px 4px rgb(0 0 0 / 10%) !important;
}

.dashboard-sidebar .dropdown-toggle.btn {
  border: none !important;
}

// common beautifications css starts

.version-sb-menu {
  background-color: #ffffff1a !important;
}

// sticky tabs common scss starts
.search-container input {
  width: 305px;
}

.search-wrap {
  .search-container {
    float: right !important;
  }
}

.form-control:focus {
  border: 1px solid #007bff !important;
}

.sticky-tabs-container {
  position: absolute;
  bottom: 0px;
  a {
    color: #000 !IMPORTANT;
    font-size: 14px;
  }
  .text-primary {
    color: #000 !IMPORTANT;
    font-size: 14px;
  }
  .nav-link.active {
    border-bottom: 2px solid #518CFF;
    color: #2F2F2F;
    font-weight: 600;
    border-left: none;
    border-right: none;
    border-top: none;
  }

  .nav-link:hover {
    // border-bottom: 2px solid #518CFF;
    // color: #2F2F2F;
    // font-weight: 600;
    border-left: none;
    border-right: none;
    border-top: none;
  }
}

.sticky-tabs-container .nav-tabs.sticky {
  border-bottom: none;
}

.custom-tabs-2 .nav-tabs {
  border-bottom: none;
  z-index: 1050 !important;
}

.no-shadow.btn {
  box-shadow: none !important;
  border: none !important;
}

.badge {
  position: absolute;
    margin-top: -12px;
    z-index: 1;
    right: 4px;
    background-color: #ffe000;
    padding: 5px 10px;
    display: flex;
    align-items: center;
}

.clock__svg path {
  stroke: #000;
}

/* simple tabs css start */
.simple-tabs-text {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-size: 15px;
  line-height: 30px;
  color: #1B2945;
}

.simple-tabs-text.active {
  border-bottom: 2px solid #518CFF;
  font-weight: 600 !important;
}

.new-tab-ui .nav-tabs .nav-link.active {
  border-bottom: 2px solid #518cff;
  padding: 0.5rem 0.8rem;
  opacity: 1;
}

.new-tab-ui .nav-tabs .nav-link {
  opacity: 0.5;
  font-weight: 400 !important;
}

.new-tab-ui .nav-tabs .nav-link.active .text-primary {
  color: #2f2f2f !important;
}
/* simple tabs css end */

/* new survey builder css starts */

.section-title {
  color: #1C69FF;
  font-family: Inter;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
}

.required-input {
  color: #CE1B1B;
  font-family: Inter;
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
  line-height: normal; 
}

.form-text {
  color: #606060 !important;
  font-family: Inter;
  font-size: 12px !important;
  font-style: normal;
  font-weight: 500 !important;
  line-height: normal;
}

.form-control {
  border-radius: 4px;
  background: #FFF;
  box-shadow: 0px 1px 5px 0px rgba(0, 0, 0, 0.10);
}

.form-control:focus {
  background: #fff;
}

.click-to-open {
  color: #518cff;
  text-decoration: underline;
  font-size: 14px;
  margin-bottom: 20px;
  cursor: pointer;
}

/* new survey builder css ends */

.input-label {
  font-size: 0.815rem;
  font-family: Inter;
  font-weight: 400;
  font-style: normal;
  line-height: 1.213rem;
}

.diff-viewer-wrap {
  border-radius: 4px;
  background: #FFF;
  box-shadow: 0px 1px 5px 0px rgba(0, 0, 0, 0.1);
}

.diff-view-main-wrap {
  width: 101%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-left: 18px;
}

.versions-list {
  // overflow-x: scroll;
  flex-wrap: inherit !IMPORTANT;
  width: 74vw;
}

// Section option css start
.section-options-dropdown {
  width: 200px;
  height: auto;
  position: absolute;
  margin-left: 40px;
  background-color: #fff;
  z-index: 9999;
  border-radius: 4px;
  border: 1px solid #DCDCDC;
  background: #FFF;
  box-shadow: 0px 1px 20px 0px rgba(0, 0, 0, 0.05);
  padding: 16px;

  .opt {
    color: #424242;
    font-family: Inter;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    cursor: pointer;
  }
}
// Section option css end

.version-options.dropdown-toggle.btn {
  border: none !important;
  box-shadow: none !important;
  transform: rotate(90deg) !important;
}

.vo-dd .dropdown-item.active {
  background-color: transparent !important;
}

.vo-dd .dropdown-item {
  text-align: left;
}

a.danger-text.dropdown-item {
  color: rgb(233, 7, 75) !important;
}

// APPS MODULE CSS START

.fade.modal-backdrop.custom-backdrop.show {
  opacity: 0;
}

.modal-dialog.apps-modal {
  margin: 0px;
  width: 310px;

  .modal-content {
    height: 545px;
    background: #1B2945;
    box-shadow: 0px 4px 40px 0px rgba(0, 0, 0, 0.25);
  }

  .modal-body {
    padding: 0px;
  }

  .modal-footer {
    border-top: none;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.apps-modal-wrap.modal.show {
  padding-left: 0px !important;
  z-index: 1111;
  backdrop-filter: unset;
}

.apps-text {
  color: #FFF;
  font-family: Inter;
  font-size: 10px;
  font-style: normal;
  font-weight: 600;
  line-height: 18px;
  opacity: 0.5;
  margin-bottom: 12px;
}

.app-desc {
  color: #FFF;
  font-family: Inter;
  font-size: 11px;
  font-style: normal;
  font-weight: 300;
  line-height: 18px;
  opacity: 0.5;
  margin-top: 2px;
}

.app-name {
  color: #FFF;
  font-family: Inter;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 13px; /* 128.571% */
}

.feat-link {
  color: #FFF;
  font-family: Inter;
  font-size: 13px;
  font-style: normal;
  font-weight: 400;
  line-height: 18px; /* 138.462% */
  display: flex;
  align-items: center;
  height: 40px;
  cursor: pointer;
  padding-left: 35px;
}

.feat-link:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
  box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.15);
  border-radius: 2px;
  color: #fff;
}

.color-block {
  width: 24px;
  height: 24px;
  flex-shrink: 0;
  border-radius: 2px;
  margin-right: 10px;
}

.app-btn {
  width: 266px;
height: 34px;
flex-shrink: 0;
border-radius: 4px;
background: #414c63;
color: #FFF;

font-family: Inter;
font-size: 14px;
font-style: normal;
font-weight: 400;
line-height: 18px;
display: flex;
    align-items: center;
justify-content: center;
cursor: pointer;
}

.app-btn:hover {
  color: #fff
}