<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <!-- <link rel="icon" href="%PUBLIC_URL%/favicon.ico" /> -->
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no"> -->

    <link rel="icon" href="/favicon.ico" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
    />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="The leading people development platform for professional services companies."
    />
    <!-- <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" /> -->
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <!-- Polyfill Intl.RelativeTimeFormat, its dependencies & `en` locale data -->
    <script src="https://polyfill.io/v3/polyfill.min.js?features=Intl.RelativeTimeFormat,Intl.RelativeTimeFormat.~locale.en"></script>
    <title>Feedback App - Unmatched</title>
    <script>
      (function (h, o, t, j, a, r) {
        h.hj =
          h.hj ||
          function () {
            (h.hj.q = h.hj.q || []).push(arguments);
          };
        h._hjSettings = { hjid: 3580257, hjsv: 6 };
        a = o.getElementsByTagName("head")[0];
        r = o.createElement("script");
        r.async = 1;
        r.src = t + h._hjSettings.hjid + j + h._hjSettings.hjsv;
        a.appendChild(r);
      })(window, document, "https://static.hotjar.com/c/hotjar-", ".js?sv=");
    </script>
    <script type="text/javascript">
      (function (c, l, a, r, i, t, y) {
        c[a] =
          c[a] ||
          function () {
            (c[a].q = c[a].q || []).push(arguments);
          };
        t = l.createElement(r);
        t.async = 1;
        t.src = "https://www.clarity.ms/tag/" + i;
        y = l.getElementsByTagName(r)[0];
        y.parentNode.insertBefore(t, y);
      })(window, document, "clarity", "script", "i5v8oytndn");
    </script>
    <script type="text/javascript">
      !(function (e, t, n) {
        function a() {
          var e = t.getElementsByTagName("script")[0],
            n = t.createElement("script");
          (n.type = "text/javascript"),
            (n.async = !0),
            (n.src = "https://beacon-v2.helpscout.net"),
            e.parentNode.insertBefore(n, e);
        }
        if (
          ((e.Beacon = n =
            function (t, n, a) {
              e.Beacon.readyQueue.push({ method: t, options: n, data: a });
            }),
          (n.readyQueue = []),
          "complete" === t.readyState)
        )
          return a();
        e.attachEvent
          ? e.attachEvent("onload", a)
          : e.addEventListener("load", a, !1);
      })(window, document, window.Beacon || function () {});
    </script>
    <script type="text/javascript">
      window.Beacon("init", "888231bb-a0e9-4c35-898b-40ed3259bb26");
      window.Beacon("config", {
        docsEnabled: false,
      })
    </script>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
  </body>
</html>
