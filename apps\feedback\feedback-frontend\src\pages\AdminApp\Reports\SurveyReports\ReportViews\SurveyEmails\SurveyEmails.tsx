import React from "react";
import { useParams } from "react-router-dom";
import useCustomLayout from "pages/AdminApp/Shared/custom-layout-hook";
// import SurveyTimeline from "./SurveyTimeline/SurveyTimeline";
import Emails from "./Emails";
// import { SURVEY, SURVEY_STATES } from "./manage-survey-meta";
import util from "unmatched/utils";
import {
  getSurveyByIdFact,
  getTemplatesFact,
  patchEmailTemplateFact,
  sendCustomEmailReminderFact,
  sendEmailReminderFact,
} from "pages/AdminApp/Survey/survey-api";
import useFilter from "pages/CommonFilters/hook";
import _, { debounce } from "lodash";
import useToastr from "unmatched/modules/toastr/hook";
import { SURVEY } from "pages/AdminApp/Email/meta";
import { SURVEY_STATES } from "pages/AdminApp/Survey/ManageSurvey/manage-survey-meta";


export default function SurveyEmails(props: any) {
  const layout = useCustomLayout();
  const params = useParams<any>();
  const [survey, setSurveyInfo] = React.useState<any>(SURVEY);
  const [,setRawSurvey] = React.useState<any>(null);
  const [isLoading, setIsLoading] = React.useState(true);
  const [emailData, setEmailData] = React.useState(null);
  const [emailTemplates, setEmailTemplates] = React.useState([]);
  const [,setMetas] = React.useState<any>([]);
  const toastr = useToastr();
  const filtersState = useFilter();

  const getFilters = () => {
    filtersState.getFilters((_filters: any) => {
      const arr: any = [];
      _.forEach(_filters, (values: any, key: any) => {
        arr.push({
          title: values.label,
          key: key,
          value: values.values.map((value: any) => {
            return { key: value, title: value };
          }),
        });
      });
      arr.push({
        title: "All",
        value: ["All"],
      });
      setMetas(arr);
    });
  };

  const getTemplates = (initial = true) => {
    getTemplatesFact(props.surveyId, false, 'reporting').then((response: any) => {
        console.log(response.results)
      setEmailTemplates(response.results);
      initial &&
        setEmailData(
          response.results.find((r: any) => r.label === "ENCRYPTED_REPORT")
        );
    });
  };

  const updateEmailDataDebounced = debounce(
    (data: any, setAgain?: boolean, setIsSaving?: any) => {
      // let body = data.body.replaceAll(`<span style="color: black;"> </span>`, " ");
      setIsSaving?.(true);
      const body = data.body.replaceAll(`rgb(81, 140, 255)`, "black");
      patchEmailTemplateFact(
        {
          ...data,
          index: params.id,
          body,
        },
        data.id
      ).then((response: any) => {
        setAgain && setEmailData(response);
        setIsSaving?.(false);
        getTemplates(false);
      });
    },
    1000
  );

  const updateEmailData = (
    data: any,
    setAgain?: boolean,
    setIsSaving?: any,
    showToast?: boolean
  ) => {
    // let body = data.body.replaceAll(`<span style="color: black;"> </span>`, " ");
    setIsSaving?.(true);
    const body = data.body.replaceAll(`rgb(81, 140, 255)`, "black");
    return patchEmailTemplateFact(
      {
        ...data,
        index: params.id,
        body,
      },
      data.id
    ).then((response: any) => {
      setAgain && setEmailData(response);
      setIsSaving?.(false);
      getTemplates(false);
      showToast &&
        toastr.onSucces({
          title: "Success",
          content: "Template saved Successfully",
        });
      return response;
    });
  };

  const sendEmailReminder = (data: any, showToast: boolean) => {
    return sendEmailReminderFact(data).then((res: any) => {
      {
        showToast &&
          toastr.onSucces({
            title: "Success",
            content: "Email sent Successfully",
          });
      }
      return res;
    });
  };

  const sendCustomEmailReminder = (data: any, showToast: boolean) => {
    return sendCustomEmailReminderFact(data).then((res: any) => {
      {
        showToast &&
          toastr.onSucces({
            title: "Success",
            content: "Email sent Successfully",
          });
      }
      return res;
    });
  };

  async function fetchSurveyInfo() {
    try {
      const response: any = await getSurveyByIdFact(props.surveyId);
      const { getFormatedTime } = util.date;
      // const metaResponse = await getMetaLabelsFact([]);
      setRawSurvey(response);
      setSurveyInfo((_d: any) => {
        // console.log(response, "CURRENT SURVEY");
        return {
          ..._d,
          name: response.name,
          startDate: getFormatedTime(response.startDate, "MMM yyyy"),
          endDate: getFormatedTime(response.endDate, "MMM yyyy"),
          isoStartDate: response.startDate,
          isoEndDate: response.endDate,
          type: response.type,
        };
      });
      setIsLoading(false);
    } catch (e) {
      setIsLoading(false);
    }
  }

  React.useEffect(() => {
    getFilters();
    fetchSurveyInfo();
    getTemplates();
    //eslint-disable-next-line
  }, []);

  const meta = util.lib.get(SURVEY_STATES, survey.status);

  return (
    <>
      <div>
        {emailData !== null && (
          <Emails
            survey={survey}
            layout={layout}
            meta={meta}
            globalLoad={isLoading}
            emailData={emailData}
            updateEmailData={updateEmailData}
            updateEmailDataDebounced={updateEmailDataDebounced}
            sendEmailReminder={sendEmailReminder}
            sendCustomEmailReminder={sendCustomEmailReminder}
            indexId={params.id}
            setEmailData={setEmailData}
            templates={emailTemplates}
          />
        )}
      </div>
    </>
  );
}
