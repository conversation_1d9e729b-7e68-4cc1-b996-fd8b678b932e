import util from "unmatched/utils";
import { Session, User } from "unmatched/types";
import {
  useDispatch,
  useHistory,
  useSelector,
  useState,
} from "unmatched/hooks";
import { getSessionFact, getClientInfoFact, patchClientInfoFact } from "./api";
import actions from "./slice";

export default function useSession() {
  const [sessionLoaded, setSessionLoaded] = useState(false);
  const session: Session = useSelector((state: any) => state.session);
  const dispatch = useDispatch();
  const history = useHistory();

  const fetchSession = async (params?: any) => {
    dispatch(actions.setLoading(true));
    if (isLoggedIn()) {
      const { onUnAuthorize, onSuccess, onError } = params || {};
      try {
        const response = await getSessionFact();
        window.Beacon("identify", {
          name: response.firstName + " " + response.lastName,
          email: response.email,
        });
        window.Beacon("config", {
          docsEnabled: true,
        });
        dispatch(actions.setUser(response));
        getClientInfo();
        if (onSuccess) onSuccess(response);
      } catch (error: any) {
        // dispatch(actions.setLoading(false));

        if (error.statusCode === 401) {
          util.session.onUnAuthorize();

          if (onUnAuthorize) {
            onUnAuthorize(error);
            window.Beacon("logout");
            window.Beacon("config", {
              docsEnabled: false,
            });
            window.Beacon('navigate', '/ask/chat/')
          }
        } else {
          dispatch(actions.setError(error));
        }
        if (onError) onError(error);
      }
    } else {
      getClientInfo();
      window.Beacon("logout");
      window.Beacon("config", {
        docsEnabled: false,
      });
      window.Beacon('navigate', '/ask/chat/')
      dispatch(actions.reset());
    }
  };

  const getClientInfo = () => {
    getClientInfoFact().then(
      (data: any) => {
        dispatch(actions.setLoading(false));
        setSessionLoaded(true);
        dispatch(actions.setClient(data));
      },
      () => {
        dispatch(actions.setLoading(false));
      }
    );
  };

  const patchClientInfo = (data: any) => {
    return patchClientInfoFact(data).then(
      (data: any) => {
        dispatch(actions.setLoading(false));
        setSessionLoaded(true);
        dispatch(actions.setClient(data));
      },
      () => {
        dispatch(actions.setLoading(false));
      }
    );
  };

  const login = (payload: {
    token: string;
    expiry: string;
    user: User;
    redirectUrl?: string | null;
  }) => {
    const { token, user, expiry, redirectUrl } = payload;
    util.session.login(token, expiry);
    dispatch(actions.setToken(token));
    dispatch(actions.setExpiry(expiry));
    dispatch(actions.setUser(user));
    if (redirectUrl) {
      history.push(redirectUrl);
      return;
    }
    history.push("/launcher");
  };

  const resetSession = () => {
    dispatch(actions.reset());
    util.session.logout();
  };

  const logout = () => {
    history.push(util.appUrls.logout);
  };

  const isLoggedIn = () => !!util.session.getToken();

  return {
    ...session,
    login,
    logout,
    fetchSession,
    resetSession,
    isLoggedIn,
    sessionLoaded,
    getClientInfo,
    patchClientInfo,
  };
}
