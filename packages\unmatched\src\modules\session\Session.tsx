// node modules
import { ReactNode, useEffect } from "react";
// import { ThemeProvider } from "styled-components";
// Helpers
import util from "../../utils";
// import { Div, Image } from "../../components";
import useSession from "../../modules/session/hook";
import { Text } from "@repo/ui/components/Text";

interface SessionProps {
  children: ReactNode;
  appFailTemplate?: Function;
}

const Session = (props: SessionProps) => {
  const { children, appFailTemplate } = props;
  const session = useSession();
  useEffect(() => {
    initSession();
  }, []);

  const initStorageEvent = () => {
    window.addEventListener("storage", ({ key }: any) => {
      if (!key) {
        session.logout();
      }
    });
  };

  const initSession = async () => {
    util.api.addInterceptors();
    initStorageEvent();
    session.fetchSession();
  };

  if (session.isLoading || !session.sessionLoaded) {
    // return <Loader text="Loading Session...." />;
    return (
      <div className="flex items-center justify-center min-h-screen">
        {session.client.loader ? (
          <img
            src={session.client.loader}
            style={{
              width: "100px",
              height: "auto",
            }}
          />
        ) : (
          <Text.H2>Loading Session....</Text.H2>
        )}
      </div>
    );
    // Application Loading Component
  } else if (session.error.msg) {
    // Application Failed to load component
    return appFailTemplate
      ? appFailTemplate(session.error)
      : "There is some issue with the server";
  }
  return children;
  // <ThemeProvider theme={session.theme}>{children}</ThemeProvider>;
};

export default Session;
