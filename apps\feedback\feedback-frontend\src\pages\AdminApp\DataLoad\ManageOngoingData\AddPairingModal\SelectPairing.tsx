import React from "react";
import { CustomModal as Modal, Text, Button } from "unmatched/components";

export default function SelectPairing(props: any) {
  const { setScreen } = props;
  return (
    <>
      <Modal.Body>
        <Text.P1 className="mb-2">
          <b>Add more pairings</b>
        </Text.P1>
        <Text.P2>
          You can add more pairings in bulk by uploading a csv or excel file or
          you can also add pairings one by one manually.
          <br />
          <b>
            Note : All exisiting rules will once again be validated for new
            pairings.
          </b>
        </Text.P2>
      </Modal.Body>
      <Modal.Footer>
        <Button
          variant="outline-primary"
          className="mx-1"
          onClick={() => setScreen(2)}
        >
          Manually Add
        </Button>
        {/* <Button className="mx-1" onClick={() => setScreen(3)}>
          Upload File
        </Button> */}
      </Modal.Footer>
    </>
  );
}
