// Node modules
import React from "react";
// Helpers
import FORM_SCHEMA, { validatePassword } from "../Password/password-form";
import AuthContainer from "../AuthContainer";
// import { authTokenSelector } from "../auth-selectors";
import { useFormik, useCallback } from "unmatched/hooks";
import useToastr from "unmatched/modules/toastr/hook";
// Components
import { Button, Card, Div, Form, Text, Span } from "unmatched/components";
import Loader from "../Loader";
import PasswordForm from "../Password/PasswordForm";
import { useParams } from "react-router-dom";
import { useHistory, useState } from "unmatched/hooks";
import { resetFact } from "../auth-api";
import appUrls from "unmatched/utils/urls/app-urls";

interface ParamTypes {
  email: string;
  token: string;
}

const META_DATA = {
  title: "Password Reset",
  content: "Setup a new password for your account",
  loadingTitle: "Resetting password",
  loadingContent: "Please wait while we reset your password",
};

export default function Activation() {
  const history = useHistory();
  const { email, token } = useParams<ParamTypes>();

  const meta = META_DATA;

  const { showToast } = useToastr();

  const [state, setState] = useState({
    token,
    email,
    password: "",
    confirmPassword: "",
    tooltip: {
      number: false,
      alphabet: false,
      min: false,
    },
    isLoading: false,
    error: {
      statusCode: 0,
      msg: "",
    },
  });
  // const state = useSelector((store: any) => store.auth.login);

  const onFetchFail = (err: any, payload: any) => {
    showToast({
      show: true,
      title: "Error",
      variant: "danger",
      content: err.msg,
    });
    if (err.msg === "WRONG_TOKEN" || err.msg === "USER_NOT_FOUND") {
      history.push(appUrls.auth.login);
    }
    setState((state: any) => ({
      ...state,
      isLoading: false,
      ...payload,
      error: err,
    }));
  };

  const onReset = async (data: any) => {
    setState((state: any) => ({
      ...state,
      isLoading: true,
    }));
    try {
      await resetFact(data);
      showToast({
        show: true,
        title: "Password Successfully Reset",
        variant: "success",
        content: "Enter login details to continue",
      });
      history.push(appUrls.auth.login);
    } catch (err) {
      onFetchFail(err, {});
    }
  };

  const formikOptions = {
    initialValues: {
      email: email,
      token,
      password: "",
      confirmPassword: "",
    },
    validationSchema: FORM_SCHEMA,
    onSubmit: (values: any) => {
      const { email, token } = state;
      const { password } = values;
      if (email && token) onReset({ email, password, token });
    },
  };

  const formik = useFormik(formikOptions);

  const onPasswordChange = useCallback(
    (e: React.ChangeEvent<any>) => {
      formik.handleChange(e);
      const tooltip: any = validatePassword(e.target.value);
      setState((_state) => ({
        ..._state,
        tooltip,
      }));
    },
    [formik]
  );

  let Content = null;

  if (state.isLoading) {
    Content = (
      <Loader title={meta.loadingTitle} content={meta.loadingContent}></Loader>
    );
  } else {
    Content = (
      <Form onReset={formik.handleReset} onSubmit={formik.handleSubmit}>
        <Text.H2 className="text-primary pb-2">{meta.title}</Text.H2>
        <Text.P1 className="pb-4">
          {meta.content}
          <Div className="py-4">
            Account email id: <Span className="font-weight-bold">{email}</Span>
          </Div>
          <Div className="">
            <PasswordForm
              formik={formik}
              onPasswordChange={onPasswordChange}
              tooltip={state.tooltip}
              colXL={12}
            ></PasswordForm>
          </Div>
        </Text.P1>
        <Card.Footer>
          <Button size="lg" variant="primary" type="submit">
            Reset
          </Button>
        </Card.Footer>
      </Form>
    );
  }
  return (
    <AuthContainer className="col-xl-4 offset-xl-4 col-lg-6 offset-lg-3">{Content}</AuthContainer>
  );
}
