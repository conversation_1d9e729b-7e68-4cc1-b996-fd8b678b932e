import React from "react";
import styled from "styled-components";
import { Div, Layout, Card, Text, Button, Icon } from "unmatched/components";
import { useXHR } from "unmatched/hooks";
import useToastr from "unmatched/modules/toastr/hook";
import util from "unmatched/utils";
import { saveSurveyFact } from "../survey-api";

interface CardProps {
  children: React.ReactNode;
  title: string;
  onClick?: Function;
}

const LoaderWrap = styled(Div)`
  height: 465px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
`;

const LoaderH2 = styled.p`
  font-family: "Inter";
  font-style: normal;
  font-weight: 500;
  font-size: 18px;
  line-height: 30px;
  color: #518cff;
  margin-top: 10px;
  margin-bottom: 2px;
`;

const LoaderText = styled.p`
  font-family: "Inter";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 21px;
  color: #2f2f2f;
`;

const ChooseSurveyCard = ({ title, children, onClick }: CardProps) => {
  return (
    <Layout.Col xl={3} lg={3} md={4} sm={6} xs={12} className="pb-2">
      <Card
        onClick={() => {
          if (onClick) onClick();
        }}
        className="h-100 shadow-sm rounded cursor-pointer"
      >
        <Div className="p-3">
          <Text.H3 className="pb-2">{title}</Text.H3>
          {children}
        </Div>
      </Card>
    </Layout.Col>
  );
};

const ChooseType = (props: any) => {
  const survey = useXHR({ isLoading: false });

  // selected
  const [, setSelected] = React.useState("");

  const toastr = useToastr();

  const onUpwardReview = () => {
    setSelected(util.enums.Survey.Upward);
    survey.setLoading(true);
    saveSurveyFact(util.enums.Survey.Upward).then(
      (response: any) => {
        survey.setLoading(false);
        setSelected("");
        props.onTempleteSelect(response.data.id || 1);
      },
      (err: any) => {
        setSelected("");
        survey.setLoading(false);
        props.onTempleteSelect(1);
        survey.onError(err);
        toastr.onError(err);
      }
    );
  };

  const on360DegreeReview = () => {
    setSelected(util.enums.Survey._360Degree);
    survey.setLoading(true);
    saveSurveyFact(util.enums.Survey._360Degree).then(
      (response: any) => {
        survey.setLoading(false);
        setSelected("");
        props.onTempleteSelect(response.data.id || 1);
      },
      (err: any) => {
        setSelected("");
        survey.setLoading(false);
        props.onTempleteSelect(1);
        survey.onError(err);
        toastr.onError(err);
      }
    );
  };

  const onEngagementSelection = () => {
    setSelected(util.enums.Survey.Engagement);
    survey.setLoading(true);
    saveSurveyFact(util.enums.Survey.Engagement).then(
      (response: any) => {
        survey.setLoading(false);
        setSelected("");
        props.onTempleteSelect(response.data.id || 1);
      },
      (err: any) => {
        setSelected("");
        survey.setLoading(false);
        props.onTempleteSelect(1);
        survey.onError(err);
        toastr.onError(err);
      }
    );
  };

  /**
   *  TO Do things:
   *  Add Enum to package
   */
  const onExitSelection = () => {
    setSelected(util.enums.Survey.Exit);
    survey.setLoading(true);
    saveSurveyFact(util.enums.Survey.Exit).then(
      (response: any) => {
        survey.setLoading(false);
        setSelected("");
        props.onTempleteSelect(response.data.id || 1);
      },
      (err: any) => {
        setSelected("");
        survey.setLoading(false);
        props.onTempleteSelect(1);
        survey.onError(err);
        toastr.onError(err);
      }
    );
  };

  const onSelfEvaluation = () => {
    setSelected(util.enums.Survey.Self);
    survey.setLoading(true);
    saveSurveyFact(util.enums.Survey.Self).then(
      (response: any) => {
        survey.setLoading(false);
        setSelected("");
        // props.onTempleteSelect(response.data.id || 1);
        props.onSelectSelfEvaluation(response.data.id);
      },
      (err: any) => {
        setSelected("");
        survey.setLoading(false);
        props.onTempleteSelect(1);
        survey.onError(err);
        toastr.onError(err);
      }
    );
  };

  if (survey.isLoading) {
    return (
      <LoaderWrap>
        <Icon
          icon="fal fa-circle-notch"
          spin
          className="fs-spin my-3 mb-4 fs-60"
          variant="primary"
        />
        <LoaderH2>Preparing...</LoaderH2>
        <LoaderText>Please wait while we setup your survey..</LoaderText>
      </LoaderWrap>
    );
  }

  return (
    <Div className="p-4">
      <Text.P1 className="pb-4 fs-14">
        Choose the survey template type or you can{" "}
        <Button
          className="text-underline px-0 pt-0 pb-1 fs-14 fw-400"
          onClick={() => props.toggleClone(true)}
          variant="link"
        >
          clone an existing survey
        </Button>
        .
      </Text.P1>
      <Layout.Row className="align-items-strech">
        {/* survey.isLoading && selected === util.enums.Survey.Upward */}
        <ChooseSurveyCard onClick={onUpwardReview} title="Upward Review">
          <Text.P2>
            Create an upward review to get feedback from the associates for the
            managers.
          </Text.P2>
        </ChooseSurveyCard>
        <ChooseSurveyCard
          onClick={on360DegreeReview}
          title="360 Degree Feedback"
        >
          <Text.P2>
            Create a 360 degree survey to get feedback from other colleagues.
          </Text.P2>
        </ChooseSurveyCard>
        <ChooseSurveyCard
          onClick={onSelfEvaluation}
          // isLoading={survey.isLoading && selected === util.enums.Survey.Self}
          title="Self Evaluation Survey"
        >
          <Text.P2>Create a self evaluation survey for the employees.</Text.P2>
        </ChooseSurveyCard>
      </Layout.Row>
      <Div className="border-top pt-5 mt-5">
        <Layout.Row className="align-items-strech">
          <ChooseSurveyCard
            onClick={onEngagementSelection}
            title="Engagement Survey"
          >
            <Text.P2>
              Create a survey to know the engagement levels in the employees.
              {/* Create a 360 degree survey to get feedback from other colleagues. */}
            </Text.P2>
          </ChooseSurveyCard>
          <ChooseSurveyCard onClick={onExitSelection} title="Exit Survey">
            <Text.P2>
              Create a survey for departing employee.
              {/* Create a 360 degree survey to get feedback from other colleagues. */}
            </Text.P2>
          </ChooseSurveyCard>
        </Layout.Row>
      </Div>
    </Div>
  );
};

// ChooseType.propTypes = {

// };

export default ChooseType;
