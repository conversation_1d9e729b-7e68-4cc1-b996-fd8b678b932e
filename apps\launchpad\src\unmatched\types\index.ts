export interface Theme {
  primary: string;
  secondary: string;
  success: string;
  warning: string;
  info: string;
  danger: string;
}

export interface User {
  id: number;
  email: string;
  firstName: string;
  lastName?: string;
  title?: string;
  department?: string;
  location?: string;
  role?: string;
  isAdmin?: boolean;
  isPasswordSet?: boolean;
}

export interface Client {
  id?: number;
  lightLogo: string;
  darkLogo: string;
  name: string;
  thumbnail?: string;
  loader?: string;
  loginText?: string;
  contactText: string;
  contacts: [];
  homepage: any;
  homepagePath: any;
  features: [];
}

export interface ErrorType {
  statusCode: number;
  errorCode: string;
  msg: string;
}

export interface XhrType {
  isLoading: boolean;
  error: ErrorType;
}

export interface Session extends XhrType {
  token: string;
  expiry: string;
  user: User;
  client: Client;
  theme: Theme;
  userAgent: any;
}

export interface AppRoute {
  name: string;
  path: string;
  isExact?: boolean;
  isPrivate?: boolean;
  component: Function;
}
