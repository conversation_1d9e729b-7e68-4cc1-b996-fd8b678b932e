import React, { useEffect } from "react";
import { Link } from "react-router-dom";
import {
  Div,
  Layout,
  Span,
  Tabs,
  Tab,
  PageContainer,
  Text,
} from "unmatched/components";
import {
  useState,
  useQuery,
  useHistory,
  useParams,
  useXHR,
} from "unmatched/hooks";
import appUrls from "unmatched/utils/urls/app-urls";
import Overall from "./Overall/Overall";
import Category from "./Category/Category";
import Item from "./Item/Item";
import { getUserByIdFact } from "unmatched/modules/session/api";
import icons from "assets/icons/icons";
import CustomHeader from "pages/AdminApp/Shared/CustomHeader/CustomHeader";
import { Helmet } from "react-helmet";

const { Graph } = icons;

const TYPES = {
  OVERALL: "overall",
  CATEGORY: "catgory",
  ITEM: "item",
};

const PeopleAnalytics = () => {
  const history = useHistory();

  const queryParams = useQuery();

  const { id }: any = useParams();

  const [params, setParams] = useState({
    search: "",
    filter: queryParams.get("filter") || TYPES.OVERALL,
  });

  const user = useXHR({
    defaultResponse: {},
  });

  const onFilterChange = (filter: string) => {
    history.push(appUrls.admin.analytics.people.getAnalyticsUrl(id, filter));
    setParams((_params: any) => ({
      ..._params,
      filter,
    }));
  };

  // const filterState = useFilter();

  // const onSearchChange = (search: string) => {
  //   setParams((_params: any) => ({
  //     ..._params,
  //     search,
  //   }));
  // };

  // const onPageChange = (page: number) => {
  //   setParams((_params: any) => ({
  //     ..._params,
  //     page,
  //   }));
  // };

  const getUserData = async () => {
    try {
      const response = await getUserByIdFact(id);
      user.onSuccess(response);
    } catch (err: any) {
      user.onError(err);
    }
  };

  const getFilterLink = (key: string, title: string) => {
    return (
      <Link
        className={params.filter === key ? "text-primary" : ""}
        to={appUrls.admin.analytics.people.getAnalyticsUrl(user.id, key)}
      >
        {title}
      </Link>
    );
  };

  const getTabsTemplate = () => {
    if (user.isLoading) {
      return "Loading Data..";
    } else if (user.data.id) {
      return (
        <Div className="custom-tabs-2 new-tab-ui pt-5">
          <Div className="pt-5" />
          <Tabs
            activeKey={params.filter}
            onSelect={(k: any) => onFilterChange(k)}
            className="position-fixed w-100 bg-white"
            style={{ top: 92, zIndex: 10 }}
          >
            <Tab
              eventKey={TYPES.OVERALL}
              title={getFilterLink(TYPES.OVERALL, "Overall Analytics")}
            >
              {params.filter === TYPES.OVERALL && (
                <Overall userId={id} user={user.data} />
              )}
            </Tab>
            <Tab
              eventKey={TYPES.CATEGORY}
              title={getFilterLink(TYPES.CATEGORY, "Category-wise Analytics")}
            >
              {params.filter === TYPES.CATEGORY && (
                <Category user={user.data} />
              )}
            </Tab>
            <Tab
              eventKey={TYPES.ITEM}
              title={getFilterLink(TYPES.ITEM, "Item-wise Analytics")}
            >
              {/* */}
              {params.filter === TYPES.ITEM && (
                <Item userId={id} user={user.data} />
              )}
            </Tab>
          </Tabs>
        </Div>
      );
    }
    return "";
  };

  // const getSearchInput = () => {
  //   return (
  //     <FormControl.Search
  //       placeholder="Search for surveys"
  //       value={params.search}
  //       onChange={(evt: any) => onSearchChange(evt.target.value)}
  //     />
  //   );
  // };

  const getEmployeeTitle = (title: string, value: string | number) => {
    return (
      <Text.H3 className="text-muted text-truncate mb-2">
        {title} <Span className="text-dark pl-2">{value}</Span>
      </Text.H3>
    );
  };

  useEffect(() => {
    getUserData();
  }, []);

  return (
    <Div>
      <Helmet>
        <title>
          {user.data.name
            ? `People Analytics - ${user.data.name}`
            : "Loading..."}
        </title>
      </Helmet>
      <CustomHeader
        title={
          <Div className="px-3">
            <Layout.Row className="pt-4">
              <Layout.Col xl={3} lg={3} md={3} sm={12} xs={12}>
                {getEmployeeTitle("Employee Name", user.data.name)}
              </Layout.Col>
              <Layout.Col
                xl={3}
                lg={3}
                md={3}
                sm={12}
                xs={12}
                className="text-xl-right"
              >
                {getEmployeeTitle("Employee ID", user.data.empId)}
              </Layout.Col>
              <Layout.Col
                xl={4}
                lg={3}
                md={3}
                sm={12}
                xs={12}
                className="text-xl-right"
              >
                {getEmployeeTitle("Email id", user.data.email)}
              </Layout.Col>
              <Layout.Col
                xl={2}
                lg={3}
                md={3}
                sm={12}
                xs={12}
                className="text-xl-right"
              >
                {getEmployeeTitle("Title", user.data?.metadata?.title)}
              </Layout.Col>
            </Layout.Row>
          </Div>
        }
        breadcrumbs={[
          {
            label: "Analytics",
            icon: <Graph className="grey-icon__svg" />,
          },
          {
            label: "People Analytics",
            route: appUrls.admin.analytics.people.list,
          },
          { label: user.data.name },
        ]}
        noShadow
        style={{ padding: "25px 30px 47px" }}
      />
      <PageContainer noHeader>
        <Div className="p-3">
          <Div>{getTabsTemplate()}</Div>
          <Div className="pt-4"></Div>
        </Div>
      </PageContainer>
    </Div>
  );
};

export default PeopleAnalytics;
