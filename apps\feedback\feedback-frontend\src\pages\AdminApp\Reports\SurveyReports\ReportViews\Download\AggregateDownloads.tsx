import { getReportsDownloadRequests } from "pages/AdminApp/Reports/reports-api";
import { useEffect, useQuery, useState, useTable } from "unmatched/hooks";
import { Text, Table, Div } from "unmatched/components";
import { util } from "@unmatchedoffl/ui-core";

export function AggregateDownloads({ surveyId }: any) {
  const tableMeta = useTable({
    size: 10,
  });

  const [loading, setLoading] = useState(true);
  const queryParams = useQuery();

  const getStatusText = (status: string) =>
    ({
      SUC: "Ready",
      FAIL: "Failed",
      QUE: "Preparing",
    }[status]);

  const [binderDownloads, setBinderDownloads] = useState([]);
  const getReports = async (page?: number) => {
    try {
      setLoading(true);
      const dt = await getReportsDownloadRequests({
        surveyId,
        label: "AGGREGATE",
        page_size: 10,
        page: page ?? 1,
      });
      setBinderDownloads(dt.results);
      tableMeta.updatePagination({
        totalPages: dt.count_pages,
        totalItems: dt.count_items,
      });
      setLoading(false);
    } catch (error) {}
  };

  useEffect(() => {
    getReports();
  }, [queryParams.get("filter")]);

  const getColumnsData = () => {
    return [
      { key: 2, label: "No.", hasSort: false },
      {
        key: 4,
        label: "Requested Filters",
        hasSort: true,
        sortValue: "asc",
        sortKey: "parameters",
      },
      {
        key: 5,
        label: "Requested By",
        hasSort: true,
        sortValue: "asc",
        sortKey: "requestedBy",
      },
      {
        key: 6,
        label: "Requested On",
        hasSort: true,
        sortValue: "dsc",
        sortKey: "requestedBy",
      },
      {
        key: 6,
        label: "Status",
        hasSort: true,
        sortValue: "dsc",
        sortKey: "status",
      },
      { key: 9, label: "Actions", hasSort: false },
    ];
  };

  const [columnsData] = useState<any>(getColumnsData());

  const getRowsTemplate = () => {
    return binderDownloads.map((item: any, index: number) => {
      const isEven = index % 2 === 0 || index === 0;
      return (
        <Table.Row even={!isEven} key={item.key}>
          <Table.Data width="60px">
            <Text.P1>{tableMeta.getSNo(index)}.</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>
              {Object.values(item.parameters || {}).join(", ") || "N/A"}
            </Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.requested_by?.email}</Text.P1>
          </Table.Data>
          <Table.Data width="190px">
            <Text.P1>
              {util.date.getBrowserTime(
                item.requested_on,
                "MMM dd, yyyy, HH:mm"
              )}
            </Text.P1>
          </Table.Data>
          <Table.Data width="190px">
            <Text.P1>{getStatusText(item.task?.status)}</Text.P1>
          </Table.Data>
          <Table.Data>
            {item.status}
            <Div
              className="cursor-pointer"
              style={{
                color: "#2F80ED",
                fontSize: 14,
                textDecoration: "underline",
              }}
              onClick={() => {
                item.file && window.open(item.file, "_blank");
              }}
            >
              {item.task?.status === "SUC" && "Download"}
            </Div>
          </Table.Data>
        </Table.Row>
      );
    });
  };
  return (
    <Div className="pt-4">
      <Table
        columns={columnsData}
        isLoading={loading}
        rows={binderDownloads}
        customRows
        render={() => getRowsTemplate()}
        hasPagination
        activePage={tableMeta.page}
        pages={tableMeta.totalPages}
        onPageSelect={(number: number) => {
          tableMeta.onPageSelect(number);
          getReports(number);
          // getDownloadRequests({ page: number });
        }}
        size={tableMeta.size}
        totalItems={tableMeta.totalItems}

        // onSort={(item: any) => {
        //   const label = util.label.getSortingLabel(
        //     item.sortKey,
        //     item.sortValue
        //   );
        //   setColumnsData((_columns: any) => {
        //     return tableMeta.resetColumns(_columns, item);
        //   });
        //   getUsers(null, { ordering: label, search: tableMeta.search });
        // }}
      />
    </Div>
  );
}
