import React from "react";
// Components
import { Text, Layout, Div, Table, FormControl } from "unmatched/components";
import {
  fetchRealPairingsFact,
  fetchSurveyAbstractPairingsFact,
} from "../pairings-api";
import { useTable } from "unmatched/hooks";
import { util } from "@unmatchedoffl/ui-core";
import { get, keys, map } from "lodash";

interface Pairing {
  count_items: number;
  count_pages: number;
  results: Array<Pairs>;
  surveys: Array<{ id: number; name: string }>;
}
interface Pairs {
  rater: PairInfo;
  target: PairInfo;
  file: string;
  survey?: any;
  status?: null | string;
}
interface PairInfo {
  emp_id: string;
  first_name: string;
  last_name: string;
  email: string;
  is_active: boolean;
  metadata: any;
  is_tenant_admin: boolean;
}

export const SURVEY_BUILDER_ROUTE_CHUNK = "admin/survey/create";

const AllPairingsList = ({ survey, show }: any) => {
  // const { showToast } = useToastr();
  const tableMeta = useTable({
    size: 15,
  });
  const [lSearch, setLSearch] = React.useState("");
  const [isLoading, setIsLoading] = React.useState(true);
  const [ordering, setOrdering] = React.useState("-updated_at");
  const [pairings, setPairings] = React.useState<Pairing>({
    count_items: 0,
    count_pages: 0,
    results: [],
    surveys: [],
  });
  const [filters, setFilters] = React.useState({
    search: "",
    page: 1,
    totalPages: 1,
  });

  React.useEffect(() => {
    if (!show) {
      getPairings();
    }
  }, [show]);

  React.useEffect(() => {
    getPairings();
  }, [filters]);

  const getPairings = async (params?: any) => {
    try {
      setIsLoading(true);
      // const fileInfo = await fetchPairFileInfoFact(id);
      // setFileInfo(fileInfo.data);
      let adminPairings;
      if (window.location.href.includes(SURVEY_BUILDER_ROUTE_CHUNK)) {
        adminPairings = await fetchSurveyAbstractPairingsFact({
          page: filters.page,
          page_size: 15,
          search: filters.search.length > 0 ? filters.search : undefined,
          index_id: survey.id,
          ordering,
          ...(params && params),
        });
      } else {
        adminPairings = await fetchRealPairingsFact({
          page: filters.page,
          page_size: 15,
          search: filters.search.length > 0 ? filters.search : undefined,
          index_id: survey.id,
          ordering,
          ...(params && params),
        });
      }
      setPairings(adminPairings.data);
      tableMeta.updatePagination({
        totalPages: adminPairings.data?.count_pages,
        totalItems: adminPairings.data?.count_items,
      });
      setIsLoading(false);
    } catch (err) {
      console.log(err);
      setIsLoading(false);
    }
  };

  const onSearch = () => {
    setFilters((_filters) => ({
      ..._filters,
      search: lSearch,
      page: 1,
    }));
  };

  const onPageSelect = (page: number) => {
    setFilters((_filters) => ({
      ..._filters,
      page,
    }));
  };

  const getColumns = () => {
    const basic = [{ key: 2, label: "No.", hasSort: false }];
    const getSubmittedStatus = () => {
      if (survey.enableSubmittedStatus === true) {
        return [{ key: 10, label: "Status", hasSort: false }];
      }
      return [];
    };

    return [
      ...basic,
      {
        key: 3,
        label: "Rater Full Name",
        hasSort: true,
        sortKey: "rater__first_name",
        sortValue: "",
      },
      { key: 4, label: "Rater Employee ID", hasSort: false },
      {
        key: 5,
        label: "Rater Email",
        hasSort: true,
        sortKey: "rater__email",
        sortValue: "",
      },
      {
        key: 6,
        label: "Target Full Name",
        hasSort: true,
        sortKey: "target__first_name",
        sortValue: "",
      },
      { key: 7, label: "Target Employee ID", hasSort: false },
      {
        key: 8,
        label: "Target Email",
        hasSort: true,
        sortKey: "target__email",
        sortValue: "",
      },
      { key: 9, label: "Version", hasSort: false },
      ...getSubmittedStatus(),
    ];
  };

  const columnsData = getColumns(); // deepscan-disable-line REFERENCE_BEFORE_LEXICAL_DECL

  const getCompColumns = () => {
    let columnsList = keys(columnsData);
    columnsList = map(columnsList, (key: string) => ({
      ...get(columnsData, key),
      key,
    }));
    return columnsList;
  };

  const getRows = () => {
    return pairings?.results?.map((item: Pairs, index: number) => {
      const isEven = index % 2 === 0 || index === 0;
      return (
        <Table.Row even={!isEven} key={item.target.emp_id}>
          <Table.Data width="70px">
            <Text.P1>{filters.page * 10 - 10 + index + 1}.</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>
              {item.rater.first_name} {item.rater.last_name}
            </Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.rater.emp_id}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.rater.email}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>
              {item.target.first_name} {item.target.last_name}
            </Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.target.emp_id}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.target.email}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.survey?.name}</Text.P1>
          </Table.Data>
          {survey.enableSubmittedStatus === true && (
            <Table.Data>
              <Text.P1>{item.status}</Text.P1>
            </Table.Data>
          )}
        </Table.Row>
      );
    });
  };

  return (
    <>
      <Layout.Row className="justify-content-end  my-2">
        <Layout.Col className="py-1 d-flex flex-row w-100" lg={5} md={12}>
          <Div className="search-wrap w-100">
            <FormControl.Search
              value={lSearch}
              onChange={(evt: any) => setLSearch(evt.target.value)}
              onSearch={onSearch}
              placeholder="Search for name, email or emp "
            />
          </Div>
        </Layout.Col>
      </Layout.Row>
      <Table
        columns={getCompColumns()}
        rows={pairings.results}
        customRows
        render={() => getRows()}
        hasPagination
        activePage={filters.page}
        pages={tableMeta.totalPages}
        onPageSelect={onPageSelect}
        isLoading={isLoading}
        onSort={(item: any) => {
          const label = util.label.getSortingLabel(
            item.sortKey,
            item.sortValue
          );
          setColumnsData((_columns: any) => {
            return Object.values(tableMeta.resetColumns(_columns, item));
          });
          setOrdering(label);
          getPairings({ ordering: label });
        }}
        size={tableMeta.size}
        totalItems={tableMeta.totalItems}
      />
    </>
  );
};

export default AllPairingsList;
