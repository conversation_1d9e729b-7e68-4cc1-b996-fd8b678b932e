name: feedback-frontend-deploy

run-name: Feedback Frontend Deploy

on:
  push:
    branches: ["development", "master", "migration"]

env:
  AWS_REGION: us-east-1
  BRANCH_NAME: ${{ github.head_ref || github.ref_name }}

permissions:
  contents: read
  packages: read

jobs:
  build:
    name: Build
    runs-on: ubuntu-latest
    steps:
      - name: Clone repository
        uses: actions/checkout@v3
      - name: Setup Node
        uses: actions/setup-node@v3
        with:
          node-version: 16
          cache: npm
          registry-url: "https://npm.pkg.github.com"

      #- name: Upgrade NPM
      #run: npm install -g npm

      - name: Install dependencies
        run: npm ci --ignore-scripts
        env:
          NODE_AUTH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Generate build
        env:
          CI: false
        run: |
          npm run build
          mkdir build/feedback
          mv build/static/ build/feedback/static/
      # Share artifact inside workflow
      - name: Share artifact inside workflow
        uses: actions/upload-artifact@v4
        with:
          name: react-github-actions-build
          path: ./build

  deploy:
    name: Deploy
    runs-on: ubuntu-latest
    needs: build
    environment: Development

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      # Download previously shared build
      - name: Get artifact
        uses: actions/download-artifact@v4
        id: download
        with:
          name: react-github-actions-build
          path: build

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: S3 Deploy
        id: aws-eb-deploy
        run: npm run-script deploy-${{ env.BRANCH_NAME }}
