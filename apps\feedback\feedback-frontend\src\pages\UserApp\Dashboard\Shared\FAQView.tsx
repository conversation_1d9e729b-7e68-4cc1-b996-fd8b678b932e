import React, { useContext } from "react"; //
import { Div, Icon } from "unmatched/components";
import styled from "styled-components";
import Accordion from "react-bootstrap/Accordion";
import { useAccordionToggle } from "react-bootstrap/AccordionToggle";
import AccordionContext from "react-bootstrap/AccordionContext";

export default function FAQView({ faqs }: any) {
  return (
    <Div className="pt-3">
      {faqs.map((data: any, index: number) => (
        <Accordion defaultActiveKey={`${index}`}>
          <>
            <ContextAwareToggle eventKey={`${index}`}>
              {data.question}
            </ContextAwareToggle>
            <Accordion.Collapse eventKey={`${index}`} className="">
              <Content className="mx-3">
                <Div dangerouslySetInnerHTML={{ __html: data.answer }} />
              </Content>
            </Accordion.Collapse>
          </>
        </Accordion>
      ))}
    </Div>
  );
}

function ContextAwareToggle({ children, eventKey, callback }: any) {
  const currentEventKey = useContext(AccordionContext);

  const decoratedOnClick = useAccordionToggle(
    eventKey,
    () => callback && callback(eventKey)
  );

  const isCurrentEventKey = currentEventKey === eventKey;

  return (
    <ToggleButton
      // style={{ backgroundColor: isCurrentEventKey ? "pink" : "lavender" }}
      onClick={decoratedOnClick}
    >
      <Div className="d-inline-block" style={{ width: 16 }}>
        <Icon
          icon={`far ${isCurrentEventKey ? "fa-angle-down" : "fa-angle-right"}`}
        />
      </Div>
      {children}
    </ToggleButton>
  );
}

const ToggleButton = styled.h4`
  width: 100%;
  cursor: pointer;
  font-weight: 400;
  background: none;
  color: #518cff;
  border: none;
  font-size: 14px;
  margin-top: 10px;
  &:focus {
    outline: none;
  }
`;
const Content = styled.div`
  h1 {
    font-size: 16px;
    font-weight: 700;
  }
  h2 {
    font-size: 14px;
    font-weight: 700;
  }

  p,
  ol,
  li,
  a {
    font-size: 14px;
  }
  p {
    margin-bottom: 0.5rem;
  }
  a {
    text-decoration: underline;
  }
  ol,
  ul {
    padding-left: 15px;
  }
  ol > li,
  ul > li {
    padding-left: 8px;
  }
`;
