import React from "react";
import { Loader2 } from "lucide-react";
import { UseFormReturn } from "react-hook-form";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@repo/ui/components/form";
import { Label } from "@repo/ui/components/label";
import { Input } from "@repo/ui/components/input";
import { Button } from "@repo/ui/components/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@repo/ui/components/select";
import { FormattedMetadataField } from "../employeesService";

// Custom FormLabel that doesn't turn red on error
const CustomFormLabel = ({ className, children, ...props }: React.ComponentProps<typeof Label>) => {
  return (
    <Label className={className} {...props}>
      {children}
    </Label>
  );
};

interface EmployeeFormProps {
  form: UseFormReturn<any>;
  onSubmit: (e: React.FormEvent) => void;
  metadataFields: FormattedMetadataField[];
  isSubmitting: boolean;
  isEditMode?: boolean;
  onCancel: () => void;
}

export const EmployeeForm: React.FC<EmployeeFormProps> = ({
  form,
  onSubmit,
  metadataFields,
  isSubmitting,
  isEditMode = false,
  onCancel,
}) => {
  return (
    <Form {...form}>
      <form onSubmit={onSubmit} className="space-y-4">
        {/* Static fields */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem className="w-full">
                <CustomFormLabel>Email *</CustomFormLabel>
                <FormControl>
                  <Input
                    {...field}
                    placeholder="Enter email address"
                    type="email"
                    className="w-full"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="emp_id"
            render={({ field }) => (
              <FormItem className="w-full">
                <CustomFormLabel>Emp ID</CustomFormLabel>
                <FormControl>
                  <Input
                    {...field}
                    placeholder="Enter employee ID"
                    className="w-full"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="first_name"
            render={({ field }) => (
              <FormItem className="w-full">
                <CustomFormLabel>First Name *</CustomFormLabel>
                <FormControl>
                  <Input
                    {...field}
                    placeholder="Enter first name"
                    className="w-full"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="last_name"
            render={({ field }) => (
              <FormItem className="w-full">
                <CustomFormLabel>Last Name *</CustomFormLabel>
                <FormControl>
                  <Input
                    {...field}
                    placeholder="Enter last name"
                    className="w-full"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Dynamic fields from metadata */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {metadataFields.map((metaField) => (
            <FormField
              key={metaField.field}
              control={form.control}
              name={metaField.field}
              render={({ field: formField }) => (
                <FormItem className="w-full">
                  <CustomFormLabel>{metaField.displayName || metaField.field}</CustomFormLabel>
                  <FormControl>
                    {metaField.values && metaField.values.length > 0 ? (
                      <Select
                        onValueChange={formField.onChange}
                        defaultValue={formField.value}
                        value={formField.value}
                      >
                        <SelectTrigger className="w-full">
                          <SelectValue
                            placeholder={`Select ${metaField.displayName || metaField.field}`}
                          />
                        </SelectTrigger>
                        <SelectContent>
                          {metaField.values.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    ) : (
                      <Input
                        {...formField}
                        placeholder={`Enter ${metaField.displayName || metaField.field}`}
                        className="w-full"
                      />
                    )}
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          ))}
        </div>

        <div className="flex justify-between">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
          >
            Cancel
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {isEditMode ? "Updating..." : "Adding..."}
              </>
            ) : (
              isEditMode ? "Update Employee" : "Add Employee"
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
};
