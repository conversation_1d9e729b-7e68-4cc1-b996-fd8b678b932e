import { useEffect, useState } from "react";
import {
  Div,
  Text,
  PageContainer,
  Nav,
  ComboBasic<PERSON>ilter,
  <PERSON>mbo<PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from "unmatched/components";
import CustomHeader from "pages/AdminApp/Shared/CustomHeader/CustomHeader";
import "../../../../Survey/ManageSurvey/Emails/components/styles.css";
import appUrls from "unmatched/utils/urls/app-urls";
import icons from "assets/icons/icons";
import { UMTab } from "unmatched/components/Tabs";
import useFilter from "pages/CommonFilters/hook";
import { forEach } from "lodash";
import { generateZippedEngagementReports } from "pages/AdminApp/Reports/reports-api";
import useToastr from "unmatched/modules/toastr/hook";

const { Paste } = icons;

export const userFriendlyTemplatesMap = {
  ENCRYPTED_REPORT: "Encrypted Report",
  REPORT_ACCESS_KEY: "Encrypted Access Key",
};

const AdvancedReports = (props: any) => {
  const [filter, setFilter] = useState("generate");
  const [metas, setMetas] = useState<any>([]);
  const filtersState = useFilter();
  const toastr = useToastr();

  const [selectedMeta, setSelectedMeta] = useState<any>({
    cohort: "",
    applied: undefined,
    isSet: false,
  });

  const { survey, layout } = props;

  useEffect(() => {
    getFilters();
  }, []);

  const getFilters = () => {
    filtersState.getFilters((_filters: any) => {
      const arr: any = [];
      forEach(_filters, (values: any, key: any) => {
        arr.push({
          title: values.label,
          key: key,
          value: values.values.map((value: any) => {
            return { key: value, title: value };
          }),
        });
      });
      setMetas(arr);
    });
  };

  const onFilterChange = (_filter: string) => {
    setFilter(_filter);
  };

  const getNavItem = (title: string, key: string) => {
    return (
      <UMTab
        eventKey={key}
        activeKey={filter}
        onClick={() => onFilterChange(key)}
      >
        {title}
      </UMTab>
    );
  };

  const getCohortSelected = (cohort: string) => {
    if (cohort === undefined || cohort === null) {
      return "";
    }
    return metas.filter((meta: any) => meta.key === cohort)[0]?.title ?? "";
  };

  const filters = {
    showUnflagged: false,
    cohort: {
      options: metas,
      selected: getCohortSelected(selectedMeta.cohort) || "Select",
    },
  };

  return (
    <Div>
      <CustomHeader
        style={{ marginLeft: layout.marginLeft, height: 107 }}
        title={
          <Div>
            <Text.H1 className="pb-2">{survey.name}</Text.H1>
            <Div className="sticky-tabs-container">
              <Nav className="nav-tabs sticky">
                {getNavItem("Generate", "generate")}
              </Nav>
            </Div>
          </Div>
        }
        breadcrumbs={[
          {
            label: "Surveys",
            icon: <Paste className="grey-icon__svg" />,
            route: appUrls.admin.survey.default,
          },
          { label: "Emails" },
          { label: survey.name },
        ]}
      />

      <PageContainer className="pt-5">
        <Div className="pt-2" />
        <Text.H3>Set Differentiating Factor</Text.H3>
        <Text.P1>
          Set the criteria you need to generate individual grouped reports for
        </Text.P1>
        <Div className="mt-3" style={{ width: 131 }}>
          <ComboBasicFilter
            cohart={filters.cohort}
            applied={{}}
            onCohartUpdate={(e: string) => {
              setSelectedMeta({
                ...selectedMeta,
                cohort: e,
              });
            }}
            onAppliedUpdate={() => null}
            isAppliedShown={false}
            hideIcon={true}
          />
        </Div>
        <Text.H3 className="mt-4">Add More Filters</Text.H3>
        <Text.P1>
          If you want to add more filters to get drilled down reports.
        </Text.P1>
        <Div className="mt-4" style={{ width: 541 }}>
          <ComboFilter
            filters={filtersState.filters}
            selected={filtersState.selected}
            onFilterSelect={(_selected: any) => {
              filtersState.onSelect(_selected);
            }}
            onSubmit={() => ""}
          />
        </Div>

        <Button
          variant="primary mt-4"
          disabled={!selectedMeta.cohort}
          onClick={(e: any) => {
            e.stopPropagation();
            if (selectedMeta.cohort) {
              const data = {
                report_for: selectedMeta.cohort,
                filter_by: filtersState.selected,
              };
              generateZippedEngagementReports({
                filters: data,
                index: survey.id,
              }).then(res => {
                if (res?.data?.taskId) {
                  toastr.onSucces({
                    title: "Success",
                    content: "Requested report successfully.",
                  });
                }
              });
            }
          }}
        >
          Generate
        </Button>
      </PageContainer>
    </Div>
  );
};

export default AdvancedReports;
