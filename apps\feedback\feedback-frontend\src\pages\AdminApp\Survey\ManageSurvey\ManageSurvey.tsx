import React from "react";
import styled from "styled-components";
import { useQuery, useHistory } from "unmatched/hooks";
import { useParams } from "react-router-dom";
import {
  Div,
  Layout,
  Tab,
  Nav,
  Text,
  FormGroup,
  Card,
  PageContainer,
} from "unmatched/components";
import appUrls from "unmatched/utils/urls/app-urls";
import useCustomLayout from "pages/AdminApp/Shared/custom-layout-hook";
// import SurveyTimeline from "./SurveyTimeline/SurveyTimeline";
import General from "./General/General";
import ParticipationReport from "./ParticipationReport/ParticipationReport";
import ReportCount from "./ReportCount/ReportCount";
import Emails from "./Emails/Emails";
import { SURVEY, SURVEY_STATES } from "./manage-survey-meta";
import util from "unmatched/utils";
import SurveyDates from "./SurveyDates/SurveyDates";
import {
  getSurveyByIdFactV2,
  getTemplatesFact,
  patchEmailTemplateFact,
  sendCustomEmailReminderFact,
  sendEmailReminderFact,
} from "../survey-api";
import useFilter from "pages/CommonFilters/hook";
import TABS from "./tabs.interface";
import _, { debounce } from "lodash";
import useToastr from "unmatched/modules/toastr/hook";
import AddParticipants from "../CreateSurvey/AddParticipants/AddParticipants";
import icons from "assets/icons/icons";
import MANAGE_SURVEY_ROUTES from "./manage-survey-routes";
import AppRoutes from "pages/AppRoutes";
import Provider from "../CreateSurvey/Provider";
import Participants from "./Participants/Participants";
import { Spinner } from "react-bootstrap";
import FAQ from "../CreateSurvey/FAQs/FAQs";
import Rules from "../CreateSurvey/Rules/Rules";
import useSession from "unmatched/modules/session/hook";
// import { surveyPairingFileUploadFact } from "../CreateSurvey/AddParticipants/PairingsManagement/pairings-api";

const { Paste } = icons;

const NavItem = styled(Nav.Item)`
  font-size: 14px;
  .active {
    background: #ebeff7 !important;
    position: relative;
    color: #2f2f2f !important;
    font-weight: 600;
    border-radius: 0;
    // &::after {
    //   border-bottom: 2px solid #518cff;
    //   position: absolute;
    //   content: "";
    //   z-index: 999;
    //   width: 10%;
    //   left: 15px;
    //   bottom: 0;
    // }
  }
`;

const NavLink = styled(Nav.Link).attrs({
  className: "text-muted",
})``;

export default function ManageSurvey(props: any) {
  const layout = useCustomLayout();
  const queryParams = useQuery();
  const params = useParams<any>();
  const history = useHistory();
  const [activeTab, setActiveTab] = React.useState<string>(
    queryParams.get("filter") || ""
  );
  const [survey, setSurveyInfo] = React.useState<any>(SURVEY);
  const [rawSurvey, setRawSurvey] = React.useState<any>(null);
  const [isLoading, setIsLoading] = React.useState(true);
  const [emailData, setEmailData] = React.useState(null);
  const [emailTemplates, setEmailTemplates] = React.useState([]);
  const [metas, setMetas] = React.useState<any>([]);
  const [selectedVersion, setSelectedVersion] = React.useState(null);
  const toastr = useToastr();
  const filtersState = useFilter();

  const { user } = useSession();

  const getFilters = () => {
    filtersState.getFilters((_filters: any) => {
      const arr: any = [];
      _.forEach(_filters, (values: any, key: any) => {
        arr.push({
          title: values.label,
          key: key,
          value: values.values.map((value: any) => {
            return { key: value, title: value };
          }),
        });
      });
      arr.push({
        title: "All",
        value: ["All"],
      });
      setMetas(arr);
    });
  };

  const getTemplates = (initial = true) => {
    getTemplatesFact(params.id).then((response) => {
      setEmailTemplates(response.results);
      initial &&
        setEmailData(
          response.results.find((r: any) => r.label === "REMINDER_INACTIVE")
        );
    });
  };

  const updateEmailDataDebounced = debounce(
    (data: any, setAgain?: boolean, setIsSaving?: any) => {
      // let body = data.body.replaceAll(`<span style="color: black;"> </span>`, " ");
      setIsSaving?.(true);
      const body = data.body.replaceAll(`rgb(81, 140, 255)`, "black");
      patchEmailTemplateFact(
        {
          ...data,
          index: params.id,
          body,
        },
        data.id
      ).then((response: any) => {
        setAgain && setEmailData(response);
        setIsSaving?.(false);
        getTemplates(false);
      });
    },
    1000
  );

  const updateEmailData = (
    data: any,
    setAgain?: boolean,
    setIsSaving?: any,
    showToast?: boolean
  ) => {
    // let body = data.body.replaceAll(`<span style="color: black;"> </span>`, " ");
    setIsSaving?.(true);
    const body = data.body.replaceAll(`rgb(81, 140, 255)`, "black");
    return patchEmailTemplateFact(
      {
        ...data,
        index: params.id,
        body,
      },
      data.id
    ).then((response: any) => {
      setAgain && setEmailData(response);
      setIsSaving?.(false);
      getTemplates(false);
      showToast &&
        toastr.onSucces({
          title: "Success",
          content: "Template saved Successfully",
        });
      return response;
    });
  };

  const sendEmailReminder = (data: any, showToast: boolean) => {
    return sendEmailReminderFact(data).then((res: any) => {
      {
        showToast &&
          toastr.onSucces({
            title: "Success",
            content: "Email sent Successfully",
          });
      }
      return res;
    });
  };

  const sendCustomEmailReminder = (data: any, showToast: boolean) => {
    return sendCustomEmailReminderFact(data).then((res: any) => {
      {
        showToast &&
          toastr.onSucces({
            title: "Success",
            content: "Email sent Successfully",
          });
      }
      return res;
    });
  };

  async function fetchSurveyInfo() {
    try {
      const response: any = await getSurveyByIdFactV2(params.id);
      const { getFormatedTime } = util.date;
      // const metaResponse = await getMetaLabelsFact([]);
      setRawSurvey(response);
      setSurveyInfo((_d: any) => {
        // console.log(response, "CURRENT SURVEY");
        return {
          ..._d,
          auxIndex: response.auxIndex,
          auxTitle: response.auxTitle,
          name: response.name,
          startDate: getFormatedTime(response.startDate, "MMM yyyy"),
          endDate: getFormatedTime(response.endDate, "MMM yyyy"),
          isoStartDate: response.startDate,
          isoEndDate: response.endDate,
          type: response.type,
          timezone: response.timezone,
          availableTimeZones: response.availableTimeZones,
          enableSubmittedStatus: response.show_submitted_status,
        };
      });
      if (
        queryParams.get("filter") === null &&
        response.type === "SurveyIndexExit"
      ) {
        setActiveTab(TABS.Participant);
      } else if (queryParams.get("filter") === null) {
        setActiveTab(TABS.Dates);
      }
      setIsLoading(false);
    } catch (e) {
      setIsLoading(false);
    }
  }

  React.useEffect(() => {
    getFilters();
    fetchSurveyInfo();
    getTemplates();
    //eslint-disable-next-line
  }, []);

  const meta = util.lib.get(SURVEY_STATES, survey.status);

  const onTabSelect = (filter: string) => {
    history.push(appUrls.admin.survey.getSurveyUrl(params.id || 1, filter));
    setActiveTab(filter);
  };

  const exitSurveySidebar = (
    <>
      <NavItem>
        <NavLink
          className="my-2 dark-nav-link text-white"
          eventKey={TABS.Participant}
          onSelect={(_filter: string) => onTabSelect(_filter)}
        >
          Participants
        </NavLink>
      </NavItem>
      <NavItem>
        <NavLink
          className="dark-nav-link text-white"
          eventKey={TABS.Questionaire}
          onSelect={(_filter: string) => {
            onTabSelect(_filter);
            setSelectedVersion(rawSurvey.versions?.[0]?.id);
            history.push(
              appUrls.admin.survey.create.getManageQuestionsUrl(
                rawSurvey.id,
                rawSurvey.versions?.[0]?.id
              ) + `?filter=QUESTIONAIRE`
            );
          }}
        >
          <Div>Questionaire(s)</Div>
        </NavLink>
        {activeTab === TABS.Questionaire &&
          rawSurvey?.versions?.map((v: any) => {
            const selectedStyle = {
              color: "rgb(81 140 255)",
            };

            return (
              <Text.P1
                className="version-sb-menu"
                style={{
                  marginLeft: 25,
                  paddingLeft: 20,
                  lineHeight: 2,
                  cursor: "pointer",
                  backgroundColor: "#ffffff1a !important",
                  boxShadow: "0px 0px 4px rgb(0 0 0 / 15%)",
                  borderRadius: 2,
                  color: "#fff !important",
                  marginRight: 15,
                  ...(selectedVersion === v.id && selectedStyle),
                }}
                onClick={() => {
                  setSelectedVersion(v.id);
                  onTabSelect("QUESTIONAIRE");
                  history.push(
                    appUrls.admin.survey.create.getManageQuestionsUrl(
                      rawSurvey.id,
                      v.id
                    ) + `?filter=QUESTIONAIRE`
                  );
                }}
                key={v.id}
              >
                <span
                  style={{
                    color:
                      selectedVersion === v.id ? "rgb(81 140 255)" : "#fff",
                  }}
                >
                  {v.name}
                </span>
              </Text.P1>
            );
          })}
      </NavItem>
      <NavItem>
        <NavLink
          className="my-2 dark-nav-link text-white"
          eventKey={TABS.Emails}
          onSelect={(_filter: string) => onTabSelect(_filter)}
        >
          Emails
        </NavLink>
      </NavItem>
      <NavItem>
        <NavLink
          className="my-2 dark-nav-link text-white"
          eventKey={TABS.Faq}
          onSelect={(_filter: string) => onTabSelect(_filter)}
        >
          FAQs
        </NavLink>
      </NavItem>
    </>
  );

  const otherSurveySidebar = (
    <>
      <NavItem>
        <NavLink
          className="my-2 dark-nav-link text-white"
          eventKey={TABS.Dates}
          onSelect={(_filter: string) => onTabSelect(_filter)}
        >
          Survey Properties
        </NavLink>
      </NavItem>

      {survey.type !== "SurveyIndexEngagement" &&
        survey.type !== "SurveyIndexSelf" && (
          <NavItem>
            <NavLink
              className="my-2 dark-nav-link text-white"
              eventKey={TABS.Rules}
              onSelect={(_filter: string) => onTabSelect(_filter)}
            >
              Participants
            </NavLink>
          </NavItem>
        )}
      <NavItem>
        <NavLink
          className="dark-nav-link text-white"
          eventKey={TABS.Questionaire}
          onSelect={(_filter: string) => {
            onTabSelect(_filter);
            setSelectedVersion(rawSurvey.versions?.[0]?.id);
            history.push(
              appUrls.admin.survey.create.getManageQuestionsUrl(
                rawSurvey.id,
                rawSurvey.versions?.[0]?.id
              ) + `?filter=QUESTIONAIRE`
            );
          }}
        >
          <Div>Questionaire(s)</Div>
        </NavLink>
        {activeTab === TABS.Questionaire &&
          rawSurvey?.versions?.map((v: any) => {
            const selectedStyle = {
              color: "rgb(81 140 255)",
            };

            return (
              <Text.P1
                className="version-sb-menu"
                style={{
                  marginLeft: 25,
                  paddingLeft: 20,
                  lineHeight: 2,
                  cursor: "pointer",
                  backgroundColor: "#ffffff1a !important",
                  boxShadow: "0px 0px 4px rgb(0 0 0 / 15%)",
                  borderRadius: 2,
                  color: "#fff !important",
                  marginRight: 15,
                  ...(selectedVersion === v.id && selectedStyle),
                }}
                onClick={() => {
                  setSelectedVersion(v.id);
                  onTabSelect("QUESTIONAIRE");
                  history.push(
                    appUrls.admin.survey.create.getManageQuestionsUrl(
                      rawSurvey.id,
                      v.id
                    ) + `?filter=QUESTIONAIRE`
                  );
                }}
                key={v.id}
              >
                <span
                  style={{
                    color:
                      selectedVersion === v.id ? "rgb(81 140 255)" : "#fff",
                  }}
                >
                  {v.name}
                </span>
              </Text.P1>
            );
          })}
      </NavItem>

      <NavItem>
        <NavLink
          className="my-2 dark-nav-link text-white"
          eventKey={TABS.Participants}
          onSelect={(_filter: string) => onTabSelect(_filter)}
        >
          {survey.type === "SurveyIndexEngagement" ||
          survey.type === "SurveyIndexSelf"
            ? "Participants"
            : "Pairings"}
        </NavLink>
      </NavItem>

      <NavItem>
        <NavLink
          className="my-2 dark-nav-link text-white"
          eventKey={TABS.Emails}
          onSelect={(_filter: string) => onTabSelect(_filter)}
        >
          Emails
        </NavLink>
      </NavItem>
      <NavItem>
        <NavLink
          className="my-2 dark-nav-link text-white"
          eventKey={TABS.Statistics}
          onSelect={(_filter: string) => onTabSelect(_filter)}
        >
          General Statistics
        </NavLink>
      </NavItem>
      <NavItem>
        <NavLink
          className="my-2 dark-nav-link text-white"
          eventKey={TABS.ParticipationReports}
          onSelect={(_filter: string) => onTabSelect(_filter)}
        >
          Participation Report
        </NavLink>
      </NavItem>
      {(survey.type === "SurveyIndexUpward" ||
        survey.type === "SurveyIndex360") && (
        <NavItem>
          <NavLink
            className="my-2 dark-nav-link text-white"
            eventKey={TABS.ReportCount}
            onSelect={(_filter: string) => onTabSelect(_filter)}
          >
            Report Count
          </NavLink>
        </NavItem>
      )}
      <NavItem>
        <NavLink
          className="my-2 dark-nav-link text-white"
          eventKey={TABS.Faq}
          onSelect={(_filter: string) => onTabSelect(_filter)}
        >
          FAQs
        </NavLink>
      </NavItem>
    </>
  );

  return (
    <>
      <Tab.Container activeKey={activeTab}>
        <Layout.Sidebar
          className="dark-sidebar"
          hasHeader={false}
          style={{ marginLeft: layout.sidebar.marginLeft, zIndex: 1050 }}
          width={layout.sidebar.width}
        >
          <Div className="pl-3 py-2">
            {/* <Link style={{ color: "#fff" }} to={appUrls.admin.survey.default}>
            <Icon icon="far fa-chevron-left" /> Surveys
          </Link> */}
          </Div>
          <Nav variant="pills" className="flex-column">
            {isLoading ? (
              <>
                <Div
                  className="d-flex justify-content-center align-items-center"
                  style={{ minHeight: "20vh" }}
                >
                  <Spinner animation="border" color="#fff" />
                </Div>
              </>
            ) : (
              <>
                {survey.type === "SurveyIndexExit"
                  ? exitSurveySidebar
                  : otherSurveySidebar}
              </>
            )}
            {/* <NavItem>
              <NavLink
                className="my-2 dark-nav-link text-white"
                eventKey={TABS.Dates}
                onSelect={(_filter: string) => onTabSelect(_filter)}
              >
                Survey Dates
              </NavLink>
            </NavItem>
            <NavItem>
              <NavLink
                className="my-2 dark-nav-link text-white"
                eventKey={TABS.Participants}
                onSelect={(_filter: string) => onTabSelect(_filter)}
              >
                Participants
              </NavLink>
            </NavItem>
            <NavItem>
              <NavLink
                className="my-2 dark-nav-link text-white"
                eventKey={TABS.Questionaire}
                onSelect={(_filter: string) => {
                  onTabSelect(_filter);
                  setSelectedVersion(rawSurvey.versions?.[0]?.id);
                  history.push(
                    appUrls.admin.survey.create.getManageQuestionsUrl(
                      rawSurvey.id,
                      rawSurvey.versions?.[0]?.id
                    ) + `?filter=QUESTIONAIRE`
                  );
                }}
              >
                <Div>Questionnaire</Div>
              </NavLink>
              {activeTab === TABS.Questionaire &&
                rawSurvey?.versions?.map((v: any) => {
                  const selectedStyle = {
                    color: "rgb(81 140 255)",
                  };

                  return (
                    <div
                      style={{
                        color: "#fff",
                        paddingLeft: 40,
                        lineHeight: 2,
                        cursor: "pointer",
                        ...(selectedVersion === v.id && selectedStyle),
                      }}
                      onClick={() => {
                        setSelectedVersion(v.id);
                        onTabSelect("QUESTIONAIRE");
                        history.push(
                          appUrls.admin.survey.create.getManageQuestionsUrl(
                            rawSurvey.id,
                            v.id
                          ) + `?filter=QUESTIONAIRE`
                        );
                      }}
                      key={v.id}
                    >
                      {v.name}
                    </div>
                  );
                })}
            </NavItem>

            <NavItem>
              <NavLink
                className="my-2 dark-nav-link text-white"
                eventKey={TABS.Participant}
                onSelect={(_filter: string) => onTabSelect(_filter)}
              >
                Participants
              </NavLink>
            </NavItem>

            <NavItem>
              <NavLink
                className="my-2 dark-nav-link text-white"
                eventKey={TABS.Emails}
                onSelect={(_filter: string) => onTabSelect(_filter)}
              >
                Emails
              </NavLink>
            </NavItem>
            <NavItem>
              <NavLink
                className="my-2 dark-nav-link text-white"
                eventKey={TABS.Statistics}
                onSelect={(_filter: string) => onTabSelect(_filter)}
              >
                General Statistics
              </NavLink>
            </NavItem>
            <NavItem>
              <NavLink
                className="my-2 dark-nav-link text-white"
                eventKey={TABS.ParticipationReports}
                onSelect={(_filter: string) => onTabSelect(_filter)}
              >
                Participation Report
              </NavLink>
            </NavItem>
            {(survey.type === "SurveyIndexUpward" ||
              survey.type === "SurveyIndex360") && (
              <NavItem>
                <NavLink
                  className="my-2 dark-nav-link text-white"
                  eventKey={TABS.ReportCount}
                  onSelect={(_filter: string) => onTabSelect(_filter)}
                >
                  Report Count
                </NavLink>
              </NavItem>
            )} */}

            {/* <NavItem>
            <NavLink
              className="my-2 dark-nav-link text-white"
              eventKey={TABS.SurveyTimeLine}
              onSelect={(_filter: string) => onTabSelect(_filter)}
            >
              Survey Timeline
            </NavLink>
          </NavItem> */}
          </Nav>
        </Layout.Sidebar>
        <Div
          className="pb-5 mb-4"
          style={{ marginLeft: layout.container.marginLeft }}
        >
          <Provider {...props}>
            <AppRoutes
              breadcrumbs={[
                {
                  label: "Surveys",
                  icon: <Paste className="grey-icon__svg" />,
                  route: appUrls.admin.survey.default,
                },
                { label: "Manage survey" },
                { label: rawSurvey?.name || "Untitled" },
              ]}
              viewOnly={true}
              manageSurvey={rawSurvey}
              routes={MANAGE_SURVEY_ROUTES}
            />
          </Provider>
          {isLoading ? (
            <>
              <Div
                className="d-flex justify-content-center align-items-center"
                style={{ minHeight: "100vh" }}
              >
                {/* <Spinner animation="border" /> */}
                <img src={util.images.MainLoader} height={110} />
              </Div>
            </>
          ) : (
            <>
              {activeTab !== TABS.Questionaire && (
                <Tab.Content>
                  <Tab.Pane className="bg-white" eventKey={TABS.Dates}>
                    <PageContainer className="pt-2">
                      <Layout.Container fluid className="pt-3">
                        <SurveyDates
                          survey={survey}
                          layout={layout}
                          meta={meta}
                          globalLoad={isLoading}
                          tab={activeTab}
                          rawSurvey={rawSurvey}
                          fetchSurveyInfo={fetchSurveyInfo}
                          user={user}
                        />
                        {console.log("shivam", survey)}

                        {survey?.type === "SurveyIndexUpward" &&
                          survey?.auxIndex && (
                            <Card className="mb-4" noShadow>
                              <Card.Header className="pl-3 py-2">
                                <Layout.Flex>
                                  <Layout.FlexItem>
                                    <Text.H3>Auxiliary Survey</Text.H3>
                                  </Layout.FlexItem>
                                </Layout.Flex>
                              </Card.Header>
                              <Layout.Row>
                                <Layout.Col xl={10}>
                                  <FormGroup.Label className="ml-3">
                                    Survey Title - {survey?.auxTitle}{" "}
                                    {` (${survey?.auxIndex})`}
                                  </FormGroup.Label>
                                </Layout.Col>
                              </Layout.Row>
                            </Card>
                          )}
                      </Layout.Container>
                    </PageContainer>
                  </Tab.Pane>
                  <Tab.Pane className="bg-white" eventKey={TABS.Participants}>
                    <Div style={{ marginTop: 80 }}>
                      <AddParticipants
                        breadcrumbs={[
                          {
                            label: "Surveys",
                            icon: <Paste className="grey-icon__svg" />,
                            route: appUrls.admin.survey.default,
                          },
                          { label: "Manage Survey" },
                          { label: rawSurvey?.name },
                        ]}
                        survey={{ data: rawSurvey }}
                        canDelete={false}
                      />
                    </Div>
                  </Tab.Pane>
                  <Tab.Pane className="bg-white" eventKey={TABS.Participant}>
                    {
                      <Div style={{ marginTop: 80 }}>
                        <Participants
                          survey={survey}
                          layout={layout}
                          indexId={params.id}
                          globalLoad={isLoading}
                          tab={activeTab}
                          metas={metas}
                        />
                      </Div>
                    }
                  </Tab.Pane>
                  <Tab.Pane className="bg-white" eventKey={TABS.Questionaire}>
                    <Div style={{ marginTop: 80 }} />
                  </Tab.Pane>
                  <Tab.Pane className="bg-white" eventKey={TABS.Emails}>
                    {emailData !== null && (
                      <Emails
                        survey={survey}
                        layout={layout}
                        meta={meta}
                        globalLoad={isLoading}
                        tab={activeTab}
                        emailData={emailData}
                        updateEmailData={updateEmailData}
                        updateEmailDataDebounced={updateEmailDataDebounced}
                        sendEmailReminder={sendEmailReminder}
                        sendCustomEmailReminder={sendCustomEmailReminder}
                        indexId={params.id}
                        setEmailData={setEmailData}
                        templates={emailTemplates}
                      />
                    )}
                  </Tab.Pane>
                  <Tab.Pane className="bg-white" eventKey={TABS.Statistics}>
                    <General
                      survey={survey}
                      layout={layout}
                      indexId={params.id}
                      globalLoad={isLoading}
                      tab={activeTab}
                      metas={metas}
                    />
                  </Tab.Pane>
                  <Tab.Pane
                    className="bg-white"
                    eventKey={TABS.ParticipationReports}
                  >
                    <ParticipationReport
                      survey={survey}
                      layout={layout}
                      indexId={params.id}
                      metas={metas}
                      globalLoad={isLoading}
                      tab={activeTab}
                    />
                  </Tab.Pane>
                  <Tab.Pane className="bg-white pt-4 px-3" eventKey={TABS.Faq}>
                    <Div className="pt-5">
                      <FAQ
                        survey={{ data: { ...survey, id: params.id } }}
                        layout={layout}
                        indexId={params.id}
                      />
                    </Div>
                  </Tab.Pane>
                  <Tab.Pane
                    className="bg-white pt-4 px-3"
                    eventKey={TABS.Rules}
                  >
                    <Div className="pt-5">
                      <Rules survey={{ data: { ...survey, id: params.id } }} />
                    </Div>
                  </Tab.Pane>
                  {(survey.type === "SurveyIndexUpward" ||
                    survey.type === "SurveyIndex360") && (
                    <Tab.Pane className="bg-white" eventKey={TABS.ReportCount}>
                      <ReportCount
                        survey={survey}
                        layout={layout}
                        indexId={params.id}
                        metas={metas}
                        globalLoad={isLoading}
                        tab={activeTab}
                      />
                    </Tab.Pane>
                  )}
                </Tab.Content>
              )}
            </>
          )}
        </Div>
      </Tab.Container>
    </>
  );
}
