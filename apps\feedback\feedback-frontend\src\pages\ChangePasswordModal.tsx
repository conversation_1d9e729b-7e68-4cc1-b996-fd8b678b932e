import React, { useState } from "react";
import { CustomModal as Modal } from "unmatched/components";
import useSession from "unmatched/modules/session/hook";
import ChangePassword from "./Auth/ChangePassword/ChangePassword";

export default function ChangePasswordModal(props: any) {
  const {
    user,
    user: { isPasswordSet },
  } = useSession();
  const [passSetComplete, setPassSetComplete] = useState(false);
  return (
    <Modal {...props}>
      <Modal.Header closeButton>
        <Modal.Title>
          {!isPasswordSet || passSetComplete
            ? "Set Password"
            : "Change Password"}
        </Modal.Title>
      </Modal.Header>
      <Modal.Body size="sm">
        <ChangePassword
          setPassSetComplete={setPassSetComplete}
          closeModal={props.onHide}
          user={user}
        />
      </Modal.Body>
    </Modal>
  );
}
