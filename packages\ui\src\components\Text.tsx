import React from "react";
import { cx } from "class-variance-authority";

const H1 = React.forwardRef<HTMLHeadingElement, React.ComponentProps<"h1">>(
  ({ className, children, ...props }, ref) => (
    <h1 className={cx("mb-5 text-2xl font-bold", className)} ref={ref} {...props}>
      {children}
    </h1>
  )
);

const H2 = React.forwardRef<HTMLHeadingElement, React.ComponentProps<"h2">>(
  ({ className, children, ...props }, ref) => (
    <h2 className={cx("mb-5 text-lg", className)} ref={ref} {...props}>
      {children}
    </h2>
  )
);

const P1 = React.forwardRef<HTMLParagraphElement, React.ComponentProps<"p">>(
  ({ className, children, ...props }, ref) => (
    <p className={cx("mt-3 text-sm", className)} ref={ref} {...props}>
      {children}
    </p>
  )
);

const H3 = React.forwardRef<HTMLHeadingElement, React.ComponentProps<"h3">>(
  ({ className, children, ...props }, ref) => (
    <h3 className={cx("mt-3 text-base", className)} ref={ref} {...props}>
      {children}
    </h3>
  )
);

const P2 = React.forwardRef<HTMLParagraphElement, React.ComponentProps<"p">>(
  ({ className, children, ...props }, ref) => (
    <p className={cx("mt-3 text-xs", className)} ref={ref} {...props}>
      {children}
    </p>
  )
);

H1.displayName = "H1";
H2.displayName = "H2";
P1.displayName = "P1";
H3.displayName = "H3";
P2.displayName = "P2";

export const Text = {
  H1,
  H2,
  P1,
  H3,
  P2,
};
