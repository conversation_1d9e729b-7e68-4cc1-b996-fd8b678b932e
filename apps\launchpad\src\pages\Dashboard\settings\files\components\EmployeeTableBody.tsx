import React from "react";
import { TableBody, TableCell, TableRow } from "@repo/ui/components/table";
import { Employee } from "../filesService";
import { Skeleton } from "@repo/ui/components/skeleton";

interface EmployeeTableBodyProps {
  loadingEmployees: boolean;
  employees: Employee[];
  pagination: {
    pageIndex: number;
    pageSize: number;
    pageCount: number;
    totalItems: number;
  };
  onRowClick?: (employee: Employee) => void;
}

export const EmployeeTableBody: React.FC<EmployeeTableBodyProps> = ({
  loadingEmployees,
  employees,
  pagination,
  onRowClick,
}) => {

  if (loadingEmployees) {
    return (
      <TableBody>
        {Array.from({ length: 5 }).map((_, index) => (
          <TableRow key={index}>
            <TableCell><Skeleton className="h-4 w-8" /></TableCell>
            <TableCell><Skeleton className="h-4 w-24" /></TableCell>
            <TableCell><Skeleton className="h-4 w-24" /></TableCell>
            <TableCell><Skeleton className="h-4 w-32" /></TableCell>
            <TableCell><Skeleton className="h-4 w-16" /></TableCell>
            <TableCell><Skeleton className="h-4 w-16" /></TableCell>
          </TableRow>
        ))}
      </TableBody>
    );
  }

  // Show loading state when employees array is empty but we're not in a loading state
  // This happens when we're transitioning between pages
  if (employees.length === 0 && !loadingEmployees) {
    return (
      <TableBody>
        <TableRow>
          <TableCell colSpan={6} className="h-24 text-center">
            Loading employees...
          </TableCell>
        </TableRow>
      </TableBody>
    );
  }

  // This case is now handled by the condition above

  // Calculate the row number starting point based on pagination
  const { pageIndex, pageSize, totalItems } = pagination;
  const startIndex = pageIndex * pageSize;

  // Force the number of rows to match the expected count for the current page
  let displayedEmployees = [...employees];

  // If we're on the last page, calculate how many items should be shown
  const isLastPage = pageIndex === Math.ceil(totalItems / pageSize) - 1;
  const itemsOnLastPage = isLastPage ? (totalItems % pageSize) || pageSize : pageSize;

  // Limit the number of rows to the expected count
  if (isLastPage && displayedEmployees.length > itemsOnLastPage) {
    displayedEmployees = displayedEmployees.slice(0, itemsOnLastPage);
  } else if (!isLastPage && displayedEmployees.length > pageSize) {
    displayedEmployees = displayedEmployees.slice(0, pageSize);
  }

  return (
    <TableBody>
      {displayedEmployees.map((employee, index) => (
        <TableRow
          key={employee.id}
          className={onRowClick ? "cursor-pointer hover:bg-gray-50" : ""}
          onClick={() => onRowClick && onRowClick(employee)}
        >
          <TableCell>{startIndex + index + 1}</TableCell>
          <TableCell>{employee.firstName}</TableCell>
          <TableCell>{employee.lastName}</TableCell>
          <TableCell>{employee.email}</TableCell>
          <TableCell>{employee.empId || "N/A"}</TableCell>
          <TableCell>{employee.departed}</TableCell>
        </TableRow>
      ))}
    </TableBody>
  );
};
