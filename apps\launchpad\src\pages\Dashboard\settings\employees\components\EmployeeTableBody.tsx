import React, { useMemo } from "react";
import { TableBody, TableCell, TableRow } from "@repo/ui/components/table";
import { Loader2 } from "lucide-react";
import { Employee } from "../useEmployees";

interface EmployeeTableBodyProps {
  loadingUsers: boolean;
  employees: Employee[];
  onRowClick?: (employee: Employee) => void;
}

export const EmployeeTableBody: React.FC<EmployeeTableBodyProps> = ({
  loadingUsers,
  employees,
  onRowClick,
}) => {
  // Extract unique metadata keys from all employees
  const metadataKeys = useMemo(() => {
    const keys = new Set<string>();

    employees.forEach((employee) => {
      if (employee.metadata && typeof employee.metadata === 'object') {
        Object.keys(employee.metadata).forEach((key) => keys.add(key));
      }
    });

    // Convert to array and sort alphabetically for consistent display
    return Array.from(keys).sort();
  }, [employees]);

  // Calculate total number of columns (base columns + metadata columns)
  const totalColumns = 5 + metadataKeys.length;

  const handleRowClick = (employee: Employee) => {
    if (onRowClick) {
      onRowClick(employee);
    }
  };

  return (
    <TableBody>
      {loadingUsers ? (
        <TableRow>
          <TableCell colSpan={totalColumns} className="h-24 text-center">
            <div className="flex justify-center items-center">
              <Loader2 className="h-6 w-6 animate-spin mr-2" />
              Loading employees...
            </div>
          </TableCell>
        </TableRow>
      ) : employees.length === 0 ? (
        <TableRow>
          <TableCell colSpan={totalColumns} className="h-24 text-center">
            No employees found.
          </TableCell>
        </TableRow>
      ) : (
        employees.map((employee) => (
          <TableRow
            key={employee.id}
            onClick={() => handleRowClick(employee)}
            className={onRowClick ? "cursor-pointer hover:bg-muted/50" : ""}
          >
            <TableCell>{employee.firstName}</TableCell>
            <TableCell>{employee.lastName}</TableCell>
            <TableCell>{employee.email}</TableCell>
            <TableCell>{employee.empId}</TableCell>
            <TableCell>{employee.departed}</TableCell>

            {/* Render metadata values */}
            {metadataKeys.map((key) => (
              <TableCell key={key}>
                {employee.metadata && typeof employee.metadata === 'object' && employee.metadata[key] !== undefined
                  ? String(employee.metadata[key])
                  : '-'}
              </TableCell>
            ))}
          </TableRow>
        ))
      )}
    </TableBody>
  );
};
