import _ from "lodash";
import React from "react";

interface Props {
  question: any;
  type: string;
  viewOnly?: boolean;
  questionTypes?: any;
  Radio: any;
  Checkbox: any;
  Rating: any;
  Input: any;
  renderOption: any;
}

const QuestionTypes = ({
  question,
  type,
  viewOnly,
  questionTypes,
  Radio,
  Rating,
  Checkbox,
  Input,
  renderOption,
}: Props) => {
    React.useEffect(() => {
        console.log("QuestionTypes mounted");
    }, []);
  const optionTempate = {
    [questionTypes.RADIO]: (
      <Radio
        id={question.radioId}
        question={question}
        renderOption={renderOption}
        questionTypes={questionTypes}
      />
    ),
    [questionTypes.CHECKBOX]: (
      <Checkbox
        id={question.checkboxId}
        question={question}
        viewOnly={viewOnly}
        renderOption={renderOption}
        questionTypes={questionTypes}
      />
    ),
    [questionTypes.INPUT]: <Input />,
    [questionTypes.RATING]: (
      <Rating
        viewOnly={viewOnly}
        question={question}
        renderOption={renderOption}
        questionTypes={questionTypes}
      />
    ),
    [questionTypes.RANKING]: (
      <Rating
        viewOnly={viewOnly}
        question={question}
        renderOption={renderOption}
        questionTypes={questionTypes}
      />
    ),
  };

  return _.get(optionTempate, type);
};

export default QuestionTypes;
