import axios from "axios";
import React from "react";
// import _ from 'lodash';
import { ErrorType } from "../types";

interface DefaultState {
  isLoading?: boolean;
  error?: ErrorType;
  defaultResponse?: any;
  cancelSource?: {
    cancel?: Function;
  };
}

const getSetKey = (key: string) => {
  return `set${key.charAt(0).toUpperCase()}${key.slice(1)}`;
};

const getProgress = (loaded: number, total: number) => {
  return Math.floor((loaded * 100) / total);
};

const defaultError = {
  statusCode: 0,
  errorCode: "",
  msg: "",
};

const useXHR = (data: DefaultState = {}, key = "data", options: any = {}) => {
  const CancelToken = axios.CancelToken;
  const [isLoading, setLoading] = React.useState<boolean>(options.isLoading);
  const [isSaving, setSaving] = React.useState<boolean>(
    options.isSaving || false
  );
  const [response, setResponse] = React.useState(data.defaultResponse);
  const [error, setError] = React.useState<ErrorType>(
    data.error || defaultError
  );
  const [saveError, setSaveError] = React.useState<ErrorType>(defaultError);
  const [cancelSource, setCancelSource] = React.useState(CancelToken.source());
  const [progress, setProgress] = React.useState(0);

  const setKey = getSetKey(key);

  const cancelRequest = () => {
    if (cancelSource && cancelSource.cancel)
      cancelSource.cancel("Canceled Request");
  };

  const onProgress = (evt: any) => {
    const percentCompleted = getProgress(evt.loaded, evt.total);
    setProgress(percentCompleted);
  };

  const onSuccess = (response: any) => {
    setResponse(response);
    setLoading(false);
  };

  const onError = (error: ErrorType) => {
    setLoading(false);
    setError(error);
  };

  const resetError = () => {
    setError(defaultError);
  };

  const getCancelToken = () => {
    return cancelSource.token;
  };

  const getMetaConfig = () => ({
    cancelToken: getCancelToken(),
    onUploadProgress: onProgress,
    onDownloadProgress: onProgress,
  });

  React.useEffect(() => cancelRequest, []);

  return {
    isLoading,
    isSaving,
    progress,
    error,
    saveError,
    cancelSource,
    [key]: response,
    setLoading,
    setSaving,
    setError,
    setSaveError,
    [setKey]: setResponse,
    setCancelSource,
    cancelRequest,
    onProgress,
    onSuccess,
    onError,
    resetError,
    getMetaConfig,
  };
};

export default useXHR;
