// Node modules
import _ from "lodash";
import React, { useState } from "react";

import {
  Card,
  FormikAutoSave,
  Layout,
  Text,
  Form,
  FormGroup,
  FormControl,
  Icon,
  DeleteConfirmation,
  Button,
  Div,
  util
} from "@unmatchedoffl/ui-core";
// Styles
// import { questionsSelector } from "./questions-selector";
// import util from "../../../utils";

const { isFieldInvalid } = util.formik;

interface QuestionFormProps {
  initialValues: any;
  question: any;
  index: number;
  onClone: Function;
  onDelete: Function;
  formik: any;
  questionTypes: any;
  children: any;
  permissions?: any;
}

const QuestionForm = (props: QuestionFormProps) => {
  const {
    question,
    onClone,
    onDelete,
    // hasDemoLabel,
    formik,
    questionTypes,
    initialValues,
    permissions,
  } = props;

  const [doubleConfirm, setDoubleConfirm] = useState(false);

  const {
    values,
    errors,
    handleBlur,
    handleChange,
    handleSubmit,
    handleReset,
  } = formik;

  React.useEffect(() => {
    console.log("question form component mounted")
  }, []);
  
  const getTypeOptions = () =>
    _.map(
      _.keys(questionTypes).filter(
        (item: any) => !["DROPDOWN", "RANKING"].includes(item)
      ),
      (item: any) => (
        <FormControl.SelectItem
          key={item}
          onSelect={() => {
            formik.setFieldValue(
              "type",
              util.lib.get(questionTypes, item),
              true
            );
          }}
        >
          <Text.P1 className={"text-capitalize"}>{item.toLowerCase()}</Text.P1>
        </FormControl.SelectItem>
      )
    );

  const getSelectedType = () => {
    let selected = "";
    _.keys(questionTypes).forEach((key: any) => {
      const _value = _.get(questionTypes, key);
      if (_value === question.type) {
        selected = key;
      }
    });
    return selected;
  };

  const getActionOptions = () => {
    const className = "p-1 pl-2 mr-2";
    return (
      <>
        <Button
          onClick={() => onClone(question.id)}
          variant="light"
          type="button"
          className={className}
        >
          <Icon icon="fal fa-clone" />
          {/* <Text.P1>
            Clone 
          </Text.P1> */}
        </Button>
        <Button
          className={className}
          variant="light"
          type="button"
          onClick={() => setDoubleConfirm(true)}
        >
          <Icon icon="fal fa-trash" />
          {/* <Text.P1>
            Delete 
          </Text.P1> */}
        </Button>
      </>
    );
  };

  const getParagraphCardTemplate = () => {
    return (
      <Card noShadow className="my-3">
        <Card.Body className="p-2">
          <Text.P2>Instruction</Text.P2>
          {/* <Text.H2>Instruction</Text.H2> */}
          <FormGroup className="text-right pt-4">
            <FormControl.Text
              name={"question"}
              value={values["question"]}
              isInvalid={isFieldInvalid(formik, "question")}
              onBlur={handleBlur}
              onChange={handleChange}
              placeholder={"Question"}
            />
            <FormGroup.InValidFeedback>
              {isFieldInvalid(formik, "question") && errors["question"]}
            </FormGroup.InValidFeedback>
          </FormGroup>
          {/* <Text.P1>{question.question}</Text.P1> */}
        </Card.Body>
        <Card.Footer className="border-top px-4">
          <Div className="text-right">
            {!permissions.readOnly && getActionOptions()}
          </Div>
        </Card.Footer>
      </Card>
    );
  };

  const getCardTemplate = () => {
    return (
      <Card noShadow>
        <Card.Body className="p-2">
          <Layout.Row>
            <Layout.Col>
              <Text.P2>Question {props.index}</Text.P2>
            </Layout.Col>
            {permissions.hasName ? (
              <Layout.Col
                xl={3}
                lg={4}
                md={4}
                sm={12}
                xs={12}
                className="ml-auto"
              >
                <FormGroup className="text-right">
                  <FormControl.Text
                    name={"name"}
                    value={values["name"]}
                    isInvalid={isFieldInvalid(formik, "name")}
                    onBlur={handleBlur}
                    onChange={handleChange}
                    placeholder={"name"}
                  />
                  <FormGroup.InValidFeedback>
                    {isFieldInvalid(formik, "name") && errors["name"]}
                  </FormGroup.InValidFeedback>
                </FormGroup>
              </Layout.Col>
            ) : (
              ""
            )}
            <Layout.Col
              xl={3}
              lg={4}
              md={4}
              sm={12}
              xs={12}
              className="ml-auto"
            >
              <FormGroup className="text-right">
                <FormControl.Select value={getSelectedType()}>
                  {!permissions.readOnly && getTypeOptions()}
                </FormControl.Select>
              </FormGroup>
            </Layout.Col>
          </Layout.Row>
          <FormGroup className="pt-2">
            <FormControl.Text
              name={"question"}
              value={values["question"]}
              isInvalid={isFieldInvalid(formik, "question")}
              onBlur={handleBlur}
              onChange={handleChange}
              placeholder={"Question"}
            />
            <FormGroup.InValidFeedback>
              {isFieldInvalid(formik, "question") && errors["question"]}
            </FormGroup.InValidFeedback>
          </FormGroup>
          {props.children}
        </Card.Body>
        <Card.Footer className="border-top px-4">
          <Div>
            <Layout.Row>
              <Layout.Col xl={3} lg={4} md={6} sm={8} xs={12}>
                <Layout.Flex>
                  <Layout.FlexItem>
                    <Text.P1>Mark as Mandatory</Text.P1>
                  </Layout.FlexItem>
                  <Layout.FlexItem className="ml-auto">
                    <FormControl.Switch
                      name={"mandatory"}
                      isInvalid={isFieldInvalid(formik, "mandatory")}
                      checked={values["mandatory"]}
                      onBlur={handleBlur}
                      onChange={handleChange}
                    />
                  </Layout.FlexItem>
                </Layout.Flex>
                {!permissions.hideCollectFeedback ? (
                  <Layout.Flex>
                    <Layout.FlexItem>
                      <Text.P1>Collect Text Feedback</Text.P1>
                    </Layout.FlexItem>
                    <Layout.FlexItem className="ml-auto">
                      <FormControl.Switch
                        name={"textFeedback"}
                        isInvalid={isFieldInvalid(formik, "textFeedback")}
                        checked={values["textFeedback"]}
                        onBlur={handleBlur}
                        onChange={handleChange}
                      />
                    </Layout.FlexItem>
                  </Layout.Flex>
                ) : (
                  ""
                )}
              </Layout.Col>
              <Layout.Col className="text-right">
                {!permissions.readOnly && getActionOptions()}
              </Layout.Col>
            </Layout.Row>
          </Div>
        </Card.Footer>
      </Card>
    );
  };

  return (
    <Form onReset={handleReset} onSubmit={handleSubmit} className="mb-3 mt-2">
      <FormikAutoSave
        initialValues={initialValues}
        values={values}
        onSave={handleSubmit}
      >
        {question.type === questionTypes.PARAGRAPH
          ? getParagraphCardTemplate()
          : getCardTemplate()}

        <DeleteConfirmation
          label="question"
          show={doubleConfirm}
          onCancel={() => setDoubleConfirm(false)}
          onDelete={onDelete}
        />
      </FormikAutoSave>
    </Form>
  );
};

QuestionForm.defaultProps = {
  permissions: {
    hasName: false,
    hideCollectFeedback: false,
    readonly: false,
  },
};

export default QuestionForm;
