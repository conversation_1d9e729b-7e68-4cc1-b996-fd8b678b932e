import { useFormik } from "formik";
import React from "react";
import {
  <PERSON>ton,
  Card,
  Div,
  Form,
  FormControl,
  FormGroup,
  Layout,
  Text,
} from "unmatched/components";
import Loader from "../Loader";
import AuthContainer from "../AuthContainer";
import { useState } from "unmatched/hooks";
import $icons from "assets/icons/icons";
import { reqResetREQFact } from "../auth-api";
import util from "unmatched/utils";

const { getFieldErrorMessage, isFieldInvalid, isFieldValid, yup } = util.formik;

const META_DATA = {
  title: "Password Reset",
  content: "Enter the email address associated with your account",
  loadingTitle: "Check your mailbox",
  loadingContent:
    "Password reset link has been emailed to you. Click the link in the email to reset your password.",
};

export default function ReqestPasswordChange() {
  const meta = META_DATA;

  const [state, setState] = useState({
    isLoading: false,
    sent: false,
    error: {
      statusCode: 0,
      errorCode: "",
      msg: "",
    },
  });

  const onFetchFail = (err: any, payload: any) => {
    setState((state: any) => ({
      ...state,
      isLoading: false,
      ...payload,
      error: err,
    }));
  };

  const onRequest = async (data: any) => {
    setState((state: any) => ({
      ...state,
      isLoading: true,
    }));
    try {
      await reqResetREQFact(data);
      setState((state: any) => ({
        ...state,
        sent: true,
      }));
      // history.push(appUrls.auth.login);
    } catch (err) {
      formik.setFieldValue("email", formData.email);
      // formik.setFieldError("email", Error(state.error.msg).title)
      onFetchFail(err, {});
    }
  };

  const [formData, setFormData] = React.useState({
    email: "",
  });

  // let initialValues =
  const formik = useFormik({
    initialValues: {
      email: formData.email,
    },
    validationSchema: yup.object().shape({
      email: yup
        .string()
        .required("Enter your email id")
        .email("Enter a valid email id"),
    }),
    onSubmit: (values: any) => {
      setFormData({ email: values["email"] });
      onRequest(values);
      // formik.setFormikState(email: values.email})
    },
  });

  let Content = null;

  const Error = (error: string) => {
    const errors: any = {
      USER_NOT_ACTIVE: {
        title: "This email id is not activated",
        description:
          "Check your email to activate or click below to resend the activation email",
      },
      USER_NOT_FOUND: {
        title: "Unable to reset the password",
        description: "Check your email id and try again",
      },
      OTHER: {
        title: "Something went wrong",
        description: "Please try again",
      },
    };

    return errors[error] ?? errors.OTHER;
  };

  if (state.sent) {
    const { CheckMail } = $icons;
    Content = (
      <>
        <Loader
          title={meta.loadingTitle}
          iconTemplate={<CheckMail />}
          content={meta.loadingContent}
        ></Loader>
      </>
    );
  } else if (state.isLoading) {
    Content = <Loader hideContent></Loader>;
  } else {
    Content = (
      <Form onReset={formik.handleReset} onSubmit={formik.handleSubmit}>
        <Text.H2 className="text-primary pb-2">{meta.title}</Text.H2>
        <Text.P1 className="pb-4">
          {meta.content}
          <Div className="py-3">
            <Layout.Row className="py-3">
              <Layout.Col>
                <FormGroup>
                  <FormGroup.Label>Email</FormGroup.Label>
                  <FormControl.Email
                    name="email"
                    isInvalid={isFieldInvalid(formik, "email")}
                    isValid={isFieldValid(formik, "email")}
                    onBlur={formik.handleBlur}
                    onChange={(e: any) => {
                      formik.handleChange(e);
                      setFormData({ email: e.target.value });
                      setState({
                        ...state,
                        error: {
                          statusCode: 0,
                          errorCode: "",
                          msg: "",
                        },
                      });
                    }}
                    placeholder="Email address"
                    value={formik.values["email"] || ""}
                  ></FormControl.Email>
                  <FormGroup.InValidFeedback
                    text={getFieldErrorMessage(formik, "email")}
                  ></FormGroup.InValidFeedback>
                  {state.error.statusCode >= 400 ? (
                    <Div className="mt-3">
                      <FormGroup.InValidFeedback>
                        {Error(state.error.msg).title}
                      </FormGroup.InValidFeedback>
                      <FormGroup.Description className="pt-1">
                        {Error(state.error.msg).description}
                      </FormGroup.Description>
                    </Div>
                  ) : (
                    ""
                  )}
                </FormGroup>
              </Layout.Col>
            </Layout.Row>
          </Div>
        </Text.P1>
        <Card.Footer>
          <Button size="lg" variant="primary" type="submit">
            Request Password Reset
          </Button>
        </Card.Footer>
      </Form>
    );
  }
  return (
    <AuthContainer className="col-xl-4 offset-xl-4">{Content}</AuthContainer>
  );
}
