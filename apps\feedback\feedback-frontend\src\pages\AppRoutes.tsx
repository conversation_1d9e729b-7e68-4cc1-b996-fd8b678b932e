import React, { ReactNode } from "react";
import { Route, RouteChildrenProps, Switch } from "react-router";
// import appUrls from "unmatched/utils/urls/app-urls";
import { AppRoute } from "unmatched/types";
import useSession from "unmatched/modules/session/hook";
import Head from "react-helmet";
import { Navbar, Image, Text } from "unmatched/components";
import UNMATCHED_BOTM_LOGO from "assets/images/unmatched_btm.svg";
import { useWindowSize } from "react-use";

interface PropTypes {
  viewOnly?: boolean;
  routes: Array<AppRoute>;
  children?: ReactNode;
  name?: string;
  breadcrumbs?: any;
  manageSurvey?: any;
  sendInvite: any;
  setSendInvite: any;
  getVersions: any;
}

const AppRoutes = (props: PropTypes) => {
  const {
    routes,
    children,
    viewOnly = false,
    breadcrumbs,
    manageSurvey,
    sendInvite,
    setSendInvite,
    getVersions,
  } = props;
  const { isLoggedIn } = useSession();
  const { width } = useWindowSize();

  return (
    <Switch>
      {routes.map((route: AppRoute, key) => {
        return (
          <Route
            path={route.path}
            key={key}
            exact={route.isExact}
            render={(props: RouteChildrenProps) => {
              if (route.isPrivate && !isLoggedIn()) {
                window.location.href = window.location.origin;
              }
              const Content = route.component;
              return (
                <>
                  <Head>
                    <title>{route.name}</title>
                  </Head>
                  <Content
                    viewOnly={viewOnly}
                    breadcrumbs={breadcrumbs}
                    manageSurvey={manageSurvey}
                    {...props}
                    setSendInvite={setSendInvite}
                    sendInvite={sendInvite}
                    getVersions={getVersions}
                  />
                  {width > 768 && (
                    <Navbar
                      fixed="bottom"
                      className="bg-light py-2 justify-content-end border-top"
                    >
                      <Text.P1 className="text-right text-muted d-flex justify-content-center align-items-cneter py-1">
                        <Image
                          src={UNMATCHED_BOTM_LOGO}
                          height="15px"
                          className="mr-3"
                        />{" "}
                        Employee well-being. Engagement. Upliftment. Culture.
                      </Text.P1>
                    </Navbar>
                  )}

                  {/* {!window?.location?.href?.includes?.('user/dashboard/survey') && } */}
                </>
              );
            }}
          />
        );
      })}
      {children}
    </Switch>
  );
};

export default AppRoutes;
