import axios from "axios";
import util from "unmatched/utils";
// import api from "unmatched/utils/api";
// import API_URLS from "unmatched/utils/api";

// interface LoginPayload {
//       email: string;
//       password: string;
// }

// interface Upload {
//   email: string;
// }

// interface SetPayload {
//   email: string;
//   token: string;
//   password: string;
// }

export const getAllSurveys = async (params?: any) => {
  const config = util.api.getConfigurations(params, {
    headers: {
      // 'Content-Type': 'blob',
    },
  });
  return axios
    .get(`${util.apiUrls.GET_ALL_SURVEYS}`, config)
    .then((response) => {
      return response;
    });
};

export const getAllSurveysV2 = async (params?: any) => {
  const config = util.api.getConfigurations(params, {
    headers: {
      // 'Content-Type': 'blob',
    },
  });
  return axios
    .get(`${util.apiUrls.GET_ALL_SURVEYS_V2}`, config)
    .then((response) => {
      return response;
    });
};

export const getStats = async (target: string, id: any, stat: string) => {
  const config = util.api.getConfigurations({}, {
    headers: {},
  });
  return axios
    .get(`${util.apiUrls.GET_STATS_URL(target, id, stat)}`, config)
    .then((response) => {
      return response;
    });
};