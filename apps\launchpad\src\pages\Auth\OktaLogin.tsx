// import React from "react";
import { useOktaAuth } from "@okta/okta-react";
import { Link2Icon } from "lucide-react";
import { NavLink } from "react-router";
// import { Icon } from "unmatched/components";

function OktaLogin() {
  const { authState, oktaAuth } = useOktaAuth();
  const loginWithRedirect = () =>
    oktaAuth.signInWithRedirect({ originalUri: "/dashboard" });
  const logOut = () => oktaAuth.signOut();

  //   const buttonText = authState?.isAuthenticated ? "Logout" : "Login";
  const btnLogic = authState?.isAuthenticated ? logOut : loginWithRedirect;

  return (
    <>
      {/* <button onClick={btnLogic}>{buttonText}</button> */}
      <NavLink
        to="#"
        onClick={btnLogic}
        className="text-xs pt-1 bg-primary w-full rounded text-white d-flex py-2"
      >
        <div className="px-3 border-right">
          {/* <Icon icon="far fa-link" /> */}
          <Link2Icon className="inline" />
        </div>
        <div className="flex-grow-1 text-center">Login with sso</div>
      </NavLink>
    </>
  );
}

export { OktaLogin };
