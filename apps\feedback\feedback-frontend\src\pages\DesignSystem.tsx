import { useFormik } from "formik";
import React, { useState } from "react";
import {
  Button,
  Text,
  Form,
  FormGroup,
  FormControl,
  Pagination,
  Card,
  Table,
  Icon,
  Layout,
  DatePicker,
  Toast,
  Div,
  FormikInput,
} from "unmatched/components";
// import Categories from "./UserApp/Dashboard/Shared/Categories";
// import QuestionsProgress from "./UserApp/Dashboard/Shared/QuestionsProgress";
import SurveyStatus from "./UserApp/Dashboard/UpwardReview/Pairings/SurveyStatus";
// import styles from './App.module.scss';
// Table

function App() {
  const [toast, setToast] = useState(false);

  const columns = [
    { key: 1, label: "Heading1", hasSort: true },
    { key: 2, label: "Heading2", hasSort: true },
  ];
  const rows = [
    { key: "row1", id: 1, title: "Heading1" },
    { key: "row2", id: 2, title: "Heading2" },
  ];

  const formik = useFormik({
    initialValues: {
      text: "",
      email: "",
      password: "",
      numnber: 0,
    },
    onSubmit: (values: any) => values,
  });

  return (
    <Div className="App">
      <Layout.Container>
        {/* <Categories
          label="string"
          categories={[
            {
              title: "Test 1",
              isActive: true,
              totalQuestions: 23,
              questionsLeft: 15,
            },
            {
              title: "Test 2",
              isActive: false,
              totalQuestions: 44,
              questionsLeft: 12,
            },
          ]}
          
        /> */}

        {/* <QuestionsProgress label="Questions" total={56} status="" />
        <QuestionsProgress label="Completed" total={12} status="success" /> */}

        {/* setFilters={() => { }}
        filters={{}} */}
        <SurveyStatus
          toDo={12}
          inProgress={33}
          declined={1}
          completed={0}
          selected="TO_DO"
          // eslint-disable-next-line @typescript-eslint/no-empty-function
          setParameters={() => ""}
          parameters={{}}
        />

        <Card className="mt-3">
          <Card.Body>
            <Button onClick={() => setToast(true)}>Show Toast</Button>
            <Toast
              variant="success"
              show={toast}
              title="Success message!"
              delay={2000}
              onClose={() => setToast(false)}
              content="The action has been perfomed."
            />
          </Card.Body>
        </Card>
        <Card className="mt-3">
          <Card.Body>
            <Card.Title>Unmatched form component's</Card.Title>
            <Form>
              <Layout.Row>
                <Layout.Col>
                  <FormGroup>
                    <FormGroup.Label>Email</FormGroup.Label>
                    <FormControl.Email placeholder="password" />
                    <FormGroup.ValidFeedback>
                      Valid Feeddback
                    </FormGroup.ValidFeedback>
                    <FormGroup.InValidFeedback>
                      Invalid Feedback
                    </FormGroup.InValidFeedback>
                    <FormGroup.Description>Description</FormGroup.Description>
                  </FormGroup>
                </Layout.Col>
                <Layout.Col>
                  <FormGroup>
                    <FormGroup.Label>Email</FormGroup.Label>
                    <FormControl.Search placeholder="asdasd" />
                    <FormGroup.ValidFeedback>
                      Valid Feeddback
                    </FormGroup.ValidFeedback>
                    <FormGroup.InValidFeedback>
                      Invalid Feedback
                    </FormGroup.InValidFeedback>
                    <FormGroup.Description>Description</FormGroup.Description>
                  </FormGroup>
                </Layout.Col>
                <Layout.Col>
                  <FormGroup>
                    <FormGroup.Label>Password</FormGroup.Label>
                    <FormControl.Password
                      placeholder="password"
                      autoComplete="off"
                    ></FormControl.Password>
                  </FormGroup>
                </Layout.Col>
                <Layout.Col>
                  <FormGroup>
                    <FormGroup.Label>Select box</FormGroup.Label>
                    <FormControl.Select value="asd">
                      <FormControl.SelectItem>1</FormControl.SelectItem>
                      <FormControl.SelectItem>2</FormControl.SelectItem>
                    </FormControl.Select>
                  </FormGroup>
                </Layout.Col>
              </Layout.Row>
              <Layout.Row>
                <Layout.Col>
                  <FormGroup>
                    <FormControl.Radio>
                      <FormControl.Radio.Label>Radio</FormControl.Radio.Label>
                      <FormControl.Radio.Input />
                    </FormControl.Radio>
                  </FormGroup>
                </Layout.Col>
                <Layout.Col>
                  <FormGroup>
                    <FormControl.Checkbox>
                      <FormControl.Checkbox.Label>
                        Checkbox
                      </FormControl.Checkbox.Label>
                      <FormControl.Checkbox.Input />
                    </FormControl.Checkbox>
                  </FormGroup>
                </Layout.Col>
                <Layout.Col>
                  <FormGroup>
                    <FormControl.Switch />
                  </FormGroup>
                </Layout.Col>
              </Layout.Row>

              <Layout.Row>
                <Layout.Col>
                  <FormGroup>
                    <FormGroup>
                      <FormGroup.Label>Date Picker</FormGroup.Label>
                      <DatePicker
                        selected={new Date()}
                        onSelect={(date: Date) => date}
                        onChange={(date: Date) => date}
                        customInput={<FormControl.Date placeholder="date" />}
                      />
                    </FormGroup>
                  </FormGroup>
                </Layout.Col>
              </Layout.Row>
            </Form>
          </Card.Body>
        </Card>
        <Card className="mt-3">
          <Card.Body>
            <Card.Title>
              Formik inputs for simple form fields which can be internally
              managed by formik data object
            </Card.Title>
            <Layout.Row>
              <Layout.Col>
                <FormikInput.Text
                  label="Email"
                  keyName="email"
                  placeholder="Email"
                  formik={formik}
                />
              </Layout.Col>
              <Layout.Col>
                <FormikInput.Text
                  label="Password"
                  keyName="password"
                  placeholder="Password"
                  formik={formik}
                />
              </Layout.Col>
              <Layout.Col>
                <FormikInput.Text
                  label="Name"
                  keyName="text"
                  placeholder="Name"
                  formik={formik}
                />
              </Layout.Col>
              <Layout.Col>
                <FormikInput.Text
                  label="Age"
                  keyName="number"
                  placeholder="Age"
                  formik={formik}
                />
              </Layout.Col>
            </Layout.Row>
          </Card.Body>
        </Card>
        <Card className="mt-3">
          <Card.Body>
            <Card.Title>Table Component</Card.Title>
            <Table
              columns={columns}
              type="striped"
              rows={rows}
              render={(item: any) => {
                return (
                  <>
                    <Table.Data>
                      <Text.P1>{item.id} gg</Text.P1>
                    </Table.Data>
                    <Table.Data>
                      <Text.P1>{item.title}</Text.P1>
                    </Table.Data>
                  </>
                );
              }}
              hasPagination
              pages={10}
            />
            <Div className="text-left border p-3">
              <Card.Title>Custom Pagination component</Card.Title>

              <Pagination pages={10} active={1}></Pagination>
            </Div>
          </Card.Body>
        </Card>
        <Card className="mt-3">
          <Card.Body>
            <Card.Title>Card Component</Card.Title>
            Cyclone Nivar has left Chennai with an excess of 36% rain for the
            season so far, but weathermen say the active phase of the northeast
            monsoon, which ends in December, may be far from over. There may be
            back-to-back weather systems brewing over the Bay of Bengal that
            could bring more rain to the city and several parts of the state,
            they say.
          </Card.Body>
          <Card.Footer className="pr-4">
            <Button variant="outline-primary" type="submit" title="asdasd">
              Cancel
            </Button>
            <Button
              variant="primary"
              type="submit"
              title="asdasd"
              className="ml-2"
            >
              Okay
            </Button>
          </Card.Footer>
        </Card>
        <Card className="mt-3">
          <Card.Body>
            <Card.Title>Typography</Card.Title>
            <Text.H1>Heading 1</Text.H1>
            <Text.H2>Heading 2</Text.H2>
            <Text.H3>Heading 3</Text.H3>
            <Text.H4>Heading 4</Text.H4>
            <Text.P1>Paragrahp 1</Text.P1>
            <Text.P2>Paragraph 2</Text.P2>
            <Text.L1>Label 1</Text.L1>
            <Div className="pt-4">
              <Card.Title>Icons</Card.Title>
              <Icon icon="fas fa-arrow-right" />
              <Icon icon="fas fa-arrow-left" />
              <Icon icon="fas fa-arrow-up" />
              <Icon icon="fas fa-arrow-down" />
            </Div>
            <Card.Title>Buttons</Card.Title>
            {[
              "primary",
              "outline-primary",
              "secondary",
              "outline-secondary",
              "danger",
              "outline-danger",
              "success",
              "outline-success",
            ].map((item: any) => {
              return (
                <Button key={item} className="mr-3" variant={item}>
                  {item}
                </Button>
              );
            })}
          </Card.Body>
        </Card>
      </Layout.Container>
    </Div>
  );
}

export default App;
