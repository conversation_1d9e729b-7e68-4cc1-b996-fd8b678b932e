// Node modules
import { ReactNode } from "react";
// helpers
import appUrls from "../../unmatched/utils/urls/app-urls";
// components
// import { Layout, Image, Card, Div } from "unmatched/components";
import useSession from "../../unmatched/modules/session/hook";
import { Card } from "@repo/ui/components/card";

interface AuthContainerProps {
  children?: ReactNode;
  className?: string;
}

const AuthContainer = (props: AuthContainerProps) => {
  const { children, className } = props;
  const { client } = useSession();
  return (
    <div className="pb-5 flex justify-center items-center">
      <div className={className}>
        <div className="pt-4 pb-2">
          <a href={appUrls.auth.login} className="block my-5">
            {client.lightLogo.length > 0 ? (
              <img
                height="90"
                width="200"
                className="img-contain"
                src={client.lightLogo}
                alt="CLIENT_LOGO"
              />
            ) : null}
          </a>
        </div>
        <div className="flex-grow-1">
          <Card className="p-5 w-full shadow-sm max-w-3xl mx-auto rounded border">{children}</Card>
        </div>
      </div>
    </div>
  );
};

export default AuthContainer;
