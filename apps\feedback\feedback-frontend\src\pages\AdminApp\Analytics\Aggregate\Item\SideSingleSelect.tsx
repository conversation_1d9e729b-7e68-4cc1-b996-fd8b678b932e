import React from "react";
import styled from "styled-components";
import {
  <PERSON><PERSON>,
  Card,
  Icon,
  Text,
  Div,
  FormControl,
  // Layout,
  // Table,
} from "unmatched/components";
// import PropTypes from 'prop-types'

const SideSingleSelect: any = (props: any) => {
  const {
    sections,
    selected,
    setSelected,
    // years,
    // surveys,
    // onSelectQuestionAPI,
    // setYear,
    // setSurvey,
    filteredComponent,
  } = props;

  const filteredRatingSections = () => {
    // const arr: any[] = [];
    // sections.map((section: any, i: number) => {
    //   const items: any = [];
    //   section
    //     .filter((item: any) => item.type === "questionrating")
    //     .map((item: any) => items.push(item));
    //   if (items.length > 0) {
    //     return arr.push({
    //       ...sections[i],
    //       questions: items,
    //     });
    //   }
    //   return;
    // });

    return sections.filter((item: any) => item.type === "questionrating");
  };

  const onSelect = (evt: any, key: string, title: string) => {
    setSelected({ id: key, name: title });
  };

  // const selectAll = () => {
  //   const arr: any = [];
  //   filteredRatingSections().forEach((el: any) =>
  //     el.questions.map((_d: any) => arr.push(_d.key))
  //   );
  //   setSelected(arr);
  // };

  // const onYearSelect = (item: any) => {
  //   setYear((_data: any) => ({ ..._data, single: item.key }));
  //   onSelectQuestionAPI(item.key, surveys.id, "single");
  //   return;
  // };
  // const onSurveySelect = (item: any) => {
  //   setSurvey((_data: any) => ({ ..._data, single: item }));
  //   onSelectQuestionAPI(years.selected, item.key, "single");
  //   return;
  // };
  return (
    <>
      <Overlay onClick={props.onClose}></Overlay>
      <Card
        className=""
        style={{
          minHeight: "calc(100vh)",
          maxHeight: "calc(100vh)",
          position: "relative",
          zIndex: 100000,
        }}
      >
        <Card.Header className="p-3">
          <Text.H2 className="float-left"></Text.H2>
          <Button
            className="float-right p-0 text-muted"
            variant="link"
            onClick={props.onClose}
          >
            <Icon icon="fal fa-times" />
          </Button>
        </Card.Header>
        <Card.Body className="bg-white v-scroll">
          <Div className="px-0" style={{ minHeight: 300 }}>
            {/* <Layout.Row className="mb-3">
              <Layout.Col className="pr-2 pl-3 col-5">
                <Table.Filter
                  title="Year"
                  selected={years?.selected || ""}
                  options={years?.options || []}
                  onSelect={onYearSelect}
                ></Table.Filter>
              </Layout.Col>
              <Layout.Col className="pl-2 pr-3 col-7">
                <Table.Filter
                  title="Survey"
                  selected={surveys?.selected || ""}
                  options={surveys?.options || []}
                  onSelect={onSurveySelect}
                ></Table.Filter>
              </Layout.Col>
            </Layout.Row> */}
            {filteredComponent()}
            {[""].map((item: any) => {
              const getQuestionsTemplate = () => {
                return filteredRatingSections().map(
                  (question: any, index: number) => {
                    // if (question.type === "Rating")
                    return (
                      <Div className="mx-0 cursor-pointer row" key={index}>
                        <Div
                          className="border px-2 text-center py-2 col"
                          style={{ maxWidth: 35 }}
                        >
                          <FormControl.Radio>
                            <FormControl.Radio.Input
                              checked={selected === question.id}
                              onChange={(evt: any) =>
                                onSelect(evt, question.id, question.label)
                              }
                            />
                          </FormControl.Radio>
                        </Div>

                        <Div className="border px-2 py-2 col fs-12">
                          {question.label}
                        </Div>
                      </Div>
                    );
                  }
                );
              };
              return (
                <Div key={item.id} className="pb-4">
                  <Text.H3 className="py-2">{item.title}</Text.H3>
                  <Div className="mx-0 cursor-pointer row bg-light">
                    <Div
                      className="border px-2 text-center py-2 col"
                      style={{ maxWidth: 35 }}
                    ></Div>

                    <Div className="border px-2 py-2 col fs-12 fw-700">
                      Questions
                    </Div>
                  </Div>
                  {getQuestionsTemplate()}
                </Div>
              );
            })}
          </Div>
        </Card.Body>
      </Card>
    </>
  );
};

// SideSingleSelect.propTypes = {

// }
const Overlay = styled(Div)`
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 99;
  backdrop-filter: blur(3px);
  cursor: pointer;
`;
export default SideSingleSelect;
