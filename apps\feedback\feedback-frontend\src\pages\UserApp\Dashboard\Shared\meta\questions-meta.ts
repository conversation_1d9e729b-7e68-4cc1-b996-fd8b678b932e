export const categories = [
  {
    id: 1,
    title: "Individual Items",
    isActive: true,
    totalQuestions: 23,
    questionsLeft: 15,
  },
  {
    id: 2,
    title: "Summary Item",
    isActive: false,
    totalQuestions: 44,
    questionsLeft: 12,
  },
  {
    id: 3,
    title: "Comments",
    isActive: false,
    totalQuestions: 44,
    questionsLeft: 12,
  },
];
const questions = [
  {
    id: 1,
    title: "Clearly explains what results are expected for a task or project.",
    feedback: "",
    type: "RANKING",
    ranking: {
      id: 1,
      startLabel: "Almost Always",
      endLabel: "Almost Never",
      range: 5,
      selected: 1,
    },
    categoryID: 1,
  },
  {
    id: 2,
    title: "Responds timely and effectively to questions.",
    feedback: "",
    type: "RANKING",
    ranking: {
      id: 1,
      startLabel: "Almost Always",
      endLabel: "Almost Never",
      range: 5,
      selected: 4,
    },
    categoryID: 1,
  },
  {
    id: 3,
    title: "Is respectful of my time.",
    feedback: "",
    type: "RANKING",
    ranking: {
      id: 1,
      startLabel: "Almost Always",
      endLabel: "Almost Never",
      range: 5,
      selected: 4,
    },
    categoryID: 1,
  },
  {
    id: 4,
    title: "Delegates work to me appropriate for my level of seniority.",
    feedback: "",
    type: "RANKING",
    ranking: {
      id: 1,
      startLabel: "Almost Always",
      endLabel: "Almost Never",
      range: 5,
      selected: 4,
    },
    categoryID: 1,
  },
  {
    id: 5,
    title: "Maintains control under pressure.",
    feedback: "",
    type: "RANKING",
    ranking: {
      id: 1,
      startLabel: "Almost Always",
      endLabel: "Almost Never",
      range: 5,
      selected: 4,
    },
    categoryID: 1,
  },
  {
    id: 6,
    title: "Defines my role in the context of the entire transaction/case.",
    feedback: "",
    type: "RANKING",
    ranking: {
      id: 1,
      startLabel: "Almost Always",
      endLabel: "Almost Never",
      range: 5,
      selected: 4,
    },
    categoryID: 1,
  },
  {
    id: 7,
    title: "Expresses appreciation for my contributions.",
    feedback: "",
    type: "RANKING",
    ranking: {
      id: 1,
      startLabel: "Almost Always",
      endLabel: "Almost Never",
      range: 5,
      selected: 4,
    },
    categoryID: 1,
  },
  {
    id: 8,
    title: "Encourages initiative and welcomes questions and new ideas.",
    feedback: "",
    type: "RANKING",
    ranking: {
      id: 1,
      startLabel: "Almost Always",
      endLabel: "Almost Never",
      range: 5,
      selected: 4,
    },
    categoryID: 1,
  },
  {
    id: 9,
    title: "Provides learning opportunities by giving challenging assignments.",
    feedback: "",
    type: "RANKING",
    ranking: {
      id: 1,
      startLabel: "Almost Always",
      endLabel: "Almost Never",
      range: 5,
      selected: 4,
    },
    categoryID: 1,
  },
  {
    id: 10,
    title:
      "Provides guidance to help enhance my skills and knowledge or learn new ones.",
    feedback: "",
    type: "RANKING",
    ranking: {
      id: 1,
      startLabel: "Almost Always",
      endLabel: "Almost Never",
      range: 5,
      selected: 4,
    },
    categoryID: 1,
  },
  {
    id: 11,
    title: "Provides timely feedback",
    feedback: "",
    type: "RANKING",
    ranking: {
      id: 1,
      startLabel: "Almost Always",
      endLabel: "Almost Never",
      range: 5,
      selected: 4,
    },
    categoryID: 1,
  },
  {
    id: 12,
    title: "Provides clear, constructive, and continuous feedback.",
    feedback: "",
    type: "RANKING",
    ranking: {
      id: 1,
      startLabel: "Almost Always",
      endLabel: "Almost Never",
      range: 5,
      selected: 4,
    },
    categoryID: 1,
  },
  {
    id: 13,
    title:
      "Gives encouragement, support and/or advice when I have a difficult and stressful task or responsibility.",
    feedback: "",
    type: "RANKING",
    ranking: {
      id: 1,
      startLabel: "Almost Always",
      endLabel: "Almost Never",
      range: 5,
      selected: 4,
    },
    categoryID: 1,
  },
  {
    id: 14,
    title:
      "Provides support to balance my work responsibilities and personal life (e.g., respects my personal commitments and makes reasonable accommodations when possible).",
    feedback: "",
    type: "RANKING",
    ranking: {
      id: 1,
      startLabel: "Almost Always",
      endLabel: "Almost Never",
      range: 5,
      selected: 4,
    },
    categoryID: 1,
  },
  {
    id: 15,
    title:
      "Encourages cooperation and teamwork among people who depend on each other to get the work done.",
    feedback: "",
    type: "RANKING",
    ranking: {
      id: 1,
      startLabel: "Almost Always",
      endLabel: "Almost Never",
      range: 5,
      selected: 4,
    },
    categoryID: 1,
  },
  {
    id: 16,
    title: "Treats everyone with respect.",
    feedback: "",
    type: "RANKING",
    ranking: {
      id: 1,
      startLabel: "Almost Always",
      endLabel: "Almost Never",
      range: 5,
      selected: 4,
    },
    categoryID: 1,
  },
  {
    id: 17,
    title:
      "Interactions are consistent with the Firm's commitment to inclusion and diversity.",
    feedback: "",
    type: "RANKING",
    ranking: {
      id: 1,
      startLabel: "Almost Always",
      endLabel: "Almost Never",
      range: 5,
      selected: 4,
    },
    categoryID: 1,
  },
  {
    id: 18,
    title:
      "Allows me to participate in training and other professional development opportunities.",
    feedback: "",
    type: "RANKING",
    ranking: {
      id: 1,
      startLabel: "Almost Always",
      endLabel: "Almost Never",
      range: 5,
      selected: 4,
    },
    categoryID: 1,
  },
  {
    id: 19,
    title: "Demonstrates interest in my success as a lawyer.",
    feedback: "",
    type: "RANKING",
    ranking: {
      id: 1,
      startLabel: "Almost Always",
      endLabel: "Almost Never",
      range: 5,
      selected: 4,
    },
    categoryID: 1,
  },
  {
    id: 20,
    title: "I look forward to working with this person again.",
    feedback: "",
    type: "RADIO",
    radio: {
      id: 1,
      selected: 1,
      options: [
        { id: 1, value: "Strongly Agree" },
        { id: 2, value: "Agree" },
        { id: 3, value: "Neither Agree nor Disagree" },
        { id: 4, value: "Disagree" },
        { id: 5, value: "Strongly Disagree" },
      ],
    },
    categoryID: 2,
  },
  {
    id: 21,
    title:
      "Please indicate if you authorize the release of your feedback regardless of whether or not the attorney receives 3 feedback forms.",
    feedback: "",
    type: "RADIO",
    radio: {
      id: 1,
      selected: 1,
      options: [
        { id: 1, value: "Yes" },
        { id: 2, value: "No" },
      ],
    },
    categoryID: 3,
  },
  // {
  //   id: 1,
  //   title: "Clearly explains what results are expected for a task or project.",
  //   feedback: "",
  //   type: "RADIO",
  //   radio: {
  //     id: 1,
  //     selected: 1,
  //     options: [
  //       { id: 1, value: "Option 1" },
  //       { id: 2, value: "Option 2" },
  //     ],
  //   },
  //   categoryID: 1
  // },
  // {
  //   id: 2,
  //   title: "Responds timely and effectively to questions.",
  //   feedback: "",
  //   type: "CHECKBOX",
  //   checkbox: {
  //     id: 1,
  //     selected: [1, 2],
  //     options: [
  //       { id: 1, value: "Option 1" },
  //       { id: 2, value: "Option 2" },
  //       { id: 3, value: "Option 3" },
  //     ],
  //   },
  //   categoryID: 1
  // },
  // {
  //   id: 3,
  //   title: "This is the question title??",
  //   feedback: "",
  //   type: "INPUT",
  //   input: {
  //     id: 1,
  //     value: "",
  //   },
  //   categoryID: 1
  // },
  // {
  //   id: 4,
  //   title: "This is the question title??",
  //   feedback: "",
  //   type: "RANKING",
  //   ranking: {
  //     id: 1,
  //     startLabel: "Extremely Unsatisfactory",
  //     endLabel: "Outstanding",
  //     range: 8,
  //     selected: 4,
  //   },
  //   categoryID: 2
  // },
];

export default questions;
