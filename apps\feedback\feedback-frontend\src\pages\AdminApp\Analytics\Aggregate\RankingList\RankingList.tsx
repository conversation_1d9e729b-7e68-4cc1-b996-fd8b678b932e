import React from "react";
import { <PERSON><PERSON>ontainer, Div } from "unmatched/components";
import Rankings from "./Rankings";
import Categories from "./Categories";
// import { useParams } from "unmatched/hooks";
// import appUrls from '../../../../../helpers/constants/app-urls'

const RankingList = () => {
  // const params = useParams<any>();

  return (
    <Div>
      <PageContainer noHeader={true}>
        <Div className="pt-1">
          {/* <Text.H2 className="pt-4">The ranking list is based on the upward/360 surveys.</Text.H2> */}
          <Rankings />
          <Categories />
        </Div>
      </PageContainer>
    </Div>
  );
};

export default RankingList;
