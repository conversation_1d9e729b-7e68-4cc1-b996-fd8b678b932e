import React from "react";
import { Div } from "unmatched/components";
import styled from "styled-components";

const Wrap = styled(Div)`
    background: #ffeac1;
    width: 98%;
    height: auto;
    display: flex;
    align-items: center;
    padding: 8px 15px;
`;

export const Note = (props: any) => {
  return (
    <Wrap className={props.className} style={props.style}>
      {props.children}
    </Wrap>
  );
};
