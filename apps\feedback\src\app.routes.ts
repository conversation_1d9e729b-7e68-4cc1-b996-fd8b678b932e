
type ID = string;
const ADMIN_URLS = () => {
    const ADMIN = "/admin";
    const SURVEY = `${ADMIN}/survey`;
    const CREATE_SURVEY = `${SURVEY}/create`;
    const ANALYTICS = `${ADMIN}/analytics`;
    const REPORTS = `${ADMIN}/reports`;
    // const SETTINGS = `${ADMIN}/settings`;
    return {
        default: ADMIN,
        HOME: `${ADMIN}/dashboard`,
        SURVEY: {
            default: SURVEY,
            EDIT_COMMENTS: {
                GET_QUESTIONS: (id: ID) => `${SURVEY}/comments/${id}`,
                VIEW_COMMENTS: (id: ID, qID: ID) => `${SURVEY}/comments/${id}/${qID}`,
            },
            CREATE: {
                GET_URL: (id: ID) => `${CREATE_SURVEY}/${id}`,
                GET_QUESTIONS_URL: (id: ID, versionId?: ID) => {
                    return `${CREATE_SURVEY}/${id}/version/${versionId || 0}/questions`;
                },
                GET_MANAGE_QUESTIONS_URL: (id: ID, versionId?: ID) => {
                    return `${SURVEY}/manage/${id}/version/${versionId || 0}/questions`;
                },
                GET_PARTICIPANTS_URL: (id: ID) => `${CREATE_SURVEY}/${id}/paticipants`,
                GET_RULES_URL: (id: ID, filter: any) => {
                    return `${CREATE_SURVEY}/${id}/rules${filter ? `?filter=${filter}` : ''}`;
                },
                // getPairingsUrl: (id: ID) => `${CREATE_SURVEY}/${id}/pairings`,
                // getUsersUrl: (id: ID) => `${CREATE_SURVEY}/${id}/users`,
                GET_PREVIEW_URL: (id: ID, versionId?: ID) =>
                    `${CREATE_SURVEY}/${id}/preview/${versionId || 0}`,
                GET_PUBLISH_URL: (id: ID) => `${CREATE_SURVEY}/${id}/publish`,
                GET_FAQ_URL: (id: ID) => `${CREATE_SURVEY}/${id}/faq`,
                GET_UPWARD_REVIEW_URL: (id: ID) => `${CREATE_SURVEY}/${id}/intro`,
                GET_SEND_URL: (id: ID) => `${CREATE_SURVEY}/${id}/send`,
            },
            GET_SURVEYS_URL: (filter?: string) => {
                const query = filter ? `?filter=${filter}` : "";
                return `${SURVEY}/list${query}`;
            },
            GET_SURVEY_URL: (id: ID, filter?: string) => {
                const query = filter ? `?filter=${filter}` : "";
                return `${SURVEY}/manage/${id}${query}`;
            },
        },
        EMAIL: `${ADMIN}/email`,
        // settings: {
        //   getURL: (filter?: string) => {
        //     const query = filter ? `?filter=${filter}` : "";
        //     return `${SETTINGS}${query}`;
        //   },
        // },
        ANALYTICS: {
            default: ANALYTICS,
            people: {
                default: `${ANALYTICS}/people`,
                list: `${ANALYTICS}/people/list`,
                getListView: (id: ID) => `${ANALYTICS}/people/list/${id}`,
                getAnalyticsUrl: (id: ID, filter?: string) => {
                    const query = filter ? `?filter=${filter}` : "";
                    return `${ANALYTICS}/people/${id}/analyze${query}`;
                },
            },
            geRankingListUrl: () => {
                return `${ANALYTICS}/ranking/list`;
            },
            getAggregateUrl: (filter?: string) => {
                const query = filter ? `?filter=${filter}` : "";
                return `${ANALYTICS}/aggregate${query}`;
            },
            statistics: {
                default: `${ANALYTICS}/stats`,
                getListUrl: (filter?: string) => {
                    const query = filter ? `?filter=${filter}` : "";
                    return `${ANALYTICS}/stats/list${query}`;
                },
                getStatisticsUrl: (id: ID, filter?: string) => {
                    const query = filter ? `?filter=${filter}` : "";
                    return `${ANALYTICS}/stats/survey/${id}${query}`;
                },
            },
            engagement: {
                default: `${ANALYTICS}/engagement`,
                list: `${ANALYTICS}/engagement/list`,
                getListView: (id: ID) => `${ANALYTICS}/engagement/view/${id}`,
                getAnalyticsUrl: (id: ID, filter?: string) => {
                    const query = filter ? `?filter=${filter}` : "";
                    return `${ANALYTICS}/engagement/view/${id}${query}`;
                },
            },
            exit: {
                default: `${ANALYTICS}/exit`,
                list: `${ANALYTICS}/exit/list`,
                getListView: (id: ID) => `${ANALYTICS}/exit/view/${id}`,
                getAnalyticsUrl: (id: ID, filter?: string) => {
                    const query = filter ? `?filter=${filter}` : "";
                    return `${ANALYTICS}/exit/view/${id}${query}`;
                },
            },
        },
        REPORTS: {
            default: REPORTS,
            surveyList: `${REPORTS}/surveys`,
            getSurveyReportsUrl: (id: ID, filter?: string) => {
                const query = filter ? `?filter=${filter}` : "";
                return `${REPORTS}/survey/ind/${id}${query}`;
            },
            getSurveyTemplatesUrl: (id: ID, filter?: string) => {
                const query = filter ? `?filter=${filter}` : "";
                return `${REPORTS}/survey/email/${id}${query}`;
            },
            getSurvey360ReportsUrl: (id: ID, filter?: string) => {
                const query = filter ? `?filter=${filter}` : "";
                return `${REPORTS}/survey/360/${id}${query}`;
            },
        },
        DATALOAD: {
            default: `${ADMIN}/load`,
            create: `${ADMIN}/load/data`,
            getManagePairingsUrl: (id: ID) => `${ADMIN}/load/pairing/${id}`,
            getManageOngoingUrl: (id: ID) => `${ADMIN}/load/ongoing/${id}`,
            getManageEmployeessUrl: (id: ID) => `${ADMIN}/load/employee/${id}`,
        },
    };
};

export default ADMIN_URLS;

export const USER_ROUTES = () => {
    const USER = "/user";
    return {
        default: USER,
        dashboard: {
          default: `${USER}/dashboard`,
          surveyList: `${USER}/dashboard/surveys`,
          getSurveyTermsUrl: (id: ID) => `${USER}/dashboard/terms/${id}`,
          upwardReview: {
            getUrl: (id: ID) => `${USER}/dashboard/survey/${id}/upward`,
            getPairingsUrl: (id: ID) =>
              `${USER}/dashboard/survey/${id}/upward/pairings`,
            getTakeSurveyUrl: (id: ID, surveyId: ID) =>
              `${USER}/dashboard/survey/${id}/upward/take/${surveyId}`,
          },
          getSelfEvaluationUrl: (id: ID) =>
            `${USER}/dashboard/survey/${id}/self-evaluation`,
          getEngagmentUrl: (id: ID) => `${USER}/dashboard/survey/${id}/engagment`,
          getSelfUrl: (id: ID) => `${USER}/dashboard/survey/${id}/self`,
          getExitUrl: (id: ID) => `${USER}/dashboard/survey/${id}/exit`,
        },
        // Survey
        settings: `${USER}/settings`,
        faq: `${USER}/faq`,
        contactUs: `${USER}/contact`,
        reports: `${USER}/reports`,
        privacy: `${USER}/privacy`,
        terms: `${USER}/terms`,
        confidentiality: `${USER}/confidentiality`,
    };
};