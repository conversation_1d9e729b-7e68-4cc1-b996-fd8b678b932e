import React from "react";
import {
  Div,
  Layout,
  Text,
  Modal,
  Button,
  Icon,
  Form,
  DropdownButton,
  Dropdown,
  FormControl,
  Tab,
  Nav,
} from "unmatched/components";
import { useCreateSurveyContext } from "../Provider";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";
import styled from "styled-components";
import sanitizeHtml from "sanitize-html";
import { useFormik } from "formik";
import { createAFAQ, getAllFAQs, deleteAFAQ, patchAFAQ } from "./faq-api";
import PreviewFAQs from "./PreviewFAQ";
import CustomHeader from "pages/AdminApp/Shared/CustomHeader/CustomHeader";
import { UMTab } from "unmatched/components/Tabs";

const FAQ = (props: any) => {
  const {
    // updateBuilderStatus,
    survey: _s,
  } = useCreateSurveyContext();
  //   const params: any = useParams();

  const survey = _s ? _s : props.survey;
    // const { layout } = props;

  const [faqs, setFaq] = React.useState<Array<any>>([]);
  const [add, setAdd] = React.useState<boolean>(false);
  const [modify, setModify] = React.useState<boolean>(false);
  const [activeTab, setActiveTab] = React.useState("all");

  const addFAQForm = useFormik({
    initialValues: {
      question: "",
      answer: "",
    },
    onSubmit: async (values: any, { resetForm }) => {
      try {
        const faqPost = await createAFAQ({ ...values, id: survey.data.id });
        setFaq((FAQs) => [...FAQs, { ...values, id: faqPost.data.id }]);
        setAdd(false);
        resetForm();
      } catch (e: any) {
        new Error(e.message || "");
      }
    },
  });
  const modifyFAQForm = useFormik({
    initialValues: {
      question: "",
      answer: "",
      id: "",
    },
    onSubmit: async (values: any) => {
      try {
        const { question, answer } = values;
        await patchAFAQ(values.id, { question, answer });
        const _faqs = faqs.map((faq: any) => {
          if (faq.id === values.id) {
            return { ...values };
          }
          return faq;
        });
        setFaq(_faqs);
        setModify(false);
        modifyFAQForm.resetForm();
      } catch (e: any) {
        new Error(e.message || "");
      }
    },
  });

  const onDelete = async (id: string) => {
    try {
      await deleteAFAQ(id);
      setFaq((FAQs) => FAQs.filter((faq) => faq.id !== id));
    } catch (e: any) {
      new Error(e.message || "");
    }
  };
  React.useEffect(() => {
    getFAQs();
  }, []);

  const getFAQs = async () => {
    try {
      const data = await getAllFAQs({
        survey_index: survey.data.id,
      });
      setFaq(data);
    } catch (e: any) {
      new Error(e.message || "");
    }
  };

  const AddModal = () => {
    return (
      <Modal show={add} onHide={() => setAdd(false)} centered size="lg">
        <Form onSubmit={addFAQForm.handleSubmit}>
          <Modal.Header closeButton>
            <Modal.Title>
              <Text.H3>Add FAQs</Text.H3>
            </Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <Text.P1 className="mt-3 mb-2">Question</Text.P1>
            <FormControl.Text
              label="Question"
              name="question"
              onChange={addFAQForm.handleChange}
              placeholder="Question"
              value={addFAQForm.values.question}
              // formik={addFAQForm}
            />
            <Text.P1 className="mt-3 mb-2">Answer</Text.P1>
            <ReactQuill
              value={addFAQForm.values.answer}
              onChange={(e) => addFAQForm.setFieldValue("answer", e)}
            />
          </Modal.Body>
          <Modal.Footer>
            <Button variant="primary" type="submit">
              Add
            </Button>
          </Modal.Footer>
        </Form>
      </Modal>
    );
  };

  const ModifyModal = () => {
    return (
      <Modal show={modify} onHide={() => setModify(false)} centered size="lg">
        <Form onSubmit={modifyFAQForm.handleSubmit}>
          <Modal.Header closeButton>
            <Modal.Title>
              <Text.H3>Modify A FAQ</Text.H3>
            </Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <Text.P1 className="mt-3 mb-2">Question</Text.P1>
            <FormControl.Text
              label="Question"
              name="question"
              onChange={modifyFAQForm.handleChange}
              placeholder="Question"
              value={modifyFAQForm.values.question}
            />
            <Text.P1 className="mt-3 mb-2">Answer</Text.P1>
            <ReactQuill
              // value={data} onChange={(e: any) => setData(e)}
              value={modifyFAQForm.values.answer}
              onChange={(e) => modifyFAQForm.setFieldValue("answer", e)}
            />
          </Modal.Body>
          <Modal.Footer>
            <Button variant="primary" type="submit">
              Save
            </Button>
          </Modal.Footer>
        </Form>
      </Modal>
    );
  };

  const getNavItem = (title: string, key: string) => {
    return <UMTab
      eventKey={key}
      activeKey={activeTab}
      onClick={() => setActiveTab(key)}
    >
      {title}
    </UMTab>;
  };

  return (
    <Div>
      <CustomHeader
        metaItem={<Text.P1>{survey?.data?.updatedAt}</Text.P1>}
        title={
          <Div>
            <Text.H1 className="pb-2">{survey?.data?.name} - FAQ</Text.H1>
            <Div
              className="sticky-tabs-container"
              style={{ position: "absolute" }}
            >
              <Nav className="nav-tabs sticky">
                {getNavItem("FAQs", "all")}
                {getNavItem("Preview", "preview")}
              </Nav>
            </Div>
          </Div>
        }
        breadcrumbs={props.breadcrumbs}
        style={{ marginLeft: 310, height: 107 }}
      />
      <Layout.Container className="py-3 mb-3" fluid>
        <Div className="">
          <Div className="custom-tabs-2 pt-4">
            <Tab.Container activeKey={activeTab}>
              <Tab.Content>
                <Tab.Pane eventKey="all">
                  {faqs.map((fq, index) => (
                    <FAQBox className="my-3" key={index}>
                      <Layout.Row>
                        <Layout.Col>
                          <Div className="title">Question {index + 1}</Div>
                        </Layout.Col>
                        <Layout.Col className="text-right">
                          <DropBtn
                            className="text-black p-0"
                            title="..."
                            variant="outline-light"
                            id="dropdown-menu-align-responsive-1"
                          >
                            <Dropdown.Item
                              eventKey="1"
                              onClick={() => {
                                setModify(true);
                                modifyFAQForm.setValues({
                                  id: fq.id,
                                  question: fq.question,
                                  answer: fq.answer,
                                });
                              }}
                            >
                              Modify
                            </Dropdown.Item>
                            <Dropdown.Item
                              eventKey="2"
                              className="text-danger"
                              onClick={() => onDelete(fq.id)}
                            >
                              Delete
                            </Dropdown.Item>
                          </DropBtn>
                        </Layout.Col>
                      </Layout.Row>

                      <Text.P1 className="question my-2">{fq.question}</Text.P1>
                      <Div
                        className="question answer my-2"
                        dangerouslySetInnerHTML={{
                          __html: sanitizeHtml(fq.answer),
                        }}
                      />
                    </FAQBox>
                  ))}
                  <Button
                    variant="link"
                    size="sm"
                    className="font-weight-light py-3 px-5 mt-3"
                    onClick={() => setAdd(true)}
                    style={{
                      border: "2px dashed #518CFF",
                      background: "#F8FAFC",
                    }}
                  >
                    <Icon icon="far fa-plus-circle mr-2" />
                    Click to Add Question
                  </Button>
                </Tab.Pane>
                <Tab.Pane eventKey={"preview"}>
                  <PreviewFAQs faqs={faqs} />
                </Tab.Pane>
              </Tab.Content>
            </Tab.Container>
          </Div>

          {/* </PageContainer> */}
        </Div>
      </Layout.Container>
      {ModifyModal()}
      {AddModal()}
    </Div>
  );
};

export default FAQ;
const FAQBox = styled(Div)`
  border: 1px solid #f2f2f2;
  box-sizing: border-box;
  border-radius: 5px;
  padding: 20px;
  .title {
    font-size: 12px;
    line-height: 18px;
    color: #838383;
  }
  .question {
    background: #fcfcfc;
    border: 1px solid #f2f2f2;
    box-sizing: border-box;
    border-radius: 2px;
    padding: 8px;
  }
  .answer {
    h1 {
      font-size: 20px;
    }
    h2 {
      font-size: 20px;
    }
    h3 {
      font-size: 18px;
    }
    p {
      font-size: 0.875rem;
      margin: 0;
    }
  }
`;
const DropBtn = styled(DropdownButton)`
  button {
    border: none;
    padding: 0;
    color: #000;
  }
`;
