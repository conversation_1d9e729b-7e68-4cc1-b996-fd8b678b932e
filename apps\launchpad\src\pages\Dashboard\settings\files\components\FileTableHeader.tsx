import React from "react";
import { TableHead, TableHeader, TableRow } from "@repo/ui/components/table";
import { File } from "../filesService";

interface FileTableHeaderProps {
  handleSort: (field: keyof File | string) => void;
  getSortIcon: (field: keyof File | string) => React.ReactNode;
}

export const FileTableHeader: React.FC<FileTableHeaderProps> = ({
  handleSort,
  getSortIcon,
}) => {
  return (
    <TableHeader>
      <TableRow>
        <TableHead
          className="cursor-pointer"
          onClick={() => handleSort("title")}
        >
          <div className="flex items-center">
            Title
            {getSortIcon("title")}
          </div>
        </TableHead>
        <TableHead
          className="cursor-pointer"
          onClick={() => handleSort("successRecords")}
        >
          <div className="flex items-center">
            Success Records
            {getSortIcon("successRecords")}
          </div>
        </TableHead>
        <TableHead
          className="cursor-pointer"
          onClick={() => handleSort("rejectedRecords")}
        >
          <div className="flex items-center">
            Rejected Records
            {getSortIcon("rejectedRecords")}
          </div>
        </TableHead>
        <TableHead
          className="cursor-pointer"
          onClick={() => handleSort("totalRecords")}
        >
          <div className="flex items-center">
            Total Records
            {getSortIcon("totalRecords")}
          </div>
        </TableHead>
        <TableHead>
          <div className="flex items-center">
            Tags
          </div>
        </TableHead>
        <TableHead
          className="cursor-pointer"
          onClick={() => handleSort("uploadedOn")}
        >
          <div className="flex items-center">
            Uploaded On
            {getSortIcon("uploadedOn")}
          </div>
        </TableHead>
        <TableHead>
          <div className="flex items-center">
            Actions
          </div>
        </TableHead>
      </TableRow>
    </TableHeader>
  );
};
