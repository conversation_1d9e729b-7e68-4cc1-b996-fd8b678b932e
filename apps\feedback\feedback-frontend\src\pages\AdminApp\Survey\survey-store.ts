import { combineReducers } from "redux";
import util from "unmatched/utils";

// import { generateReducer } from "unmatched/utils/store";
import createSurveyReducer from "./CreateSurvey/create-survey-store";

const { generateReducer } = util.store;

const initialState = {};

const ACTIONS = {};

export default combineReducers({
  meta: generateReducer(ACTIONS, initialState),
  create: createSurveyReducer,
});
