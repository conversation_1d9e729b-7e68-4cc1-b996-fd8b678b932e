import React, { useState } from "react";
import Header from "../../Header";
import {
  PageContainer,
  Text,
  Layout,
  Div,
  ComboFilter,
  FormGroup,
  FormControl,
  Button,
  Icon,
  Modal,
} from "unmatched/components";
import {
  generateBindersReport,
} from "../../../reports-api";
import iconsSvgs from "assets/icons/icons";
import appUrls from "unmatched/utils/urls/app-urls";
import useToastr from "unmatched/modules/toastr/hook";
import { useLocalFilter } from "pages/CommonFilters/useLocalFilters";

const { Page } = iconsSvgs;

const Binders = (props: any) => {
  const { survey, layout, surveyId, onTabSelect } = props;

  const [selectedMetas, setSelectedMetas] = useState<any[]>([null]);
  const [nameStyle, setNameStyle] = useState("FIRSTNAME_LASTNAME");
  const [title, setTitle] = useState("");
  const toastr = useToastr();
  const [onSuccessModal, setOnSuccessModal] = useState(false);

  const filtersState = useLocalFilter(surveyId, undefined, 'report');

  React.useEffect(() => {
    filtersState.getFilters();
  }, []);

  const getAvailableMeta = (sM: string) => {
    return Object.keys(filtersState.filters)
      .filter((item: string) => !selectedMetas.includes(item) || item === sM)
      .map((meta: any) => {
        return { label: filtersState.filters[meta].label, key: meta };
      });
  };

  const selectMetaitem = (key: number, value: string) => {
    const arr: any = [...selectedMetas];
    arr[key] = value;
    setSelectedMetas(arr);
  };

  const getLastItemValue = () => {
    if (nameStyle === "FIRSTNAME_LASTNAME") {
      return "First Name, Last Name";
    }
    if (nameStyle === "LASTNAME_FIRSTNAME") {
      return "Last Name, First Name";
    }
  };

  const onGenerateReport = async () => {
    try {
      await generateBindersReport({
        index: surveyId,
        selectedMetas,
        filters: filtersState.selected,
        title,
        nameStyle,
      });
      toastr.onSucces({ title: "Success", content: "Generation Requested" });
      setOnSuccessModal(true);
    } catch (error) {
      toastr.onError(error);
    }
  };
  // Function to remove an item by its index
  const removeItem = (index: number) => {
    const updatedItems = [...selectedMetas];
    updatedItems.splice(index, 1);
    setSelectedMetas(updatedItems);
  };
  return (
    <PageContainer>
      <Header
        survey={survey}
        layout={layout}
        title="Binders"
        metaTemplate={null}
        breadcrumbs={[
          {
            label: "Reports",
            icon: <Page className="grey-icon__svg" />,
            route: appUrls.admin.reports.default,
          },
          { label: "Binders" },
          { label: survey.name },
        ]}
      />
      {/* tableMeta.selected.length && */}
      <Layout.Container className="pt-4" fluid>
        <Div className="pt-4">
          <Text.P1>Select Criteria</Text.P1>
          <Text.P2 className="text-muted">
            Set the criteria you need to generate a binder report.
          </Text.P2>
          <Div className="my-1" />
          <ComboFilter
            filters={filtersState.filters}
            selected={filtersState.selected}
            onFilterSelect={(_selected: any) => {
              filtersState.onSelect(_selected);
              //   setSelectedFilters(_selected);
              // getUsers(_selected, { search: tableMeta.search });
            }}
            onSubmit={() => ""}
          />
          <Div className="my-5" />
          <Text.P1>Set the Index Sequence</Text.P1>
          <Text.P2 className="text-muted">
            Choose how you want your binder report to be indexed.
          </Text.P2>
          {/* {JSON.stringify(filtersState.filters)}
          {JSON.stringify(selectedMetas)} */}
          <Div className="my-3" />
          {selectedMetas.map((sM: any, i) => (
            <Div className="row my-1" style={{ maxWidth: 480 }} key={i}>
              <Div className="col-7">
                <FormGroup>
                  {/* <FormGroup.Label>Select box</FormGroup.Label> */}
                  <FormControl.Select
                    value={filtersState?.filters[sM]?.label ?? ""}
                  >
                    {getAvailableMeta(sM).map((meta, in2) => (
                      <FormControl.SelectItem
                        key={in2}
                        value={meta}
                        onSelect={() => {
                          //   console.log(meta);
                          selectMetaitem(i, meta.key);
                        }}
                      >
                        <Text.P1>{meta.label}</Text.P1>
                      </FormControl.SelectItem>
                    ))}
                  </FormControl.Select>
                </FormGroup>
              </Div>
              <Div className="col-5 p-0">
                <Text.P1 className="text-muted py-1">
                  {selectedMetas.length - 1 === i &&
                    Object.keys(filtersState.filters).length - 1 > i && (
                      <Button
                        variant="link p-0 rounded-circle text-muted mr-2"
                        onClick={() =>
                          setSelectedMetas([...selectedMetas, null])
                        }
                      >
                        <Icon icon="far fa-plus-circle" />
                      </Button>
                    )}
                  {i !== 0 && (
                    <Button
                      variant="link text-danger rounded-circle p-0"
                      onClick={() => removeItem(i)}
                    >
                      <Icon icon="far fa-times-circle" />
                    </Button>
                  )}
                  {i === 0 && "Top Level"}
                </Text.P1>
              </Div>
            </Div>
          ))}

          <Div className="row my-1" style={{ maxWidth: 480 }}>
            <Div className="col-7">
              <FormGroup>
                {/* <FormGroup.Label>Select box</FormGroup.Label> */}
                <FormControl.Select value={getLastItemValue()}>
                  <FormControl.SelectItem
                    onSelect={() => {
                      //   console.log(meta);
                      setNameStyle("FIRSTNAME_LASTNAME");
                    }}
                  >
                    <Text.P1>First Name, Last Name</Text.P1>
                  </FormControl.SelectItem>
                  <FormControl.SelectItem
                    onSelect={() => {
                      //   console.log(meta);
                      setNameStyle("LASTNAME_FIRSTNAME");
                    }}
                  >
                    <Text.P1>Last Name, First Name</Text.P1>
                  </FormControl.SelectItem>
                </FormControl.Select>
              </FormGroup>
            </Div>
            <Div className="col-5 p-0">
              <Text.P1 className="text-muted py-1">Last Level</Text.P1>
            </Div>
          </Div>
          <Div className="my-5" />
          <Text.P1>Report Title</Text.P1>

          <Div className="my-1" style={{ maxWidth: 480 }}>
            <FormControl.Text
              type="text"
              onChange={(e: any) => setTitle(e.target.value)}
              placeholder=""
              value={title}
            />
          </Div>

          {/* {JSON.stringify(filtersState.filters)}
          {JSON.stringify(selectedMetas)} */}
          <Div className="my-3" />
        </Div>
        <Button onClick={onGenerateReport}>Generate</Button>
      </Layout.Container>
      <Modal show={onSuccessModal} backdrop="static" centered size="lg">
        <Modal.Header
          title="Download Reports"
          onHide={() => {
            // onHide();
            setOnSuccessModal(false);
          }}
        />
        <Modal.Body>
          <Div className="p-4">
            <Text.H3>Your download request is being processed.</Text.H3>
            <Text.P1>
              Reports are being processed and will be ready to download in
              ‘download requests’ tab. Please check back in sometime.
            </Text.P1>
          </Div>
        </Modal.Body>

        <Modal.Footer className="text-right my-4 border-none">
          <Button
            variant="primary"
            className="mr-2"
            onClick={() => {
              //   props.onHide();
              setOnSuccessModal(false);
              onTabSelect("DOWNLOADS", true);
            }}
          >
            Go to download requests
          </Button>
        </Modal.Footer>
      </Modal>
    </PageContainer>
  );
};

export default Binders;
