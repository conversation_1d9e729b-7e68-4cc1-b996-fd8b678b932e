import { useEffect, useState } from 'react';

export function useScrollAtBottom() {
  const [isScrollAtBottom, setIsScrollAtBottom] = useState(false);

  useEffect(() => {
    function handleScroll() {
      const { scrollTop, clientHeight, scrollHeight } = document.documentElement;

      // Check if the user has scrolled to the bottom of the page
      const reachedBottom = scrollTop + clientHeight >= scrollHeight;

      setIsScrollAtBottom(reachedBottom);
    }

    // Attach the event listener to the scroll event
    window.addEventListener('scroll', handleScroll);

    return () => {
      // Clean up by removing the event listener when the component unmounts
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  return isScrollAtBottom;
}