import React, { useEffect } from "react";
import {
  Div,
  Layout,
  Text,
  Button,
  Icon,
  HeatMapTable,
  Table,
  // ComboFilter,
  OverlayTrigger,
  Tooltip,
  Dropdown,
} from "unmatched/components";
// import CompareChart from "../../LineChart/LineChart";
// import DownloadExcel from "../../DownloadExcel";
// import Legend from "../../Legend";
// import LegendFor from "../../LegendFor";
// import GRAPH_DATA from "../../LineChart/graph-meta";
import ItemList from "../../ItemList";
import useFilter from "pages/CommonFilters/hook";
import useAnalyticsFilters from "../../Shared/AnalyticsFilters/hook";
import { useXHR } from "unmatched/hooks";
import useToastr from "unmatched/modules/toastr/hook";
import util from "unmatched/utils";
import AnalyticsFilters from "../../Shared/AnalyticsFilters/AnalyticsFilters";
import {
  getAllSectionAndItemsFact,
  getItemizedGraphDataFact,
  getItemizedHeatMapFact,
} from "../aggregate-api";
import SideSingleSelect from "./SideSingleSelect";
import SideMultiSelect from "./SideMultiSelect";
// import TimeSeriesGraph from "../../TimeSeriesGraph/TimeSeriesGraph";
import TimeSeriesGraphNew from "../../TimeSeriesGraph/TimeSeriesGraphNew";
import { cloneDeep } from "lodash";
// import PropTypes from 'prop-types';
// import styled from "styled-components";

// const COHARTS = [
//   { key: "department", title: "Department" },
//   { key: "location", title: "Location" },
// ];

// const FixedContent = styled.div`
//   position: fixed;
//   background: #fff;
//   z-index: 1;
//   width: 100%;
//   left: 60px;
//   padding-right: 106px;
//   box-shadow: ${(props: any) =>
//     props.scrollPos > 1 ? "0px 10px 10px rgb(0 0 0 / 10%)" : "none"};
//   border-bottom: 1px solid #dee2e6;
// `;

const TYPES = {
  "360": "surveyindex360",
  upward: "surveyindexupward",
  self: "surveyindexself",
  "360Group": "surveyindex360group",
};

const defaultSelected = { id: "", key: "", title: "", values: [] };

const getFilterOptions = (_filters: any) => {
  const filters = _filters || {};
  return util.lib.entries(filters).map((item: any) => {
    const [key, value] = item;
    return {
      id: key,
      key,
      title: value.label,
      values: value.values,
    };
  });
};

const FilterLayout = (props: any) => {
  const { selected, onSelect, filters, btnItem } = props;

  return (
    <Div className="pt-3">
      <Layout.Row>
        <Layout.Col xl={3} lg={4} md={6} className="pt-2">
          <Text.H3>{props.title}</Text.H3>
        </Layout.Col>
        <Layout.Col xl={3} md={4}>
          <Table.Filter
            title="Select"
            selected={selected.key}
            selectedLabel={selected.title}
            options={filters}
            onSelect={onSelect}
          />
        </Layout.Col>
        <Layout.Col xl={6} md={4} className="pt-1 d-flex justify-content-end">
          {btnItem}
        </Layout.Col>
      </Layout.Row>
      <Layout.Row className="pt-4 pb-3">
        <Layout.Col>{props.children}</Layout.Col>
        {/* {getFilterLayout()} */}
        <Layout.Col xl={2} lg={2} className="text-right">
          <Button
            onClcik={() => (props.onDownload ? props.onDownload() : "")}
            className="ml-auto"
            variant="outline-primary"
          >
            Download XLSX <Icon icon="fas fa-file-download" />
          </Button>
        </Layout.Col>
      </Layout.Row>
    </Div>
  );
};

const Item = () => {
  // const { user } = props;

  // const rows = [
  //   {
  //     id: 1,
  //     label: "Item 1",
  //     firmAverage: 2,
  //     departmentAverage: 1.5,
  //     practiceGroupAverage: 4.5,
  //     titleAverage: 2,
  //     selfAverage: 1.5,
  //     year: 2019,
  //     individualAverage: 4.5,
  //   },
  //   {
  //     id: 2,
  //     label: "Item 1",
  //     firmAverage: 2,
  //     departmentAverage: 1.5,
  //     practiceGroupAverage: 4.5,
  //     titleAverage: 2,
  //     selfAverage: 1.5,
  //     year: 2019,
  //     individualAverage: 4.5,
  //   },
  //   {
  //     id: 3,
  //     label: "Item 1",
  //     firmAverage: 2,
  //     departmentAverage: 1.5,
  //     practiceGroupAverage: 4.5,
  //     titleAverage: 2,
  //     selfAverage: 1.5,
  //     year: 2019,
  //     individualAverage: 4.5,
  //   },
  //   {
  //     id: 4,
  //     label: "Item 1",
  //     firmAverage: 2,
  //     departmentAverage: 1.5,
  //     practiceGroupAverage: 4.5,
  //     titleAverage: 2,
  //     selfAverage: 1.5,
  //     year: 2019,
  //     individualAverage: 4.5,
  //   },
  // ];
  // const columns = [
  //   { id: 1, label: "" },
  //   { id: 2, label: "Individual Item Average" },
  //   { id: 3, label: "Firm Item Average" },
  //   { id: 4, label: "Department1 Category Average" },
  //   { id: 5, label: "Department2 Category Average" },
  //   { id: 6, label: "Department3 Category Average" },
  //   { id: 7, label: "Department4 Category Average" },
  // ];

  const [showItems, toggleItems] = React.useState(false);

  // const [cohart] = React.useState({
  //   cohart: {
  //     options: COHARTS,
  //     selected: "department",
  //   },
  //   applied: {
  //     options: [],
  //     selected: "Option1",
  //   },
  // });

  const [showSingleSelect, setShowSingleSelect] = React.useState(false);
  const [activeSingleQuestion, setActiveSingleQuestion] = React.useState({
    id: "",
    name: "",
  });
  const [showDropdown, setShowDropdown] = React.useState(false);

  // const [positionY, setPositionY] = React.useState(0);

  // const getScrollPosition = () => {
  //   setPositionY(window.scrollY);
  // };

  // React.useEffect(() => {
  //   window.addEventListener("scroll", getScrollPosition);
  //   return () => {
  //     window.removeEventListener("scroll", getScrollPosition);
  //   };
  // }, []);

  const [singleItems, setSingleItems] = React.useState<any>([]);
  const [upwardItems, setUpwardItems] = React.useState<any>([]);
  const [selfItems, setSelfItems] = React.useState<any>([]);
  const [showMultiSelect, setShowMultiSelect] = React.useState({
    upward: false,
    self: false,
  });
  const [selectedItems, setSelelectedItems] = React.useState({
    upward: [],
    self: [],
  });

  // const [item] = React.useState({
  //   selected: "",
  //   placeholder: "Item 1",
  //   options: [{ title: "Item 1", id: 1 }],
  // });

  // const [compareWith] = React.useState({
  //   selected: "Department1 Average",
  //   placeholder: "Compare group",
  //   options: [{ title: "Department1 Average", id: 1 }],
  // });

  // const filtersState = useFilter();

  React.useEffect(() => {
    filtersState.getFilters();
  }, []);

  const filters = useFilter();

  let filterOptions = getFilterOptions(filters.filters);

  const [columns, setColumns]: any = React.useState({
    upward: [],
    self: [],
    ["360"]: [],
  });

  const [items, setItems]: any = React.useState({
    upward: [],
    self: [],
    // ["360"]: [],
  });

  const [selected, setSelected]: any = React.useState({
    upward: defaultSelected,
    self: defaultSelected,
    ["360"]: defaultSelected,
  });

  // const [resources, setResources] = React.useState({
  //   type: "360",
  //   options: ["Overall"],
  //   legends: [],
  //   selected: [],
  // });

  const graph = useXHR({
    defaultResponse: {
      data: [],
      labels: [],
    },
  });

  const filtersState = useFilter();

  const singleFilters = useAnalyticsFilters();
  const upwardFilters = useAnalyticsFilters();
  const selfFilters = useAnalyticsFilters();

  const selections: any = {
    upward: upwardFilters,
    self: selfFilters,
    single: singleFilters,
  };

  const toastr = useToastr();

  // Graph Function calls

  // const onGraphLoad = (year?: any) => {
  //   selections["graph"]
  //     .getFilters({
  //       year,
  //       // resource_types: TYPES["360"],
  //     })
  //     .then(
  //       (_filters: any) => {
  //         const filteredSurveys = _filters.surveys.filter(
  //           (item: any) => item.type !== TYPES["self"]
  //         );
  //         const [selectedSurvey] =
  //           filteredSurveys.filter((item: any) => item.categories.length) || [];
  //         if (!selectedSurvey) {
  //           toastr.warningToast("No Surveys Found");
  //           // selections["graph"].setFilters(_filters);
  //           return;
  //         }
  //         const [selectedCategory] = selectedSurvey?.categories || [];
  //         if (!selectedCategory) {
  //           toastr.warningToast("No Categories Found");
  //           // selections["graph"].setFilters(_filters);
  //           return;
  //         }
  //         getGraphData(
  //           {},
  //           {
  //             ..._filters,
  //             surveys: filteredSurveys,
  //             // surveys: _filters.surveys.filter(
  //             //   (item: any) => selectedSurvey.type === item.type
  //             // ),
  //             categories: selectedSurvey?.categories.map((item: any) => ({
  //               id: item.id,
  //               key: item.key,
  //               title: item.title || item.name,
  //             })),
  //             selected: {
  //               ..._filters.selected,
  //               surveys:  selectedSurvey && selectedSurvey.id ? [selectedSurvey.id] : [],
  //               types: selectedSurvey ? [selectedSurvey.type] : [],
  //               groups: [],
  //               categories: selectedCategory ? [selectedCategory.id] : [],
  //             },
  //           }
  //         );
  //       },
  //       (err: any) => {
  //         // console.log(err);
  //         toastr.onError({
  //           ...err,
  //           msg: JSON.stringify(err.msg),
  //         });
  //       }
  //     );
  // };

  // const onResourceChange = (data: any) => {
  //   getGraphData(
  //     {
  //       rater_groups: ["Overall"],
  //     },
  //     data
  //   ).then(() => {
  //     setResources((_data: any) => {
  //       return {
  //         ..._data,
  //         selected: ["Overall"],
  //       };
  //     });
  //   });
  // };

  // const onGroupChange = (data: any) => {
  //   getGraphData({
  //     rater_groups: data,
  //   }).then(() => {
  //     setResources({
  //       ...resources,
  //       selected: data,
  //     });
  //   });
  // };

  // const getGraphData = (filters?: any, _selections?: any) => {
  //   graph.setLoading(true);
  //   const payload = {
  //     ...(filters || {}),
  //     people: [...(filters?.people || [])],
  //     rater_groups: _selections.selected.groups || [],
  //   };
  //   return;
  //   // return getCategoryGraphDataFact(
  //   //   {
  //   //     // userId: user.id,
  //   //     categoryId: _selections.selected.categories[0],
  //   //     type: _selections.selected.types[0],
  //   //   },
  //   //   payload
  //   // ).then(
  //   //   (response: any) => {
  //   //     selections["graph"].setFilters(_selections);
  //   //     graph.onSuccess(response);
  //   //     return true;
  //   //   },
  //   //   () => {
  //   //     graph.setLoading(false);
  //   //   }
  //   // );
  // };

  // const onFilterSelect = (data: any) => {
  //   getGraphData(
  //     {
  //       ...data,
  //       people: data.people
  //         ? data.people.map((item: any) => util.getContentFromBrackets(item))
  //         : [],
  //     },
  //     selections["graph"]
  //   );
  // };

  // Heatmap Function calls

  const getDynamicColumns = (type: any, meta?: any) => {
    const options =
      filterOptions.find((item: any) => item.key === meta)?.values || [];
    return options.map((item: any) => {
      return {
        id: item,
        key: item,
        label: `${item} Average`,
      };
    });
  };

  const getColumnsData = (type: any) => {
    const output = [
      { id: 1, label: "Item" },
      { id: 4, label: "Firm Average" },
      ...columns[type],
    ];
    return output;
  };

  const getSelectedSurveyIndexs = (_selections?: any) => {
    if (_selections) {
      if (_selections.selected.groups && _selections.selected.groups.length) {
        return _selections.selected.groups;
      } else if (
        _selections?.selected.surveys &&
        _selections?.selected.surveys.length
      ) {
        return _selections?.selected.surveys;
      }
    }
    return [];
  };

  const getHeatMapData = (type: any, _selections?: any, meta?: any) => {
    const items = (is: any[]) => {
      if (is.length > 0) {
        return is;
      }
      return undefined;
    };
    getItemizedHeatMapFact({
      surveys: getSelectedSurveyIndexs(_selections),
      years: _selections?.selected.years || [],
      types: _selections?.selected.types || [],
      questions:
        type === "upward"
          ? items(selectedItems.upward)
          : items(selectedItems.self),
      meta,
    }).then(
      (response: any) => {
        selections[type].setFilters(_selections);
        setItems((_items: any) => {
          return {
            ..._items,
            [type]: response.data,
          };
        });
        setColumns((_columns: any) => {
          return {
            ..._columns,
            [type]: getDynamicColumns(type, meta),
          };
        });
      },
      (err: any) => {
        toastr.onError({
          ...err,
          msg: JSON.stringify(err.msg),
        });
      }
    );
  };

  const onTableLoad = (type: string, src: string, year?: any, meta?: any) => {
    selections[type]
      .getFilters({
        year,
        resource_types:
          type === "self"
            ? TYPES["self"]
            : `${TYPES["upward"]},${TYPES["360"]},${TYPES["360Group"]}`,
      })
      .then(
        (_filters: any) => {
          const [selectedSurvey] = _filters.surveys || [];
          if (!selectedSurvey) {
            // toastr.warningToast(`No ${type} Surveys Found`);
            return;
          }
          let groups = [];
          if (
            [TYPES["360"], TYPES["360Group"]].includes(selectedSurvey?.type)
          ) {
            groups =
              selectedSurvey &&
              selectedSurvey["rater_groups"].map((item: any) => {
                return {
                  id: item.id,
                  key: item.key,
                  title: item.title,
                  type: item.resource_type,
                  groupId: selectedSurvey?.id,
                };
              });
          }
          getHeatMapData(
            type,
            {
              ..._filters,
              surveys: _filters.surveys,
              groups,
              selected: {
                ..._filters.selected,
                surveys:  selectedSurvey && selectedSurvey.id ? [selectedSurvey.id] : [],
                types: selectedSurvey ? [selectedSurvey.type] : [],
                groups: [],
              },
            },
            meta || selected[type].key
          );
          getQuestionsData(selectedSurvey.id, selectedSurvey.type, src);
        },
        (err: any) => {
          // console.log(err);
          toastr.onError({
            ...err,
            msg: JSON.stringify(err.msg),
          });
        }
      );
  };

  const getRows = (type: string) => {
    const list = items[type] || [];
    return list;
  };

  const getAllValues = (type: string) => {
    const values: any = [];
    items[type].forEach((item: any) => {
      const keys = [
        "firmAverage",
        ...(selected[type]?.values || []),
        // "revieweeAverage",
        // "departmentAverage",
        // "practiceGroupAverage",
        // "titleAverage",
        // "selfAverage",
      ];
      keys.forEach((key: string) => {
        if (item[key]) {
          values.push(item[key]);
        }
      });
      // console.log(item);
    });
    // console.log(values);
    return values;
  };

  const getHeatMapDataTemplate = (item: any, type: string) => {
    const { Data } = HeatMapTable;
    const values = getAllValues(type);
    return (
      <>
        <Data className="text-dark bg-light ">
          {/* <Div className="text-left">{item.name}</Div> */}
          <OverlayTrigger
            key="bottom"
            placement="bottom"
            overlay={
              <Tooltip id="`tooltip-bottom" className="fs-10">
                {item.name}
              </Tooltip>
            }
          >
            <Div className="text-left text-truncate" style={{ maxWidth: 300 }}>
              {item.name}
            </Div>
          </OverlayTrigger>
        </Data>
        {/* <Data values={values} value={item.revieweeAverage}>
          {item.revieweeAverage}
        </Data> */}
        <Data values={values} value={item.firmAverage}>
          {item.firmAverage}
        </Data>
        {columns[type].map((col: any) => {
          return (
            <Data values={values} value={item[col.key]}>
              {item[col.key] || "0"}
            </Data>
          );
        })}
        {/* <Data values={values} value={item.departmentAverage}>
          {item.departmentAverage}
        </Data> */}
        {/* <Data values={values} value={item.practiceGroupAverage}>
          {item.practiceGroupAverage}
        </Data> */}
        {/* <Data values={values} value={item.titleAverage}>
          {item.titleAverage}
        </Data> */}
        {/* <Data values={values} value={item.selfAverage}>
          {item.selfAverage}
        </Data> */}
      </>
    );
  };

  const onGraphLoad = (year?: any) => {
    selections["single"]
      .getFilters({
        year,
        // resource_types: TYPES["360"],
      })
      .then(
        (_filters: any) => {
          const filteredSurveys = _filters.surveys.filter(
            (item: any) => item.type !== TYPES["self"]
          );
          const [selectedSurvey] =
            filteredSurveys.filter((item: any) => item.categories.length) || [];
          if (!selectedSurvey) {
            toastr.warningToast("No Surveys Found");
            // selections["graph"].setFilters(_filters);
            return;
          }
          getQuestionsData(selectedSurvey.id, selectedSurvey.type, "single", {
            ..._filters,
            surveys: filteredSurveys,
            selected: {
              ..._filters.selected,
              surveys:  selectedSurvey && selectedSurvey.id ? [selectedSurvey.id] : [],
              types: selectedSurvey ? [selectedSurvey.type] : [],
              groups: [],
            },
          });
        },
        (err: any) => {
          // console.log(err);
          toastr.onError({
            ...err,
            msg: "Something went wrong.",
          });
        }
      );
  };

  const [selectedTimeGraphCompareList, setSelectedTimeGraphCompareList] =
    React.useState<any>({});

  useEffect(() => {
    getGraphData();
  }, [selectedTimeGraphCompareList]);

  const onFilterSelectNew = (data: any, groupID?: any) => {
    const computedData = Object.entries(data).reduce((acc, [k, v]) => {
      if ((v as any).length) {
        (acc as any)[k] = v;
      }
      return acc;
    }, {});

    setSelectedTimeGraphCompareList((s: any) => {
      let newList = { ...s, [groupID]: computedData };
      newList = Object.entries(newList).reduce((acc, [k, v]) => {
        if (Object.keys(v as any).length) {
          (acc as any)[k] = v;
        }
        return acc;
      }, {});
      return newList;
    });
  };

  const onFilterRemove = (id: string) => {
    const clone = cloneDeep(selectedTimeGraphCompareList);
    delete clone[id];
    setSelectedTimeGraphCompareList(clone);
  };

  const getGraphData = async (filters?: any, _selections?: any) => {
    graph.setLoading(true);
    const raterGroups = _selections?.selected?.groups || [];
    const payload = {
      ...(filters || {}),
      compare_list: Object.values(selectedTimeGraphCompareList),
      // people: [...(filters?.people || []), user.empId],
      // rater_groups: _selections?.selected?.groups || [],
    };
    if (raterGroups.length) {
      payload["rater_groups"] = raterGroups;
    }
    return getItemizedGraphDataFact(
      _selections?.selected?.types[0],
      _selections?.id ?? activeSingleQuestion.id,
      payload
    ).then(
      (response: any) => {
        if (!util.lib.keys(payload).length) {
          filtersState.onSelect(response.defaultFilters);
        }
        selections["single"].setFilters(_selections);
        graph.onSuccess(response);
        return true;
      },
      () => {
        // debugger;
        graph.setLoading(false);
      }
    );
  };

  // const onFilterSelect = (data: any) => {
  //   getGraphData(
  //     {
  //       ...data,
  //       people: data.people
  //         ? data.people.map((item: any) => util.getContentFromBrackets(item))
  //         : [],
  //     },
  //     selections["single"]
  //   );
  // };

  React.useEffect(() => {
    filters.getFilters((_filters: any) => {
      filterOptions = getFilterOptions(_filters);
      const [item] = filterOptions;
      if (item) {
        setSelected({
          ...selected,
          upward: item,
          self: item,
        });
        console.log(item);
        onTableLoad("upward", "upward", undefined, item.key);
        onTableLoad("self", "self", undefined, item.key);
        onGraphLoad();
      }
    });
  }, []);

  const getQuestionsData = (
    surveyId: string,
    type: string,
    src: string,
    _filters?: any
  ) => {
    // try {
    // const data = await
    getAllSectionAndItemsFact(surveyId, type).then((res: any) => {
      if (src === "single") {
        setSingleItems(res.results);
        setActiveSingleQuestion({
          id: res?.results[0]?.id,
          name: res?.results[0]?.label,
        });
        getGraphData(
          {},
          {
            ..._filters,
            id: res?.results[0]?.id,
          }
        );
      } else if (src === "self") {
        setSelfItems(res.results);
      } else if (src === "upward") {
        setUpwardItems(res.results);
      }
    });
  };

  function Navigation(): {
    isNext: boolean;
    isPrev: boolean;
    onNext: Function;
    onPrev: Function;
  } {
    const currentIndex = singleItems.findIndex((item: any) => {
      return item.id === activeSingleQuestion.id;
    });

    const isPrev = singleItems.length > 0 && currentIndex > 0;
    const isNext = singleItems.length > currentIndex + 1;
    const items = selections["single"];
    const onNext = () => {
      if (!isNext) return;
      setActiveSingleQuestion({
        id: singleItems[currentIndex + 1].id,
        name: singleItems[currentIndex + 1].label,
      });
      getGraphData(
        {},
        {
          ...items,
          id: singleItems[currentIndex + 1].id,
        }
      );
    };
    const onPrev = () => {
      if (!isPrev) return;
      setActiveSingleQuestion({
        id: singleItems[currentIndex - 1].id,
        name: singleItems[currentIndex - 1].label,
      });
      getGraphData(
        {},
        {
          ...items,
          id: singleItems[currentIndex - 1].id,
        }
      );
    };

    return {
      isNext: isNext,
      isPrev: isPrev,
      onNext,
      onPrev,
    };
  }

  const getFiltersTemplate = () => {
    return (
      <Div className="d-flex justify-content-between flex-column py-3">
        <Div className="row text-left m-0" style={{ maxWidth: 360 }}>
          <Div className="col p-0" style={{ maxWidth: 100 }}>
            <Text.P2 className="my-2">Choose Item</Text.P2>
          </Div>
          <Div style={{ width: 175 }} className="col p-0">
            <Button
              variant="outline-primary"
              onClick={() => setShowSingleSelect(true)}
            >
              <Icon icon="far fa-list mr-2" />
              Show all items
            </Button>
          </Div>
        </Div>
        <Div
          className="border rounded-sm my-1 row mx-0"
          style={{ background: "#FCEED0", width: 900 }}
        >
          <Div className="pr-0 col" style={{ maxWidth: "calc(100% - 177px)" }}>
            <Div className="d-flex m-0 position-relative">
              <Div
                className="p-0"
                style={{
                  maxWidth: "calc(100% - 30px)",
                  minWidth: "calc(100% - 30px)",
                }}
              >
                <Text.P1
                  className="text-truncate flex-grow-1 fs-14 font-weight-bold text-left"
                  onClick={() => setShowDropdown(!showDropdown)}
                  style={{ marginTop: "0.7rem" }}
                >
                  {activeSingleQuestion.name}
                </Text.P1>
              </Div>
              <Div className="p-0" style={{ maxWidth: 30 }}>
                <Button
                  onClick={() => setShowDropdown(!showDropdown)}
                  variant="link"
                  className="pb-0 px-0 border-0 rounded-0"
                  style={{ marginTop: "0.3rem" }}
                >
                  <Icon icon="far fa-chevron-down" />
                </Button>
              </Div>
              <Dropdown style={{ position: "static" }} show={showDropdown}>
                <Dropdown.Toggle
                  variant="link"
                  className="px-0 py-0 mt-4"
                  id="dropdown-basic"
                  style={{ opacity: 0 }}
                ></Dropdown.Toggle>

                <Dropdown.Menu className="shadow w-100 mt-2 py-0" align="right">
                  {singleItems.map((item: any, i: number) => (
                    <Dropdown.Item
                      key={i}
                      className={`py-2 fs-12 border-bottom d-block`}
                      as={Button}
                      onClick={() => {
                        setActiveSingleQuestion({
                          id: item.id,
                          name: item.label,
                        });
                        // const items = selections["single"];
                        // getGraphData(
                        //   {},
                        //   {
                        //     ...items,
                        //     id: item.id,
                        //   }
                        // );
                        const items = selections["single"];
                        getGraphData(
                          {},
                          {
                            ...items,
                            id: item.id,
                          }
                        );
                        setShowDropdown(false);
                      }}
                    >
                      {item.label}
                    </Dropdown.Item>
                  ))}
                </Dropdown.Menu>
              </Dropdown>
            </Div>
          </Div>
          <Div
            style={{ maxWidth: 177, minWidth: 177 }}
            className="text-right px-0 col"
          >
            <Button
              variant="outline-primary"
              className="font-weight-normal fs-12 rounded-0 border-left border-top-0 border-bottom-0 border-right-0"
              disabled={!Navigation().isPrev}
              style={{ padding: ".7rem 1rem" }}
              onClick={() => Navigation().onPrev()}
            >
              <Icon icon="far fa-chevron-left mr-2" /> Previous
            </Button>
            <Button
              variant="outline-primary"
              className="font-weight-normal fs-12 rounded-0 border-left border-top-0 border-bottom-0 border-right-0"
              disabled={!Navigation().isNext}
              style={{ padding: ".7rem 1rem" }}
              onClick={() => Navigation().onNext()}
            >
              Next <Icon icon="far fa-chevron-right ml-2" />
            </Button>
          </Div>
        </Div>
      </Div>
    );
  };

  // const getFiltersTemplate = () => {
  //   return (
  //     <Layout.Row>
  //       <Layout.Col xl={9}>
  //         <Layout.Row className="py-3 mx-0">
  //           <Layout.Col className="px-0" xl={12}>
  //             {/* <ComboFilter
  //               cohart={cohart.cohart}
  //               applied={cohart.applied}
  //               onCohartUpdate={() => ""}
  //               onAppliedUpdate={() => ""}
  //               isAppliedShown={true}
  //             /> */}
  //             <ComboFilter
  //               filters={filtersState.filters}
  //               selected={filtersState.selected}
  //               onFilterSelect={(_selected: any) => {
  //                 filtersState.onSelect(_selected);
  //               }}
  //               onSubmit={() => ""}
  //             />
  //           </Layout.Col>
  //         </Layout.Row>
  //         <Layout.Row>
  //           <Layout.Col xl={2}>
  //             <Table.CompareFilter
  //               title="Item"
  //               selected={item.selected}
  //               options={[]}
  //               placeholder={item.placeholder}
  //               onSelect={() => ""}
  //             />
  //           </Layout.Col>
  //           <Layout.Col xl={4}>
  //             <Table.CompareFilter
  //               title="Compare with"
  //               selected={compareWith.selected}
  //               options={[]}
  //               placeholder={compareWith.placeholder}
  //               onSelect={() => ""}
  //             />
  //           </Layout.Col>
  //         </Layout.Row>
  //       </Layout.Col>
  //       <Layout.Col className="text-right align-self-center">
  //         <Button
  //           variant="outline-primary"
  //           onClick={() => toggleItems(!showItems)}
  //         >
  //           <Icon icon="far fa-list" /> Show all items
  //         </Button>
  //       </Layout.Col>
  //     </Layout.Row>
  //   );
  // };

  return (
    <Layout.Container fluid className="pt-3">
      {showSingleSelect && (
        <Layout.Sidebar
          className="bg-light border-right"
          right
          // hasHeader
          style={{ zIndex: 1050 }}
          width={400}
        >
          <SideSingleSelect
            onClose={() => setShowSingleSelect(false)}
            sections={singleItems}
            selected={activeSingleQuestion.id}
            setSelected={(item: any) => {
              setActiveSingleQuestion(item);
              const items = selections["single"];
              getGraphData(
                {},
                {
                  ...items,
                  id: item.id,
                }
              );
            }}
            filteredComponent={() => (
              <AnalyticsFilters
                filters={selections["single"]}
                hideTypes
                config={{
                  surveys: {
                    multiple: false,
                  },
                  groups: {
                    multiple: false,
                  },
                  layout: {
                    year: {
                      sm: 5,
                    },
                    survey: {
                      sm: 7,
                    },
                    group: {
                      sm: 12,
                      className: "mt-3",
                    },
                  },
                }}
                onYearChange={(item: any) => {
                  // onTableLoad("single", "single", );
                  onGraphLoad(item.key);
                }}
                onSurveyChange={(items: any) => {
                  const item = selections["single"];
                  const [surveyId] = items;
                  const selected = item.surveys.find((s: any) => {
                    return items.includes(s.id);
                  });
                  const groups = selected["rater_groups"]?.map((item: any) => {
                    // debugger;
                    return {
                      id: item.id,
                      key: item.key,
                      title: item.title,
                      type: item.resource_type,
                      groupId: surveyId,
                      categories: item.categories || [],
                    };
                  });
                  getQuestionsData(selected.id, selected.type, "single", {
                    ...item,
                    groups,
                    selected: {
                      ...item.selected,
                      surveys: items,
                      groups: [],
                      types: [selected.type],
                    },
                  });
                }}
                onGroupChange={(items: any) => {
                  const item = selections["single"];
                  // const filteredGroups =
                  //   item.groups.filter((item: any) =>
                  //     items.includes(item.id)
                  //   ) || [];
                  getQuestionsData(
                    item.selected.surveys[0],
                    item.selected.types[0],
                    "single",
                    {
                      ...item,
                      // groups,
                      selected: {
                        ...item.selected,
                        groups: items,
                        types: [TYPES["360Group"]],
                      },
                    }
                  );
                }}
              />
            )}
          />
        </Layout.Sidebar>
      )}
      {showMultiSelect.upward && (
        <Layout.Sidebar
          className="bg-light border-right"
          right
          // hasHeader
          style={{ zIndex: 1050 }}
          width={400}
        >
          <SideMultiSelect
            onClose={() => {
              // const item = selections["upward"];
              setShowMultiSelect({
                upward: false,
                self: false,
              });
              getHeatMapData(
                "upward",
                selections["upward"],
                selected.upward.id
              );
            }}
            sections={upwardItems}
            selected={selectedItems.upward}
            setSelected={setSelelectedItems}
            type="upward"
          />
        </Layout.Sidebar>
      )}
      {showMultiSelect.self && (
        <Layout.Sidebar
          className="bg-light border-right"
          right
          // hasHeader
          style={{ zIndex: 1050 }}
          width={400}
        >
          <SideMultiSelect
            onClose={() => {
              // const item = selections["self"];
              setShowMultiSelect({
                upward: false,
                self: false,
              });
              getHeatMapData("self", selections["self"], selected.self.id);
            }}
            sections={selfItems}
            selected={selectedItems.self}
            setSelected={setSelelectedItems}
            type="self"
          />
        </Layout.Sidebar>
      )}
      {showItems && (
        <Layout.Sidebar
          className="bg-light border-right"
          right
          hasHeader
          style={{ zIndex: 1050 }}
          width={400}
        >
          <ItemList onClose={() => toggleItems(false)} />
        </Layout.Sidebar>
      )}
      {showItems && (
        <Layout.Sidebar
          className="bg-light border-right"
          right
          hasHeader
          style={{ zIndex: 1050 }}
          width={400}
        >
          <ItemList onClose={() => toggleItems(false)} />
        </Layout.Sidebar>
      )}
      {/* <FixedContent scrollPos={positionY} className="text-right">
        <Div style={{ marginLeft: 47 }}></Div>
      </FixedContent> */}
      {getFiltersTemplate()}
      <Div>
        {/* <Text.H3>Time Series Graph</Text.H3> */}
        {/* <Layout.Row className="pt-4">
          <Layout.Col xl={9} className=""> */}
        <TimeSeriesGraphNew
          // filtersState={filtersState}
          onFilterSelect={onFilterSelectNew}
          onFilterRemove={onFilterRemove}
          userName={""}
          data={graph.data.data}
          labels={graph.data.labels}
          isLoading={graph.isLoading}
          disallowedFilters={['people']}
        />
        {/* <CompareChart data={GRAPH_DATA.data} labels={GRAPH_DATA.labels} />
            <LegendFor legends={[]} /> */}
        {/* </Layout.Col>
          <Layout.Col>
            <Legend />
          </Layout.Col>
        </Layout.Row> */}
      </Div>
      {getRows("upward").length > 0 && <Div>
        <Div>
          {/* {JSON.stringify(selections)} */}
          <FilterLayout
            title="Time Series Table Heat Map - Upward/360"
            selected={selected["upward"]}
            filters={filterOptions}
            onSelect={(item: any) => {
              setSelected({
                ...selected,
                upward: item,
              });
              getHeatMapData("upward", selections["upward"], item.key);
            }}
            btnItem={
              <Button
                variant="outline-primary"
                onClick={() =>
                  setShowMultiSelect({ upward: true, self: false })
                }
              >
                <Icon icon="far fa-list mr-2" />
                Show all items
              </Button>
            }
          >
            <AnalyticsFilters
              filters={selections["upward"]}
              hideTypes
              config={{
                surveys: {
                  multiple: false,
                },
                groups: {
                  multiple: false,
                },
                layout: {
                  year: {
                    xl: 3,
                    sm: 6,
                  },
                  survey: {
                    xl: 3,
                    sm: 6,
                  },
                  group: {
                    xl: 3,
                    sm: 12,
                  },
                },
              }}
              onYearChange={(item: any) => {

                onTableLoad("upward","", item.key, selected.upward.id);
              }}
              onSurveyChange={(items: any) => {
                const item = selections["upward"];
                const [surveyId] = items;
                const slctd = item.surveys.find((s: any) => {
                  return items.includes(s.id);
                });
                const groups = slctd["rater_groups"]?.map((item: any) => {
                  return {
                    id: item.id,
                    key: item.key,
                    title: item.title,
                    type: item.resource_type,
                    groupId: surveyId,
                  };
                });
                // console.log(selections["upward"]);
                getHeatMapData(
                  "upward",
                  {
                    ...item,
                    groups,
                    selected: {
                      ...item.selected,
                      surveys: items,
                      groups: [],
                      types: [slctd.type],
                    },
                  },
                  // ""
                  // selected.upward.id
                  selected["upward"].key
                );
              }}
              onGroupChange={(items: any) => {
                const item = selections["upward"];

                getHeatMapData(
                  "upward",
                  {
                    ...item,
                    // groups,
                    selected: {
                      ...item.selected,
                      // surveys: items,
                      groups: items,
                      types: [TYPES["360"]],
                    },
                  },
                  selected.upward.id
                );
              }}
            />
          </FilterLayout>
        </Div>
        <Div>
          <HeatMapTable
            columns={getColumnsData("upward")}
            values={getAllValues("upward")}
            rows={getRows("upward")}
            rowItem={(item: any) => getHeatMapDataTemplate(item, "upward")}
          />
        </Div>
      </Div>}
      {getRows("self").length > 0 && <Div>
        <Div>
          <FilterLayout
            title="Time Series Table Heat Map - Self"
            selected={selected["self"]}
            filters={filterOptions}
            onSelect={(item: any) => {
              setSelected({
                ...selected,
                self: item,
              });
              getHeatMapData("self", selections["self"], item.key);
            }}
            btnItem={
              <Button
                variant="outline-primary"
                onClick={() =>
                  setShowMultiSelect({ upward: false, self: true })
                }
              >
                <Icon icon="far fa-list mr-2" />
                Show all items
              </Button>
            }
          >
            <AnalyticsFilters
              filters={selections["self"]}
              hideTypes
              config={{
                surveys: {
                  multiple: false,
                },
                layout: {
                  year: {
                    xl: 3,
                    sm: 6,
                  },
                  survey: {
                    xl: 3,
                    sm: 6,
                  },
                },
              }}
              onYearChange={(item: any) => {
                onTableLoad("self", "self", item.key, selected.self.id);
              }}
              onSurveyChange={(items: any) => {
                const item = selections["self"];
                getHeatMapData(
                  "self",
                  {
                    ...item,
                    selected: {
                      ...item.selected,
                      surveys: items,
                    },
                  },
                  selected.self.id
                );
              }}
            />
          </FilterLayout>
        </Div>
        <Div>
          <HeatMapTable
            columns={getColumnsData("self")}
            values={getAllValues("self")}
            rows={getRows("self")}
            rowItem={(item: any) => getHeatMapDataTemplate(item, "self")}
          />
        </Div>
      </Div>}
      {/* <Div>
        <DownloadExcel hideFilters title="Time Series Table Heat Map" />
      </Div> */}
      <Div>
        {/* <HeatMapTable
          columns={columns}
          rows={rows}
          rowItem={(item: any) => {
            const { Data } = HeatMapTable;
            return (
              <>
                <Data className="text-dark bg-light">{item.label}</Data>
                <Data value={item.individualAverage}>
                  {item.individualAverage}
                </Data>
                <Data value={item.firmAverage}>{item.firmAverage}</Data>
                <Data value={item.departmentAverage}>
                  {item.departmentAverage}
                </Data>
                <Data value={item.practiceGroupAverage}>
                  {item.practiceGroupAverage}
                </Data>
                <Data value={item.titleAverage}>{item.titleAverage}</Data>
                <Data value={item.selfAverage}>{item.selfAverage}</Data>
              </>
            );
          }}
        /> */}
      </Div>
    </Layout.Container>
  );
};

Item.propTypes = {};

export default Item;
