@for $i from 1 through 50 {
  .fs-#{$i * 2} {
    font-size: #{$i * 2}px !important;
  }
}
@for $i from 100 through 900 {
  .fw-#{$i * 2} {
    font-weight: #{$i * 2} !important;
  }
}
.text-underline {
  text-decoration: underline;
}

.no-underline {
  text-decoration: none !important;
}

.no-pointer-events {
  pointer-events: none;
}

.custom-tabs-1 {
  .nav-tabs .nav-link,
  a {
    border: none;
    color: #2f2f2f;
    font-family: "Inter";
    font: 400;
    font-size: 14px;
  }

  .nav-tabs .nav-item.show .nav-link,
  .nav-tabs {
    border: none;
  }

  .nav-tabs .nav-item.show .nav-link,
  .nav-tabs .nav-link.active {
    border: none;
    font-weight: 600;
    font-family: "Inter";
    color: #518cff;
    border-bottom: 4px solid #518cff;
  }
}

.custom-tabs-2 {
  .nav-tabs .nav-link,
  a {
    border: none;
    color: #2f2f2f;
    font-family: "Inter";
    font: 400;
    font-size: 14px;
  }

  // .nav-tabs .nav-item.show .nav-link,
  // .nav-tabs {
  //   // border: none;
  // }

  .nav-tabs .nav-item.show .nav-link,
  .nav-tabs .nav-link.active {
    border: none;
    color: #518cff;
    font-weight: 600;
    font-family: "Inter";
    border-bottom: 4px solid #518cff;
  }
}

.cursor-pointer {
  cursor: pointer;
}

.border-none {
  border: none !important;
}

.v-scroll {
  overflow-y: auto;
}

.h-scroll {
  overflow-x: auto;
}

.position-relative {
  position: relative;
}

.position-absolute {
  position: absolute;
}

.position-fixed {
  position: fixed;
}

.navbar-toggler:focus {
  outline: none;
}
