// import _ from "lodash";
import React from "react";
// import { Text, Div } from "unmatched/components";
// <PERSON><PERSON><PERSON>, <PERSON>,
import {
  LineChart,
  Line,
  XAxis,
  <PERSON>Axis,
  CartesianGrid,
  Responsive<PERSON>ontainer,
  <PERSON><PERSON>ianAxis,
  Toolt<PERSON>,
} from "recharts";
// import PropTypes from 'prop-types';

export const getColors = (count: number) => {
  // const base = "#518CFF";
  const output: any = [
    "#518CFF",
    "#D02929",
    "#D08D29",
    "#61CE3B",
    "#9B29D0",
    "#AE4545",
    "#29D0BC",
    "#DAD219",
    "#6D4A15",
  ];
  // for (let i = 1; i <= count; i++) {
  //   output = [...output, `rgb(0, ${i * 100}, 255`];
  // }
  return output.slice(0, count);
};

const CompareChart = (props: any) => {
  const { labels, data } = props;

  const getLinesTemplate = () => {
    const _labels: any = labels || [];
    return _labels.map((item: any, index: string) => {
      const stroke = getColors(_labels.length)[index];
      return (
        <Line key={item} dataKey={item} stroke={stroke} activeDot={{ r: 10 }} />
      );
    });
  };

  return (
    <ResponsiveContainer width={"100%"} height={300}>
      <LineChart width={900} height={300} data={data}>
        <CartesianGrid horizontalPoints={[1]} stroke="#DADADA">
          <CartesianAxis axisLine={{ stroke: "#DADADA" }} />
        </CartesianGrid>
        <XAxis
          dataKey="year"
          tickLine={false}
          axisLine={{ stroke: "#DADADA" }}
          stroke="#DADADA"
          tick={{
            fontSize: "10px",
            dy: 0,
            stroke: "#838383",
            fontFamily: "Inter",
            fontWeight: "100",
          }}
          padding={{ left: 80, right: 80 }}
        />
        {/* dataKey="id" */}
        <YAxis
          type="number"
          domain={[0, 5]}
          axisLine={{ stroke: "#DADADA" }}
          stroke="#DADADA"
          tickLine={false}
          tick={{
            fontSize: "10px",
            dx: -20,
            stroke: "#838383",
            fontFamily: "Inter",
            fontWeight: "100",
          }}
        />
        <Tooltip />
        {/* <Tooltip /> */}
        {/* <Legend /> */}
        {getLinesTemplate()}
      </LineChart>
    </ResponsiveContainer>
  );
};

// LineChart;.propTypes = {

// }

export default CompareChart;
