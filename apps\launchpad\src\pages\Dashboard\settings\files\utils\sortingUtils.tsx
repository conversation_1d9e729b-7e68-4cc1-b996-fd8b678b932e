import React from "react";
import { ArrowDown, ArrowUp } from "lucide-react";
import { SortDirection } from "../useFiles";
export type { SortDirection };

/**
 * Get the appropriate sort icon based on the current sort state
 */
export const getSortIcon = (
  field: string,
  currentSortField: string,
  currentSortDirection: SortDirection
): React.ReactNode => {
  if (field !== currentSortField) {
    return null;
  }

  if (currentSortDirection === "asc") {
    return React.createElement(ArrowUp, { className: "ml-1 h-4 w-4" });
  } else {
    return React.createElement(ArrowDown, { className: "ml-1 h-4 w-4" });
  }
};

/**
 * Get the next sort direction
 *
 * This function is overloaded to support both:
 * 1. Comparing a field with the current sort field and direction
 * 2. Simply toggling the current direction
 */
export function getNextSortDirection(
  fieldOrDirection: string | SortDirection,
  currentSortField?: string,
  currentSortDirection?: SortDirection
): SortDirection {
  // If only one argument is provided, assume it's the current direction to toggle
  if (arguments.length === 1 && (fieldOrDirection === "asc" || fieldOrDirection === "desc")) {
    return fieldOrDirection === "asc" ? "desc" : "asc";
  }

  // Otherwise, handle the field comparison case
  if (currentSortField && fieldOrDirection !== currentSortField) {
    return "asc";
  }

  return (currentSortDirection === "asc") ? "desc" : "asc";
};

/**
 * Map frontend field names to backend field names for sorting
 */
export const getBackendFieldName = (field: string): string => {
  const fieldMap: Record<string, string> = {
    title: "title",
    successRecords: "success_upload_count",
    rejectedRecords: "rejected_records",
    totalRecords: "total_file_records",
    uploadedOn: "created_at",
  };

  return fieldMap[field] || field;
};
