import { cloneDeep } from "lodash";
import { ErrorType } from "unmatched/types";
import { generateReducer, setState } from "unmatched/utils/store";

interface FormData {
  firstName: string;
  lastName: string;
  title: string;
  department: string;
  location: string;
}

interface StateType {
  isLoading: false;
  error: ErrorType;
  formData: FormData;
}

export const TYPES = {
  FORM_FETCH: "app/auth/account-information/form/fetch",
  FORM_FETCH_SUCCESS: "app/auth/account-information/form/fetch-success",
  FORM_FETCH_FAILURE: "app/auth/account-information/form/fetch-failure",
  RESET: "app/auth/account-information/reset",
};

const initialState: StateType = {
  isLoading: false,
  error: {
    statusCode: 0,
    errorCode: "",
    msg: "",
  },
  formData: {
    firstName: "",
    lastName: "",
    title: "",
    department: "",
    location: "",
  },
};

export const updateUserAction = () => {
  return () => {
    // dispatch({ type: TYPES.SESSION_FETCH });
    // getSessionFact('', '').then((response: AxiosResponse) => {
    //   dispatch({ type: TYPES.SESSION_FETCH_SUCCESS, payload: response.data.payload });
    // }, (error: Error) => {
    //     dispatch({ type: TYPES.SESSION_FETCH_FAILURE, payload: { msg: 'asdasd' } });
    // });
  };
};

const ACTIONS = {
  [TYPES.FORM_FETCH_SUCCESS]: (state: StateType, payload: any) =>
    setState(state, {
      formData: {
        ...payload,
        isLoading: false,
      },
    }),
  [TYPES.FORM_FETCH_FAILURE]: (state: StateType, payload: any) =>
    setState(state, {
      formData: {
        ...state.formData,
        error: payload,
        isLoading: false,
      },
    }),
  [TYPES.RESET]: (state: StateType) => setState(state, cloneDeep(initialState)),
};

export default generateReducer(ACTIONS, initialState);
