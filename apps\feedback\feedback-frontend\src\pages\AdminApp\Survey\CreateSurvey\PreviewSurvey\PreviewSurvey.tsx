import React from "react";
import _ from "lodash";
import { useH<PERSON>ory, useParams } from "unmatched/hooks";
// import BASE_STATE from "./preview-survey-meta";
import {
  Div,
  Layout,
  Text,
  Card,
  Button,
  Icon,
  Form,
  FormGroup,
  FormControl,
} from "unmatched/components";
// import Footer from "../Footer";
import Header from "../Header";
import { QUESTION } from "../../survey-enums";
import { useCreateSurveyContext } from "../Provider";
import util from "unmatched/utils";
import {
  getSurveyQuestionsFact,
  getSurveySectionsFact,
} from "../../survey-api";
import styled from "styled-components";
import { DateTime } from "luxon";

const QuestionCard = styled(Card)`
  background: #fcfcfc;
  border-radius: 8px;
`;

const RatingContainer = styled(Div)`
  width: 20%;
`;
const RatingButton = styled(Button)`
  /* background: #FFFFFF; */
  padding: 0;
  border: 1px solid #f2f2f2;
  width: 100%;
  .number-area {
    min-width: 30px;
  }
  .border-l {
    border-left: 1px solid #f2f2f2;
  }
`;

const optionsTemplate = {
  [QUESTION.RADIO]: (question: any) => {
    const options = question.radio.options || [];
    return (
      <>
        {options.map((item: string, index: number) => {
          return (
            <FormGroup key={index}>
              <FormControl.Radio>
                <FormControl.Radio.Label>{item}</FormControl.Radio.Label>
                <FormControl.Radio.Input disabled />
              </FormControl.Radio>
            </FormGroup>
          );
        })}
      </>
    );
  },
  [QUESTION.CHECKBOX]: (question: any) => {
    const options: any = [...Object.values(question.checkbox.options)];

    // question.checkbox.options || [];
    return (
      <>
        {options.map((item: string, index: number) => {
          return (
            <FormGroup key={index}>
              <FormControl.Checkbox>
                <FormControl.Checkbox.Label>{item}</FormControl.Checkbox.Label>
                <FormControl.Checkbox.Input disabled />
              </FormControl.Checkbox>
            </FormGroup>
          );
        })}
      </>
    );
  },
  [QUESTION.INPUT]: () => (
    <FormControl.Textarea
      className="bg-white"
      disabled
      placeholder="This is a simple feedback"
      rows={4}
    />
  ),
  [QUESTION.RATING]: (question: any) => {
    const { hasSwap } = question;
    // console.log(question);
    const { toPairs, reverse } = util.lib;
    let tags = toPairs(question.rating.tags);

    if (hasSwap) {
      tags = reverse(tags);
    }
    // const pairs = toPairs(question.rating.tags);
    // const list = reverse(pairs);
    return (
      <Div>
        <Div className="d-block">
          <Div className="row gx-3 m-0">
            {tags.map(([key, value]: any) => {
              return (
                <RatingContainer key={key} className="col px-2">
                  <RatingButton
                    variant="light"
                    disabled
                    className="mb-2"
                    key={key}
                  >
                    <Div className=" d-flex flex-row justify-content-center align-items-center">
                      <Div className="number-area py-2">{key}</Div>
                      <Div className="border-l p-2 flex-grow-1 text-left">
                        <Text.P2>{value}</Text.P2>
                      </Div>
                    </Div>
                  </RatingButton>
                </RatingContainer>
              );
            })}
            <RatingContainer className="col px-2">
              <RatingButton variant="light" disabled className="mb-2">
                <Div className=" d-flex flex-row justify-content-center align-items-center">
                  <Div className="number-area py-2">0</Div>
                  <Div className="border-l p-2 flex-grow-1 text-left">
                    <Text.P2>No basis for rating</Text.P2>
                  </Div>
                </Div>
              </RatingButton>
            </RatingContainer>
          </Div>
        </Div>
      </Div>
    );
  },
};

const Question = (props: any) => {
  const { question, index } = props;
  const [isEnabled, setEnable] = React.useState(false);

  const getFeedbackButton = () => {
    if (!question.textFeedback || question.type === QUESTION.INPUT) return null;
    if (isEnabled) {
      return (
        <Button
          variant="link"
          onClick={() => setEnable(false)}
          className="text-danger"
        >
          Clear Comment
        </Button>
      );
    }
    return (
      <Button
        variant="link"
        className="text-primary"
        onClick={() => setEnable(true)}
      >
        Add Feedback <Icon icon="far fa-pencil-alt" />
      </Button>
    );
  };

  const getFeedbacTemplate = () => {
    if (isEnabled) {
      return (
        <Form>
          <FormGroup>
            <FormControl.Textarea rows={4} disabled />
          </FormGroup>
        </Form>
      );
    }
    return null;
  };

  const getOptionsTemplate = _.get(optionsTemplate, question.type);

  return (
    <QuestionCard className="my-2 px-3 py-4" noShadow>
      <Div>
        <Text.P2>
          {question.type === util.enums.QUESTION.Paragraph
            ? "Instruction"
            : `Question ${index}`}
        </Text.P2>
      </Div>
      <Layout.Flex className="py-2">
        <Layout.FlexItem className="pt-2">
          <Text.H3>{question.question}</Text.H3>
        </Layout.FlexItem>
        <Layout.FlexItem className="ml-auto">
          {getFeedbackButton()}
        </Layout.FlexItem>
      </Layout.Flex>
      {getOptionsTemplate ? getOptionsTemplate(question) : ""}
      {getFeedbacTemplate()}
    </QuestionCard>
  );
};

const PreviewSurvey = (props: any) => {
  const {
    updateBuilderStatus,
    questionsData,
    sectionsData,
    survey,
    versionsData,
    version,
    setActiveVersionId,
  } = useCreateSurveyContext();
  const params: any = useParams();
  const history = useHistory();
  const versionId = Number(params.versionId);
  const sections = sectionsData.data;
  const questions = questionsData.data;

  // const [sections] = useState(BASE_STATE.sections);
  // const [questions, setQuestions] = useState(BASE_STATE.questions);
  // const meta = useState({
  //   ...BASE_STATE,
  //   sections: undefined,
  //   questions: undefined,
  // });

  React.useEffect(() => {
    !props.viewOnly && updateBuilderStatus(util.enums.SurveyStatus.Preview);
  }, []);

  React.useEffect(() => {
    verifyVersion();
  }, [versionId]);

  const verifyVersion = () => {
    let active = versionId || 0;
    if (versionId) {
      active = versionId;
    } else if (version.id) {
      active = version.id;
    } else if (versionsData.data.length) {
      active = versionsData.data[0].id;
    }
    setActiveVersionId(active);
    history.push(
      util.appUrls.admin.survey.create.getPreviewUrl(survey.data.id, active)
    );
    if (!versionId) return;
    getSections(versionId);
    getQuestions(versionId);
  };

  const getSections = (id: any) => {
    getSurveySectionsFact(id).then((response: any) => {
      sectionsData.onSuccess(response);
    });
  };

  const getQuestions = (id: any) => {
    getSurveyQuestionsFact(id).then((response: any) => {
      questionsData.onSuccess(response);
    });
  };

  return (
    <Div>
      <Header
        metaItem={
          <Text.P1>
              {DateTime.fromISO(new Date(survey.data.updatedAt).toISOString()).toFormat(
                  " LLL dd, yyyy, hh:mm a"
                )}
          </Text.P1>
        }
        title={<Text.H1 className="pb-2">{survey.data.name}</Text.H1>}
        breadcrumbs={props.breadcrumbs}
      />
      <Layout.Container className="py-3 mb-3" fluid>
        <Div>
          <Text.P1 className="pt-2">
            {survey.data.description
              .split("\n")
              .map((item: string, i: number) => {
                if (item.trim() === "") {
                  return <br key={i} />;
                }
                return (
                  <span key={i}>
                    {item}
                    <br />
                  </span>
                );
              })}
          </Text.P1>
        </Div>
        <Div className="mt-4">
          {sections.map((item: any) => {
            const filteredQuestions = questions.filter(
              ({ sectionId }: any) => item.id === sectionId
            );
            const questionNumbers = util.getQuestionNumbers(filteredQuestions);
            return (
              <Div key={item.id}>
                <Text.H2 className="py-2 border-bottom">
                  {item.label || item.name}
                </Text.H2>
                {filteredQuestions.map((_question: any) => {
                  return (
                    <Question
                      key={_question.id}
                      question={_question}
                      index={questionNumbers[_question.id]}
                    />
                    // onFeedbackChange={(val: string) =>
                    //   onFeedbackChange(val, questionData)
                    // }
                  );
                })}
              </Div>
            );
          })}
        </Div>
      </Layout.Container>
    </Div>
  );
};

export default PreviewSurvey;
