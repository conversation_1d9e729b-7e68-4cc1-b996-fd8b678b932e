import React from "react";
import _ from "lodash";
import util from "../utils";

export default function useTable({
  _search,
  size,
  page,
  totalPages,
  _error,
  _sort,
}: any) {
  const [renderIndex, setRenderIndex] = React.useState(0);
  const [isLoading, setLoading] = React.useState(false);
  const [selected, setSelected] = React.useState<Array<string | number>>([]);
  const [selectAll, setSelectAll] = React.useState(false);
  const [pagination, setPagination] = React.useState({
    size: size || 10,
    page: page || 1,
    totalPages,
    totalItems: 0,
  });
  const [search, setSearch] = React.useState(_search || "");
  const [sort, setSort] = React.useState(_sort || "");
  const [error, setError] = React.useState(
    _error || {
      statusCode: 0,
      errorCode: "",
      msg: "",
    }
  );

  const onSelectAll = (
    list: Array<any>,
    checked: boolean,
    identifier: string | Function
  ) => {
    setSelectAll(checked);
    setSelected(() =>
      checked
        ? list.map((item: any) => {
            if (_.isFunction(identifier)) {
              return identifier(item);
            }
            return _.get(item, identifier.toString());
          })
        : []
    );
  };

  const isSelected = (item: any) => {
    return selected.includes(item);
  };

  const onSelect = (item: any, identifier: string | Function) => {
    const value = _.isFunction(identifier)
      ? identifier(item)
      : _.get(item, identifier.toString());

    if (isSelected(value)) {
      setSelected((_selected) => {
        const copy = _selected;
        return copy.filter((item: any) => item !== value);
      });
    } else {
      setSelected((_selected) => {
        const copy = _selected;
        copy.push(value);
        return [...copy];
      });
    }
  };

  const onPageSelect = (page: number) => {
    setPagination((_item: any) => {
      return {
        ..._item,
        page,
      };
    });
  };

  const updatePagination = (payload: any) => {
    setPagination((_item: any) => {
      return {
        ..._item,
        ...payload,
      };
    });
  };

  const getIndex = (index: number) => {
    const { page, size } = pagination;
    const pageOffset = page ? page - 1 : 0;
    if (pageOffset && size) {
      const sum = pageOffset * size;
      return sum + index + 1;
    }
    return index + 1;
  };

  const resetColumns = (_columns: any, item: any) => {
    const reset = {};
    util.lib.forOwn(_columns, (value: any, key: any) => {
      const data = _columns[key];
      if (key === item.key) {
        util.lib.set(reset, key, item);
      } else if (data.hasSort) {
        util.lib.set(reset, key, {
          ...data,
          sortValue: "",
        });
      } else {
        util.lib.set(reset, key, data);
      }
    });
    return reset;
  };

  const getColumnsArray = (_columns: any) => {
    return util.lib.entries(_columns).map(([key, value]: any) => {
      return { key, ...value };
    });
  };

  const splitColumns = (_columns: any) => {
    const sorted = _columns.sort((item: any) => item.order);
    const index = sorted.findIndex((item: any) => item.pushDynamic) + 1;
    return [sorted.slice(0, index), sorted.slice(index, _columns.length)];
  };

  const getParams = () => {
    return {
      search: search,
      page: pagination.page,
      page_size: pagination.size,
    };
  };

  const filterColumns = (_columns: any) => {
    const lastIndex = _columns.length - 4;
    let list = [];
    if (renderIndex > lastIndex) {
      list = _columns.slice(lastIndex, _columns.length);
    } else {
      list = _columns.slice(renderIndex, renderIndex + 4);
    }
    return list.map((item: any, index: number) => {
      return {
        ...item,
        prevArrow: index === 0 ? item.key : null,
        nextArrow: index === list.length - 1 ? item.key : null,
      };
    });
  };

  const getSNo = (index: number) => {
    return pagination.size * (pagination.page - 1) + (index + 1);
  };

  return {
    search,
    getSNo,
    sort,
    setSort,
    ...pagination,
    selected,
    selectAll,
    setPagination,
    updatePagination,
    setSearch,
    onSelectAll,
    onSelect,
    onPageSelect,
    isSelected,
    isLoading,
    setLoading,
    error,
    setError,
    resetColumns,
    getIndex,
    getParams,
    getColumnsArray,
    splitColumns,
    renderIndex,
    setRenderIndex,
    filterColumns,
  };
}
