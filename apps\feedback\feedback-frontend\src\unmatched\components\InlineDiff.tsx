import * as React from "react";
import { diffWords } from "diff";
import styled from "styled-components";

const Wrap = styled.div`
  font-size: 14px;
  .change {
    display: inline-block;
  }
  
  .removed {
    height: 24px;
    text-decoration: line-through;
    background-color: #FFCCC7;
    padding: 0 2px;
  }
  
  .added {
    height: 24px;
    background-color: #D9F7BE;
    padding: 0 2px;
  }
`;


interface IInLineDiffProps {
  oldValue: string;
  newValue: string;
}

const getUniqueKey = (text: string, index: number) => `${text}--${index}`;

const AddedDiff = ({ text }: { text: string }) => (
  <span className="added">{text}</span>
);
const RemovedDiff = ({ text }: { text: string }) => (
  <span className="removed">{text}</span>
);

const InLineDiff = ({ oldValue, newValue }: IInLineDiffProps) => {
  const difference = diffWords(oldValue, newValue, {
    
  });

  const result = difference.map((change: any, index: any) =>
    !change.added && !change.removed ? (
      <span key={getUniqueKey(oldValue, index)} className="change">
        {change.value}
      </span>
    ) : change.added ? (
      <React.Fragment key={getUniqueKey(oldValue, index)}>
        {<AddedDiff text={change.value} />}
      </React.Fragment>
    ) : (
      <React.Fragment key={getUniqueKey(oldValue, index)}>
        {<RemovedDiff text={change.value} />}
      </React.Fragment>
    )
  );

  return <Wrap>{result}</Wrap>;
};

export default InLineDiff;
