import React from "react";
import styled from "styled-components";
import { Div, Layout, Tab, Nav } from "unmatched/components";
import appUrls from "unmatched/utils/urls/app-urls";
import useCustomLayout from "../../../Shared/custom-layout-hook";
import Individual from "../ReportViews/Individual/Individual";
import Aggregate from "../ReportViews/Aggregate/Aggregate";
// import Emailstats from "../ReportViews/EmailStats/EmailStats";
import { useQuery, useHistory, useParams } from "unmatched/hooks";
// import { getSurveyByIdFact } from "pages/AdminApp/Survey/survey-api";
// import util from "unmatched/utils";
import { requestNew360Report } from "../../reports-api";
import useToastr from "unmatched/modules/toastr/hook";
import DataDump from "../ReportViews/DataDump/DataDump";
// import General from "./General/General";
// import ParticipationReport from "./ParticipationReport/ParticipationReport";
// import ReportCount from "./ReportCount/ReportCount";

const TABS = {
  INDIVIDUAL: "INDIVIDUAL",
  AGGREGATE: "AGGREGATE",
  EMAIL_STATS: "EMAIL_STATS",
  DATA_DUMP: "DATA_DUMP",
};

const NavItem = styled(Nav.Item)`
  font-size: 14px;
  padding-top: 15px;
  .active {
    background: #ebeff7 !important;
    position: relative;
    color: #2f2f2f !important;
    font-weight: 600;
    border-radius: 0;
    // &::after {
    //   border-bottom: 2px solid #518cff;
    //   position: absolute;
    //   content: "";
    //   z-index: 999;
    //   width: 10%;
    //   left: 15px;
    //   bottom: 0;
    // }
  }
`;

const NavLink = styled(Nav.Link).attrs({
  className: "text-muted",
})``;

type Props = {
  survey: any;
};

export default function UpwardSurveyReport({ survey }: Props) {
  const layout = useCustomLayout();
  const toastr = useToastr();
  const queryParams = useQuery();
  const params = useParams<any>();
  const history = useHistory();
  const [activeTab, setActiveTab] = React.useState(
    queryParams.get("filter") || TABS.INDIVIDUAL
  );

  const [loaders, setLoaders] = React.useState({
    generate: false,
  });
  const onTabSelect = (filter: string) => {
    history.push(appUrls.admin.reports.getSurveyReportsUrl(survey.id, filter));
    setActiveTab(filter);
  };
  const generateReport = async () => {
    setLoaders({ ...loaders, generate: true });
    try {
      const response = await requestNew360Report({ index: params.id });
      toastr.onSucces({
        title: "Report Generation Requested",
        content:
          "Reports generatation request has been sent with request id: " +
          response.data.taskId,
      });
      setLoaders({ ...loaders, generate: false });
    } catch (err) {
      setLoaders({ ...loaders, generate: false });
    }
  };

  return (
    <Tab.Container defaultActiveKey={activeTab}>
      <Layout.Sidebar
        className="dark-sidebar"
        hasHeader={false}
        style={{ marginLeft: layout.sidebar.marginLeft, zIndex: 1000 }}
        width={layout.sidebar.width}
      >
        <Div className="pl-3 py-2">
          {/* <Link style={{ color: '#fff' }} to={appUrls.admin.reports.surveyList}>
            <Icon icon="far fa-chevron-left" /> Surveys
          </Link> */}
        </Div>
        <Nav variant="pills" className="flex-column">
          <NavItem>
            <NavLink
              className="dark-nav-link text-white"
              eventKey={TABS.INDIVIDUAL}
              onSelect={(_filter: string) => onTabSelect(_filter)}
            >
              Individual Reports
            </NavLink>
          </NavItem>
          <NavItem>
            <NavLink
              className="dark-nav-link text-white"
              eventKey={TABS.AGGREGATE}
              onSelect={(_filter: string) => onTabSelect(_filter)}
            >
              Aggregate Reports
            </NavLink>
          </NavItem>
          <NavItem>
            <NavLink
              className="dark-nav-link text-white"
              eventKey={TABS.DATA_DUMP}
              onSelect={(_filter: string) => onTabSelect(_filter)}
            >
              Data Dump
            </NavLink>
          </NavItem>
          {/* <NavItem>
            <NavLink
              className="dark-nav-link text-white"
              eventKey={TABS.EMAIL_STATS}
              onSelect={(_filter: string) => onTabSelect(_filter)}
            >
              Email Statistics
            </NavLink>
          </NavItem> */}
        </Nav>
      </Layout.Sidebar>
      <Div className="" style={{ marginLeft: layout.container.marginLeft }}>
        <Tab.Content>
          <Tab.Pane className="bg-white" eventKey={TABS.INDIVIDUAL}>
            <Individual
              survey={survey}
              layout={layout}
              surveyId={params.id}
              generateReport={generateReport}
              loaders={loaders}
              type="360"
            />
          </Tab.Pane>
          <Tab.Pane className="bg-white" eventKey={TABS.AGGREGATE}>
            <Aggregate
              survey={survey}
              layout={layout}
              surveyId={params.id}
              generateReport={generateReport}
              loaders={loaders}
            />
          </Tab.Pane>
          <Tab.Pane className="bg-white" eventKey={TABS.DATA_DUMP}>
            <DataDump
              survey={survey}
              layout={layout}
              surveyId={params.id}
              generateReport={generateReport}
              loaders={loaders}
            />
          </Tab.Pane>
          {/* <Tab.Pane className="bg-white" eventKey={TABS.EMAIL_STATS}>
            <Emailstats
              survey={survey}
              layout={layout}
              surveyId={params.id}
              generateReport={generateReport}
              loaders={loaders}
            />
          </Tab.Pane> */}
        </Tab.Content>
      </Div>
    </Tab.Container>
  );
}
