{"name": "survey-frontend-v2", "version": "2.0.1", "private": true, "dependencies": {"@reduxjs/toolkit": "^1.5.1", "@types/draft-js": "^0.11.9", "@unmatchedoffl/ui-core": "1.0.72", "anchorme": "^2.1.2", "axios": "^0.21.0", "bootstrap": "^4.6.2", "diff": "^5.1.0", "formik": "^2.2.5", "immutable": "^4.0.0", "lodash": "^4.17.21", "luxon": "^2.0.2", "mobile-device-detect": "^0.4.3", "react": "^17.0.2", "react-app-polyfill": "^2.0.0", "react-bootstrap": "^1.6.7", "react-datepicker": "^3.4.0", "react-dnd": "^13.0.1", "react-dnd-html5-backend": "^12.1.0", "react-dom": "^17.0.2", "react-dropzone": "^11.3.1", "react-helmet": "^6.1.0", "react-intersection-observer": "^8.31.0", "react-json-view": "^1.21.3", "react-lazyload": "^3.1.0", "react-quill": "^2.0.0", "react-redux": "^7.2.2", "react-router": "^5.2.0", "react-router-dom": "^5.2.0", "react-scroll": "^1.8.7", "react-select": "^4.3.0", "react-use": "^17.2.3", "react-vertical-timeline-component": "^3.6.0", "recharts": "^2.0.9", "redux": "^4.0.5", "redux-logger": "^3.0.6", "redux-persist": "^6.0.0", "redux-thunk": "^2.3.0", "sanitize-html": "^2.4.0", "styled-components": "^5.2.1", "web-vitals": "^0.2.4", "yup": "^0.32.8"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx,json,css,scss,md}": ["prettier --write", "eslint --fix --ext"]}, "resolutions": {"react-error-overlay": "6.0.9"}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@testing-library/jest-dom": "^5.11.6", "@testing-library/react": "^11.1.2", "@testing-library/user-event": "^12.2.2", "@types/history": "^4.7.8", "@types/jest": "^26.0.15", "@types/lodash": "^4.14.165", "@types/luxon": "^2.0.5", "@types/node": "^12.19.4", "@types/react": "^17.0.2", "@types/react-datepicker": "^3.1.2", "@types/react-dom": "^17.0.2", "@types/react-grid-layout": "^1.1.1", "@types/react-helmet": "^6.1.1", "@types/react-lazyload": "^3.0.0", "@types/react-redux": "^7.1.11", "@types/react-responsive": "^8.0.2", "@types/react-router": "^5.1.8", "@types/react-router-dom": "^5.1.6", "@types/react-scroll": "^1.8.3", "@types/react-select": "^4.0.13", "@types/redux-logger": "^3.0.8", "@types/sanitize-html": "^2.3.1", "@types/styled-components": "^5.1.4", "@typescript-eslint/eslint-plugin": "^5.14.0", "@typescript-eslint/parser": "^5.14.0", "eslint": "^7.11.0", "husky": "^7.0.4", "lint-staged": "^11.0.0", "prettier": "2.3.0", "react-error-overlay": "6.0.9", "react-scripts": "^4.0.3", "redux-devtools-extension": "^2.13.8", "sass": "^1.29.0", "sass-loader": "^10.1.0", "source-map-explorer": "^2.5.0", "typescript": "4.1.2"}, "scripts": {"start": "react-scripts --openssl-legacy-provider start", "prebuild": "npm run generate-build-version", "generate-build-version": "node generate-build-version", "build": "PUBLIC_URL=/feedback/ react-scripts --max_old_space_size=4096 build", "test": "react-scripts test", "eject": "react-scripts eject", "prepare": "husky install", "deploy-migration": "aws s3 sync ./build/ s3://unmatched-v2-testing/ --delete", "deploy-development": "aws s3 sync ./build/ s3://unmatched-v2-staging/ --delete", "deploy-master": "aws s3 sync ./build/ s3://unmatched-v2-saas-frontend-production/", "pretty": "prettier --write \"src/**/*.{js,jsx,ts,tsx,json,scss,md}\"", "lint": "eslint --fix --ext .js,.jsx,.ts,.tsx ."}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version", "ie 11"]}}