import { Div, util } from "@unmatchedoffl/ui-core";
import React from "react";
// import { QUESTION } from "../../../survey-enums";
// import { getOptionItem } from "./QuestionTypes";

interface Props {
  id: number;
  question: any;
  viewOnly?: any;
  renderOption: Function;
  questionTypes: any;
  permissions: any;
  api: any;
  random: any;
}

const Checkbox = ({
  // id,
  question,
  permissions,
  renderOption,
  questionTypes,
  api,
}: Props) => {
  const { checkbox } = question;

  const onItemUpdate = (label: any, option: any) => {
    let _options = [];
    if (!option || !option.id) {
      _options = [...checkbox.options, label.option];
    } else {
      _options = checkbox.options.map((item: any, index: number) => {
        if (option.id - 1 === index) {
          return label.option;
        }
        return item;
      });
    }
    const payload = {
      id: question.id,
      type: question.type,
      checkbox: {
        ...checkbox,
        options: _options,
      },
    };
    return api(payload).then(() => {
        return payload;
    });
  };

  const onItemDelete = (id: number) => {
    const _options = checkbox.options.filter(
      (item: any, index: number) => id - 1 !== index
    );
    const payload = {
      id: question.id,
      type: question.type,
      checkbox: {
        ...checkbox,
        options: _options,
      },
    };
    return api(payload).then(() => payload);
  };

  const CheckboxOptionItem = (params: any) => {
    const { options } = params;

    const content = (
      <Div className="form-check pl-4">
        <input type="checkbox" className="form-check-input" disabled />
      </Div>
    );

    const payload = {
      ...(options || {}),
      onSubmit: onItemUpdate,
      onRemove: onItemDelete,
      value: options ? options.value : "",
    };
    return renderOption(payload, content);
  };

  return (
    <Div>
      {util.lib.isArray(checkbox.options) &&
        checkbox.options.map((item: any, index: number) => {
          const id = index + 1;
          return (
            <CheckboxOptionItem
              key={`checkboxitem-${id}-${item}`}
              sNo={id}
              options={{
                id: id,
                type: questionTypes.CHECKBOX,
                value: item,
              }}
              onItemUpdate={onItemUpdate}
              onItemDelete={onItemDelete}
              viewOnly={permissions.viewOnly}
            />
          );
        })}
      <CheckboxOptionItem
        sNo={""}
        key={`default-${checkbox.options.length}`}
        onItemUpdate={onItemUpdate}
        viewOnly={permissions.viewOnly}
      />
    </Div>
  );
};

Checkbox.defaultProps = {
  permissions: {
    viewOnly: false,
  },
};

export default Checkbox;
