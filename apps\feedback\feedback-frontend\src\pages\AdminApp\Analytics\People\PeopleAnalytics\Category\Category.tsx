import React, { useEffect } from "react";
// Button, Icon, Table, Text
import {
  Div,
  HeatMapTable,
  Layout,
  Span,
  Button,
  Icon,
  Text,
} from "unmatched/components";
// import DownloadExcel from "../../../DownloadExcel";
import {
  getCategoryGraphDataFact,
  getCategoryHeatMapFact,
} from "../../people-api";
import useFilter from "pages/CommonFilters/hook";
import { useXHR } from "unmatched/hooks";
// import TimeSeriesGraph from "../../../TimeSeriesGraph/TimeSeriesGraph";
// import util from "unmatched/utils";
import useToastr from "unmatched/modules/toastr/hook";
import useAnalyticsFilters from "pages/AdminApp/Analytics/Shared/AnalyticsFilters/hook";
import AnalyticsFilters from "pages/AdminApp/Analytics/Shared/AnalyticsFilters/AnalyticsFilters";
import TimeSeriesGraphNew from "pages/AdminApp/Analytics/TimeSeriesGraph/TimeSeriesGraphNew";
// import PropTypes from 'prop-types';

const TYPES = {
  "360": "surveyindex360",
  upward: "surveyindexupward",
  self: "surveyindexself",
  "360Group": "surveyindex360group",
};

const FilterLayout = (props: any) => {
  return (
    <Div className="pt-3">
      <Div>
        <Text.H3>
          <Span>{props.title}</Span>
        </Text.H3>
      </Div>
      <Layout.Row className="pt-4 pb-3">
        <Layout.Col>{props.children}</Layout.Col>
        {/* {getFilterLayout()} */}
        <Layout.Col xl={2} lg={2} className="text-right">
          <Button
            onClcik={() => (props.onDownload ? props.onDownload() : "")}
            className="ml-auto"
            variant="outline-primary"
          >
            Download XLSX <Icon icon="fas fa-file-download" />
          </Button>
        </Layout.Col>
      </Layout.Row>
    </Div>
  );
};

const Category = (props: { user: any }) => {
  const { user } = props;

  const [columns, setColumns]: any = React.useState({
    upward: [],
    self: [],
    ["360"]: [],
  });

  const [categories, setCategories]: any = React.useState({
    upward: [],
    self: [],
    // ["360"]: [],
  });

  // const [resources, setResources] = React.useState({
  //   type: "360",
  //   options: ["Overall"],
  //   legends: [],
  //   selected: [],
  // });

  const graph = useXHR({
    defaultResponse: {
      data: [],
      labels: [],
    },
  });

  const filtersState = useFilter();

  const graphfilters = useAnalyticsFilters();

  const upwardfilters = useAnalyticsFilters();

  const selfFilters = useAnalyticsFilters();

  const [selectedTimeGraphCompareList, setSelectedTimeGraphCompareList] =
    React.useState<any>({});
  // const [selfYears, setSelfYears]: any = React.useState([]);

  useEffect(() => {
    if (!Object.values(selectedTimeGraphCompareList).length) return;
    getGraphData(
      {
        compare_list: Object.values(selectedTimeGraphCompareList),
      },
      selections["graph"]
    );
  }, [selectedTimeGraphCompareList]);

  const selections: any = {
    upward: upwardfilters,
    self: selfFilters,
    graph: graphfilters,
  };

  const toastr = useToastr();

  // Graph Function calls

  const onGraphLoad = (year?: any) => {
    selections["graph"]
      .getFilters({
        year,
        user_id: user.id,
        // resource_types: TYPES["360"],
      })
      .then(
        (_filters: any) => {
          const filteredSurveys = _filters.surveys.filter(
            (item: any) => item.type !== TYPES["self"]
          );
          const [selectedSurvey] =
            filteredSurveys.filter((item: any) => item.categories.length) || [];
          if (!selectedSurvey) {
            toastr.warningToast("No Surveys Found");
            // selections["graph"].setFilters(_filters);
            return;
          }
          const [selectedCategory] = selectedSurvey?.categories || [];
          if (!selectedCategory) {
            toastr.warningToast("No Categories Found");
            // selections["graph"].setFilters(_filters);
            return;
          }
          getGraphData(
            {},
            {
              ..._filters,
              surveys: filteredSurveys,
              // surveys: _filters.surveys.filter(
              //   (item: any) => selectedSurvey.type === item.type
              // ),
              categories: selectedSurvey?.categories.map((item: any) => ({
                id: item.id,
                key: item.key,
                title: item.title || item.name,
              })),
              selected: {
                ..._filters.selected,
                surveys:  selectedSurvey && selectedSurvey.id ? [selectedSurvey.id] : [],
                types: selectedSurvey ? [selectedSurvey.type] : [],
                groups: [],
                categories: selectedCategory ? [selectedCategory.id] : [],
              },
            }
          );
        },
        (err: any) => {
          // console.log(err);
          toastr.onError({
            ...err,
            msg: JSON.stringify(err.msg),
          });
        }
      );
  };

  const getGraphData = (filters?: any, _selections?: any) => {
    graph.setLoading(true);
    const payload = {
      compare_list: Object.values(selectedTimeGraphCompareList),
      ...(filters || {}),
      // people: [...(filters?.people || []), user.empId],
      rater_groups: _selections.selected.groups || [],
    };
    return getCategoryGraphDataFact(
      {
        userId: user.id,
        categoryId: _selections.selected.categories[0],
        type: _selections.selected.types[0],
      },
      payload
    ).then(
      (response: any) => {
        selections["graph"].setFilters(_selections);
        graph.onSuccess(response);
        return true;
      },
      () => {
        graph.setLoading(false);
      }
    );
  };

  // const onFilterSelect = (data: any) => {
  //   getGraphData(
  //     {
  //       ...data,
  //       people: data.people
  //         ? data.people.map((item: any) => util.getContentFromBrackets(item))
  //         : [],
  //     },
  //     selections["graph"]
  //   );
  // };

  const onFilterSelectNew = (data: any, groupID?: any) => {
    const computedData = Object.entries(data).reduce((acc, [k, v]) => {
      if ((v as any).length) {
        (acc as any)[k] = v;
      }
      return acc;
    }, {});
    setSelectedTimeGraphCompareList((s: any) => {
      let newList = { ...s, [groupID]: computedData };
      newList = Object.entries(newList).reduce((acc, [k, v]) => {
        if (Object.keys(v as any).length) {
          (acc as any)[k] = v;
        }
        return acc;
      }, {});
      return newList;
    });
  };

  // Heatmap Function calls

  const getDynamicColumns = (_meta: any) => {
    return _meta.map((item: any) => {
      return {
        id: item.field,
        key: item.field,
        label: `${item.display_name} Average`,
      };
    });
  };

  const getColumnsData = (type: any) => {
    const output = [
      { id: 1, label: "Name" },
      { id: 2, label: type !== 'self' ? "Reviewee Average" : "Self Average" },
      { id: 4, label: "Firm Average" },
      ...columns[type],
      // { id: 3, label: "Department Cateogry Average" },
      // { id: 5, label: "Practice group Category Average" },
      // { id: 6, label: "Title Category Average" },
      { id: 7, label: "Self Average" },
    ];
    return output;
  };

  const getSelectedSurveyIndexs = (_selections?: any) => {
    if (_selections) {
      if (_selections.selected.groups && _selections.selected.groups.length) {
        return _selections.selected.groups;
      } else if (
        _selections?.selected.surveys &&
        _selections?.selected.surveys.length
      ) {
        return _selections?.selected.surveys;
      }
    }
    return [];
  };

  const getHeatMapData = (type: any, _selections?: any) => {
    getCategoryHeatMapFact(user.id, {
      surveys: getSelectedSurveyIndexs(_selections),
      years: _selections?.selected.years || [],
      types: _selections?.selected.types || [],
    }).then(
      (response: any) => {
        selections[type].setFilters(_selections);
        setCategories((_categories: any) => {
          return {
            ..._categories,
            [type]: response.data,
          };
        });
        setColumns({
          ...columns,
          [type]: getDynamicColumns(_selections.meta),
        });
      },
      (err: any) => {
        toastr.onError({
          ...err,
          msg: JSON.stringify(err.msg),
        });
      }
    );
  };

  const onTableLoad = (type: string, year?: any) => {
    selections[type]
      .getFilters({
        year,
        user_id: user.id,
        resource_types:
          type === "self"
            ? TYPES["self"]
            : `${TYPES["upward"]},${TYPES["360"]},${TYPES["360Group"]}`,
      })
      .then(
        (_filters: any) => {
          const [selectedSurvey] = _filters.surveys || [];
          if (!selectedSurvey) {
            // toastr.warningToast(`No ${type} Surveys Found`);
            return;
          }
          let groups = [];
          if (
            [TYPES["360"], TYPES["360Group"]].includes(selectedSurvey?.type)
          ) {
            groups =
              selectedSurvey &&
              selectedSurvey["rater_groups"].map((item: any) => {
                return {
                  id: item.id,
                  key: item.key,
                  title: item.title,
                  type: item.resource_type,
                  groupId: selectedSurvey?.id,
                };
              });
          }
          getHeatMapData(type, {
            ..._filters,
            surveys: _filters.surveys,
            groups,
            selected: {
              ..._filters.selected,
              surveys:  selectedSurvey && selectedSurvey.id ? [selectedSurvey.id] : [],
              types: selectedSurvey ? [selectedSurvey.type] : [],
              groups: [],
            },
          });
        },
        (err: any) => {
          // console.log(err);
          toastr.onError({
            ...err,
            msg: JSON.stringify(err.msg),
          });
        }
      );
  };

  const getRows = (type: string) => {
    const list = categories[type] || [];
    return list;
  };

  const getAllValues = (type: string) => {
    const values: any = [];
    categories[type].forEach((item: any) => {
      const keys = [
        "revieweeAverage",
        "firmAverage",
        ...(item.metaKeys || []),
        // "departmentAverage",
        // "practiceGroupAverage",
        // "titleAverage",
        "selfAverage",
      ];
      keys.forEach((key: string) => {
        if (item[key]) {
          values.push(item[key]);
        }
      });
      // console.log(item);
    });
    // console.log(values);
    return values;
  };

  const getHeatMapDataTemplate = (item: any, type: string) => {
    const { Data } = HeatMapTable;
    const values = getAllValues(type);
    return (
      <>
        <Data className="text-dark bg-light ">
          <Div className="text-left">{item.name}</Div>
        </Data>
        <Data values={values} value={item.revieweeAverage}>
          {item.revieweeAverage}
        </Data>
        <Data values={values} value={item.firmAverage}>
          {item.firmAverage}
        </Data>
        {columns[type].map((col: any) => {
          return (
            <Data values={values} value={item[col.key]}>
              {item[col.key]}
            </Data>
          );
        })}
        {/* <Data values={values} value={item.departmentAverage}>
          {item.departmentAverage}
        </Data> */}
        {/* <Data values={values} value={item.practiceGroupAverage}>
          {item.practiceGroupAverage}
        </Data> */}
        {/* <Data values={values} value={item.titleAverage}>
          {item.titleAverage}
        </Data> */}
        <Data values={values} value={item.selfAverage}>
          {item.selfAverage}
        </Data>
      </>
    );
  };

  useEffect(() => {
    if (user.id) {
      onTableLoad("upward");
      onTableLoad("self");
      onGraphLoad();
    }
  }, [user]);

  return (
    <Layout.Container fluid className="pt-3">
      <Div className="py-3">
        <AnalyticsFilters
          filters={selections["graph"]}
          hideTypes
          config={{
            surveys: {
              multiple: false,
            },
            groups: {
              multiple: true,
              identifier: "groupKey",
            },
            // categories: {
            //   multiple: false
            // }
          }}
          onYearChange={(item: any) => {
            onGraphLoad(item.key);
            // onTableLoad(item.key, "graph");
          }}
          // onTypeChange={(item: any) => {
          //   // onFiltersChange({
          //   //   ...selections["graph"].selected,
          //   //   types: [item.key],
          //   //   surveys: [],
          //   // });
          // }}
          onSurveyChange={(items: any) => {
            const item = selections["graph"];
            const [surveyId] = items;
            const selected = item.surveys.find((s: any) => {
              return items.includes(s.id);
            });
            const groups = selected["rater_groups"]?.map((item: any) => {
              return {
                id: item.id,
                key: item.key,
                groupKey: item["rater_group"],
                title: item.title,
                type: item.resource_type,
                groupId: surveyId,
                categories: item.categories || [],
              };
            });
            const categories = selected?.categories.map((item: any) => {
              return {
                id: item.id,
                key: item.key,
                title: item.title || item.name,
                groupId: surveyId,
              };
            });
            getGraphData(filtersState.selected, {
              ...item,
              groups,
              categories,
              selected: {
                ...item.selected,
                surveys: items,
                groups: [],
                types: [selected.type],
                categories: categories.length ? [categories[0].id] : [],
              },
            });
          }}
          onGroupChange={(items: any) => {
            const item = selections["graph"];
            // const filteredGroups =
            //   item.groups.filter((item: any) => items.includes(item.id)) || [];
            const categories = item.categories.map((item: any) => {
              return {
                id: item.id,
                key: item.key,
                title: item.title || item.name,
                // groupId: selected.id,
              };
            });
            getGraphData(filtersState.selected, {
              ...item,
              // groups,
              // categories,
              selected: {
                ...item.selected,
                // surveys: items,
                groups: items,
                types: [TYPES["360Group"]],
                categories: categories.length ? [categories[0].id] : [],
              },
            });
          }}
          onCategoryChange={(items: any) => {
            const item = selections["graph"];
            let [selected] =
              item.categories.filter((item: any) => items.includes(item.id)) ||
              {};
            if (!selected) {
              selected = item.categories.length ? item.categories[0] : {};
            }
            getGraphData(
              {},
              {
                ...item,
                // groups,
                // categories,
                selected: {
                  ...item.selected,
                  categories: selected.id ? [selected.id] : [],
                },
              }
            );
          }}
        />
      </Div>
      <Div>
        {/* <TimeSeriesGraph
          filtersState={filtersState}
          onFilterSelect={onFilterSelect}
          userName={user.name}
          data={graph.data.data}
          labels={graph.data.labels}
          isLoading={graph.isLoading}
          // resourceTypes={{
          //   ...resources,
          //   onChange: onResourceChange,
          //   onGroupChange,
          // }}
        /> */}
        <TimeSeriesGraphNew
          onFilterSelect={onFilterSelectNew}
          userName={user.name}
          data={graph.data.data}
          labels={graph.data.labels}
          isLoading={graph.isLoading}
        />
      </Div>
      {getRows("upward").length > 0 && <Div>
        <Div>
          {/* {JSON.stringify(selections)} */}
          <FilterLayout title="Heat Map - Upward/360 Degree Review">
            <AnalyticsFilters
              filters={selections["upward"]}
              hideTypes
              config={{
                surveys: {
                  multiple: false,
                },
                groups: {
                  multiple: false,
                },
              }}
              onYearChange={(item: any) => {
                onTableLoad("upward", item.key);
              }}
              onSurveyChange={(items: any) => {
                const item = selections["upward"];
                const [surveyId] = items;
                const selected = item.surveys.find((s: any) => {
                  return items.includes(s.id);
                });
                const groups = selected["rater_groups"]?.map((item: any) => {
                  return {
                    id: item.id,
                    key: item.key,
                    title: item.title,
                    type: item.resource_type,
                    groupId: surveyId,
                  };
                });
                getHeatMapData("upward", {
                  ...item,
                  groups,
                  selected: {
                    ...item.selected,
                    surveys: items,
                    groups: [],
                    types: [selected.type],
                  },
                });
              }}
              onGroupChange={(items: any) => {
                const item = selections["upward"];

                getHeatMapData("upward", {
                  ...item,
                  // groups,
                  selected: {
                    ...item.selected,
                    // surveys: items,
                    groups: items,
                    types: [TYPES["360"]],
                  },
                });
              }}
            />
          </FilterLayout>
        </Div>
        <Div>
          <HeatMapTable
            columns={getColumnsData("upward")}
            values={getAllValues("upward")}
            rows={getRows("upward")}
            rowItem={(item: any) => getHeatMapDataTemplate(item, "upward")}
          />
        </Div>
      </Div>}
      {getRows("self").length > 0 && <Div>
        <Div>
          <FilterLayout title="Heat Map - Self Assessment">
            <AnalyticsFilters
              filters={selections["self"]}
              hideTypes
              config={{
                surveys: {
                  multiple: false,
                },
              }}
              onYearChange={(item: any) => {
                onTableLoad("self", item.key);
              }}
              onSurveyChange={(items: any) => {
                const item = selections["self"];
                getHeatMapData("self", {
                  ...item,
                  selected: {
                    ...item.selected,
                    surveys: items,
                  },
                });
              }}
            />
          </FilterLayout>
        </Div>
        <Div>
          <HeatMapTable
            columns={getColumnsData("self")}
            values={getAllValues("self")}
            rows={getRows("self")}
            rowItem={(item: any) => getHeatMapDataTemplate(item, "self")}
          />
        </Div>
      </Div>}
    </Layout.Container>
  );
};

Category.propTypes = {};

export default Category;
