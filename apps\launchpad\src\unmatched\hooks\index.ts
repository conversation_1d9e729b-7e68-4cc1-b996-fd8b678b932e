// import { hooks } from "@unmatchedoffl/ui-core";
// export { useFormik } from "formik";
import { useLocation } from "react-router";
export { useParams, useLocation } from "react-router";
export { useSelector, useDispatch } from "react-redux";
export { useState, useEffect, useCallback, useRef } from "react";
export { useMedia, useDebounce } from "react-use";
import { isEqual } from "lodash";
import { useSelector } from "react-redux";

export { default as useTable } from "./table";
export { default as useXHR } from "./server-state";
export { default as useLayout } from "./layout";

export const useCheckSelector = (selector: any) =>
  useSelector(selector, isEqual);

export const useQuery = () => {
  return new URLSearchParams(useLocation().search);
};

// export const { useTable, useXHR, useLayout, useCheckSelector } = hooks;
