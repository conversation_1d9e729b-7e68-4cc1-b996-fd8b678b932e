import React from "react";
import { Text } from "unmatched/components";

export default function Confidentiality() {
  return (
    <div className="pt-3 px-5">
      <div className="col-12 h100 white-bg p-5">
        <div className="up_re_title mb-5">
          <Text.H2 className="faded-blue b900 d-inline">
            Confidentiality
          </Text.H2>
        </div>
        <Text.H3 className="brownish-grey">
          <strong>Your Identity</strong>
        </Text.H3>
        <Text.P1 className="brownish-grey mt-3">
          <span className="faded-blue">
            <strong>Your identity is always protected</strong>
          </span>
          . All feedback provided on this secure website is received directly by
          Survey Research Associates and not by anyone at the firm.{" "}
          <span className="faded-blue">
            <strong>
              The upward feedback you give on the website will never be
              identified as coming from you – you will never be identified by
              name, gender, class year or practice group.
            </strong>
          </span>{" "}
          Written comments provided by associates will be compiled together and
          presented in the upward feedback report in the aggregate. Please note
          that the written comments are presented exactly as they are written,
          so distinguishing characteristics or details should not be used.
        </Text.P1>
        <Text.P1 className="brownish-grey mt-3">
          Survey Research Associates has pledged to the firm that all the
          feedback you give on the upward review website will be confidential
          and will never be identified by its source.
        </Text.P1>
        <Text.H3 className="brownish-grey mt-4">
          <strong>Security of the Website</strong>
        </Text.H3>
        <Text.P1 className="brownish-grey mt-3">
          Once you have logged on to the site, you are protected by Secure
          Sockets Layer (SSL) software, the industry standard for secure
          transactions online. Your feedback cannot be read as it travels over
          the Internet because it is encrypted. For added security, data is not
          stored on the SRA web server, but rather on a secure SQL server.
        </Text.P1>
      </div>
    </div>
  );
}
