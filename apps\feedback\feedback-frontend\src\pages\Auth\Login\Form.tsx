import React, { useState, useEffect } from "react";
import { Link, NavLink } from "react-router-dom";
import {
  Button,
  Div,
  Form,
  FormControl,
  FormGroup,
  Icon,
  Text,
} from "unmatched/components";
import appUrls from "unmatched/utils/urls/app-urls";
import { useFormik } from "unmatched/hooks";
import Loader from "../Loader";
import $icons from "assets/icons/icons";
import { ViewStates } from "./Login";
import styled from "styled-components";
import util from "unmatched/utils";

const { yup, getFieldErrorMessage, isFieldInvalid, isFieldValid } = util.formik;

const META_DATA = {
  // title: "Resend activation",
  content: "Enter the email address associated with your account",
  loadingTitle: "Check your mailbox",
  loadingContent:
    "Activation link has been emailed to you. Click the link in the email to activate your account",
};

const StyledIcon = styled(Icon)`
  position: absolute;
  right: 60px;
  margin-top: -23px;
`;

const LoginForm = (props: any) => {
  const {
    hasPassword,
    onSubmit,
    Feedback,
    submit,
    payload,
    incorrectPassword,
    clearAjaxError,
    loading,
    meta,
    updateLoginState,
    sentActivation,
    requestMagicLink,
    sentMagicLink,
    isAdmin,
    loginState,
    showSubmit = true,
    isPasswordSet = true,
    hasMagicLink = false,
  } = props;

  const [metas, setMetas] = useState(META_DATA);
  const [showPassword, setShowPassword] = useState(false);

  useEffect(() => {
    setMetas(meta);
  }, [meta]);

  let initialValues: any = { email: payload.email };
  let validationSchema: any = {
    email: yup
      .string()
      .email("Enter a valid email id")
      .required("Enter your email id"),
  };
  if (props.hasPassword) {
    initialValues = {
      ...initialValues,
      password: payload.password,
    };
    validationSchema = {
      ...validationSchema,
      password: yup.string().required("Enter your password"),
    };
  }
  const formik = useFormik({
    initialValues,
    validateOnChange: true,
    validateOnBlur: false,
    validationSchema: yup.object().shape(validationSchema),
    onSubmit: (values) => {
      onSubmit && onSubmit(values);
    },
  });

  const onPasswordChange = (e: any) => {
    if (incorrectPassword) {
      clearAjaxError();
    }
    formik.handleChange(e);
  };

  const inValidPassword =
    isFieldInvalid(formik, "password") || incorrectPassword;

  const onEmailBlur = (evt: any) => {
    formik.handleBlur(evt);
    if (updateLoginState) updateLoginState(evt);
  };

  const onEmailChange = (evt: any) => {
    formik.handleChange(evt);
    if (updateLoginState) updateLoginState(evt);
  };

  if (sentActivation || sentMagicLink) {
    const { CheckMail } = $icons;
    return (
      <Loader
        title={metas.loadingTitle}
        iconTemplate={<CheckMail />}
        content={metas.loadingContent}
      />
    );
  }

  const renderRequestMagicLink = () => {
    if (hasMagicLink && !isAdmin) {
      return (
        <NavLink
          to="#"
          onClick={(e) => {
            e.stopPropagation();
            requestMagicLink({ email: formik.values["email"] });
          }}
          className="fs-14 pt-1 bg-primary w-100 rounded text-white d-flex py-2"
        >
          <Div className="px-3 border-right">
            <Icon icon="far fa-link" />
          </Div>
          <Div className="flex-grow-1 text-center ">Passwordless login</Div>
        </NavLink>
      );
    }
  };

  const PasswordTextInput = !showPassword
    ? FormControl.Password
    : FormControl.Text;

  return (
    <Form onReset={formik.handleReset} onSubmit={formik.handleSubmit}>
      <Text.H2 className="text-primary pb-2">{meta.title}</Text.H2>
      <Text.P1 className="pb-2">{meta.content}</Text.P1>
      <Div className="pt-4 pb-3">
        <FormGroup>
          <FormGroup.Label>Email</FormGroup.Label>
          <FormControl.Email
            name="email"
            isInvalid={isFieldInvalid(formik, "email")}
            disabled={hasPassword}
            isValid={hasPassword}
            autoFocus
            value={formik.values["email"]}
            onBlur={onEmailBlur}
            onChange={onEmailChange}
            placeholder="Email address"
          />
          <FormGroup.InValidFeedback
            text={getFieldErrorMessage(formik, "email")}
          />
        </FormGroup>
        {hasPassword && isPasswordSet && (
          <FormGroup>
            <FormGroup.Label>Password</FormGroup.Label>
            <PasswordTextInput
              name="password"
              autoFocus
              value={formik.values?.password || ""}
              isInvalid={!!inValidPassword}
              isValid={isFieldValid(formik, "password")}
              onBlur={formik.handleBlur}
              onChange={onPasswordChange}
              placeholder="Enter password"
            />
            <Div onClick={() => setShowPassword((s) => !s)}>
              <StyledIcon className="fs-14" icon="fas fa-eye" />
            </Div>
            <FormGroup.InValidFeedback
              text={getFieldErrorMessage(formik, "password") || inValidPassword}
            />
          </FormGroup>
        )}
        {Feedback}
      </Div>
      <Div
        className={
          !hasPassword
            ? "pt-4 d-flex"
            : "d-flex justify-content-between align-items-end"
        }
      >
        {hasPassword && (
          <Div className="" style={{ minWidth: "160px" }}>
            <Link to={appUrls.auth.requestPassword} className="fs-14 pt-1">
              Forgot your password?
            </Link>
          </Div>
        )}
        {(showSubmit || loginState === ViewStates.IN_ACTIVE) && (
          <Div
            className={
              loginState === ViewStates.ACTIVE
                ? "d-inline"
                : "w-100 d-block text-right"
            }
          >
            <Button
              className="ml-auto"
              size="lg"
              disabled={loading}
              variant={submit.variant}
              type="submit"
            >
              {loading && <Icon icon="fal fa-circle-notch" spin />}{" "}
              {submit.label}
            </Button>
          </Div>
        )}
      </Div>
      {(loginState === ViewStates.IN_ACTIVE ||
        loginState === ViewStates.ACTIVE) &&
        hasMagicLink &&
        !isAdmin && (
          <Div className="d-flex flex-row align-items-center my-3">
            <Div className="border-top flex-grow-1" style={{ height: 0 }}></Div>
            <Div className="px-2 fs-12 text-dark">OR</Div>
            <Div className="border-top flex-grow-1" style={{ height: 0 }}></Div>
          </Div>
        )}
      {(loginState === ViewStates.IN_ACTIVE ||
        loginState === ViewStates.ACTIVE) &&
        !isAdmin &&
        renderRequestMagicLink()}
      {/* {!isAdmin && renderRequestMagicLink()}v2 */}
      {/* <Div
          className={
            !hasPassword
              ? "pt-4 d-flex"
              : "d-flex justify-content-between align-items-end"
          }
        ></Div> */}
    </Form>
  );
};

export default LoginForm;
