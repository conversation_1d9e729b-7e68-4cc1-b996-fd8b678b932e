import { Route, Routes } from "react-router";
import { Routes as AppRoutes } from "./pages/Routes";

// Define the route object type with recursive children
type RouteObject = {
  path: string;
  element: React.ReactNode;
  children?: RouteObject[];
};

// Create a recursive function to generate route elements
const createRouteElements = (routes: RouteObject[]) => {
  return routes.map((route, index) => (
    <Route
      key={index}
      path={route.path}
      element={route.element}
    >
      {route.children && route.children.length > 0 && 
        createRouteElements(route.children)
      }
    </Route>
  ));
};

function App() {
  return (
    <div className="app-container">
      <Routes>
        {createRouteElements(AppRoutes)}
      </Routes>
    </div>
  );
}

export default App;
