import React from "react";
import { Text, Div, Layout } from "unmatched/components";
import Header from "./Header";
import Suggestions from "./Suggestions";
import icons from "assets/icons/icons";
import CustomHeader from "pages/AdminApp/Shared/CustomHeader/CustomHeader";

const { Graph } = icons;

const PeopleList = () => (
  <Div>
    <CustomHeader
      title={<Text.H1 className="pb-2">People Analytics</Text.H1>}
      breadcrumbs={[
        {
          label: "Analytics",
          icon: <Graph className="grey-icon__svg" />,
        },
        { label: "People Analytics" },
      ]}
    />

    <Header className="px-4">
      <Text.H1 className="pb-3">People Analytics</Text.H1>
      <Text.H3 className="py-3">
        Enter a name, email id or employee ID in the search bar to view employee
        statistics.
      </Text.H3>
      <Layout.Row>
        <Layout.Col xl={4}>
          <Suggestions />
        </Layout.Col>
      </Layout.Row>
    </Header>
  </Div>
);

export default PeopleList;
