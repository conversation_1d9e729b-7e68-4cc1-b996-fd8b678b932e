export const AUTH_ENDPOINTS = {
  USERS: "/auth/users/",
  USER: (id: string) => `/auth/users/${id}/`,
};

export const STAFF_ENDPOINTS = {
  METADATA_LABELS: "https://alpha.unmatched.app/api/v2/staff/customers/metadata-labels/",
  REQUIRED_USER_FIELDS: "/staff/files/load/users/",
  ABSTRACT_USERS: "/staff/files/abstract-users/",
};

export const EMPLOYEE_ENDPOINTS = {
  LIST: AUTH_ENDPOINTS.USERS,
  METADATA: STAFF_ENDPOINTS.METADATA_LABELS,
  REQUIRED_FIELDS: STAFF_ENDPOINTS.REQUIRED_USER_FIELDS,
  SAMPLE_EMPLOYEE_DOWNLOAD: "staff/files/load/users/example/",
  DOWNLOAD_ALL_EMPLOYEES: "/auth/users/?f=xlsx",
  UPLOAD: "/staff/files/load/users/",
  UPDATE_UPLOAD: "/staff/files/users/",
};

export const API_ENDPOINTS = {
  AUTH: AUTH_ENDPOINTS,
  EMPLOYEE: EMPLOYEE_ENDPOINTS,
  STAFF: STAFF_ENDPOINTS,
};

export default API_ENDPOINTS;
