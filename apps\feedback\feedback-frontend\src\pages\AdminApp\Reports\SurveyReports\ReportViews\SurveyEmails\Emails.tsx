import React, { useCallback, useEffect, useState } from "react";
import {
  Div,
  Layout,
  Text,
  Card,
  Button,
  FormGroup,
  FormControl,
  Form,
  PageContainer,
  MultiSelect,
  RichEditor,
  Nav,
} from "unmatched/components";
import CustomHeader from "pages/AdminApp/Shared/CustomHeader/CustomHeader";
import { debounce } from "lodash";
import Confirmation from "unmatched/components/Confirmation";
import "../../../../Survey/ManageSurvey/Emails/components/styles.css";
import LoadingImg from "assets/images/Loading_2.gif";
import { components } from "react-select";
import appUrls from "unmatched/utils/urls/app-urls";
import icons from "assets/icons/icons";
import { CUSTOM_EMAIL_INSERT_OPTIONS } from "pages/AdminApp/Survey/ManageSurvey/manage-survey-meta";
import {
  getComputedValue,
  getComputedValueColorSub,
  getComputedValueText,
} from "pages/AdminApp/Survey/CreateSurvey/SendSurvey/SendSurvey";
import Logs from "pages/AdminApp/Survey/ManageSurvey/Emails/components/logs/Logs";
import { UMTab } from "unmatched/components/Tabs";

const { Paste } = icons;

export const userFriendlyTemplatesMap = {
  ENCRYPTED_REPORT: "Encrypted Report",
  REPORT_ACCESS_KEY: "Encrypted Access Key",
};

const Emails = (props: any) => {
  const [targetGroup, setTargetGroup] = useState("ENCRYPTED_REPORT");
  const [showConfirm, setShowConfirm] = useState(false);
  // const [showSendCustomModal, setShowSendCustomModal] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [filter, setFilter] = useState("emails");

  const templates = [
    // "INACTIVE_ALL", // REMINDER_INACTIVE
    // "ACTIVE_INCOMPLETE", // REMINDER_ACTIVE
    // "ACTIVE_PARTIAL", // REMINDER_ACTIVE
    // "ACTIVE_COMPLETE", // REMINDER_ACTIVE
    // "SURVEY_INVITATION", // SURVEY_INVITATION
    // "SPECIAL", // SPECIAL
    "ENCRYPTED_REPORT",
    "REPORT_ACCESS_KEY",
  ];

  const targetGroupsMap = {
    ENCRYPTED_REPORT: "ENCRYPTED_REPORT",
    REPORT_ACCESS_KEY: "REPORT_ACCESS_KEY",
  };
  // meta
  const { survey, layout } = props;
  let richTextRef: any = null;
  let subRichTextRef: any = null;

  const setRef = (ref: any) => {
    richTextRef = ref;
  };

  const setSubRef = (ref: any) => {
    subRichTextRef = ref;
  };

  const sendEmailReqBody = {
    survey_index: props.indexId,
    is_sandbox: false,
  };

  // const sendEmail = (template: string, tGroup: string, showToast?: boolean) => {
  //   const reqData = { ...sendEmailReqBody };
  //   (reqData as any).target_group = tGroup;
  //   (reqData as any).template_name = template;
  //   return props.sendEmailReminder(reqData, showToast);
  // };

  // const sendMockCustomEmail = (template: string, raterUsers: any) => {
  //   const reqData = { ...sendEmailReqBody };
  //   (reqData as any).template_name = template;
  //   (reqData as any).rater_users = raterUsers;

  //   props.sendCustomEmailReminder(reqData, true);
  // };

  // const sendCustomEmailTemplate = (
  //   tGroup: string,
  //   raterUsers: any,
  //   showToast?: boolean
  // ) => {
  //   const reqData = { ...sendEmailReqBody };
  //   (reqData as any).template_name = "CUSTOM";
  //   (reqData as any).rater_users = tGroup === "CUSTOM" ? raterUsers : [];
  //   if (tGroup !== "CUSTOM") {
  //     (reqData as any).target_group = tGroup;
  //   }
  //   return props.sendCustomEmailReminder(reqData, showToast);
  // };

  const sendMockEmail = () => {
    const reqData = { ...sendEmailReqBody };
    reqData.is_sandbox = true;
    props.sendEmailReminder(reqData);
  };

  // const getRecipientsCount = async (tGroup: any) => {
  //   const reqData = { ...sendEmailReqBody };
  //   reqData.is_sandbox = true;
  //   if (tGroup) {
  //     (reqData as any).target_group = tGroup;
  //   }
  //   const result = await props.sendEmailReminder(reqData);
  //   return { recipient: tGroup, count: result?.data?.count };
  // };

  // const getRecipientsCountsArr = async (recipients: any) => {
  //   return await Promise.all(
  //     recipients.map((r: any) => {
  //       return getRecipientsCount(r.value);
  //     })
  //   );
  // };

  // const getComputedUsers = (users: any) => {
  //   const usersData = users.map((u: any) => ({
  //     ...u,
  //     label: u.email,
  //     value: u.emp_id,
  //   }));
  //   return usersData;
  // };

  // debounce on subject change start

  const onSubjectChange = () => {
    // props.updateEmailData(
    //   {
    //     ...props.emailData,
    //   },
    //   false,
    //   setIsSaving
    // );
  };

  const delayedSubChange = useCallback(debounce(onSubjectChange, 1000), [
    props.emailData?.subject,
  ]);

  useEffect(() => {
    delayedSubChange();
    return delayedSubChange.cancel;
  }, [props.emailData?.subject, delayedSubChange]);

  // debounce on subject change end

  // const getTemplate = (name: any) => {
  //   const template = props.templates.find((r: any) => r.label === name);
  //   return template;
  // };

  const TemplateOption = (props: any) => {
    return (
      <div className="px-2 py-1">
        <components.Option {...props} className="rounded pt-1 pb-2">
          <FormGroup className="m-0 d-flex align-items-center">
            <Text.P1
              style={{ color: "#000", fontSize: 14 }}
              className="pt-1 cursor-pointer"
            >
              {props.label}
            </Text.P1>
          </FormGroup>
        </components.Option>
      </div>
    );
  };

  const getUpdateEmailTemplate = () => {
    return (
      <Card className="mb-4" noShadow>
        <Card.Header className="pl-3 py-2 d-flex justify-content-between">
          <Text.H3>Email Templates</Text.H3>
          {/* <span className="mr-4 fs-14">{isSaving ? "Saving..." : "Saved"}</span> */}
        </Card.Header>
        <Div className="pl-3 py-2">
          <Div className="pr-4 pt-2">
            <Form>
              <Div className="d-flex justify-content-between align-items-center">
                <Div style={{ width: 230 }}>
                  <FormGroup>
                    <FormGroup.Label className="pb-2">
                      Choose Template:
                    </FormGroup.Label>
                    <MultiSelect
                      CustomOption={TemplateOption}
                      options={templates.map((tg: any) => ({
                        value: tg,
                        label: (userFriendlyTemplatesMap as any)[tg],
                      }))}
                      isMulti={false}
                      closeMenuOnSelect
                      value={{
                        value: targetGroup,
                        label: (userFriendlyTemplatesMap as any)[targetGroup],
                      }}
                      onSelect={(selected: any) => {
                        setTargetGroup(selected.value);
                        const template = props.templates.find(
                          (r: any) =>
                            r.label === (targetGroupsMap as any)[selected.value]
                        );
                        if (template) {
                          props.setEmailData(template);
                          const quillRef = richTextRef.getEditor();
                          const subQuillRef = subRichTextRef.getEditor();
                          // https://stackoverflow.com/questions/46626633/how-do-you-insert-html-into-a-quilljs
                          setTimeout(() => {
                            quillRef.clipboard.dangerouslyPasteHTML(
                              getComputedValue(template.body, true)
                            );

                            subQuillRef.clipboard.dangerouslyPasteHTML(
                              getComputedValueColorSub(
                                getComputedValue(template.subject, true)
                              )
                            );
                          }, 100);
                        }
                      }}
                    />
                  </FormGroup>
                </Div>

                <Div>
                  <Button
                    onClick={() => setShowConfirm(true)}
                    variant="outline-primary"
                    loading={isSaving}
                  >
                    {isSaving && <img width={25} src={LoadingImg} />} Save
                    Template
                  </Button>
                </Div>
              </Div>

              <FormGroup>
                <FormGroup.Label>Email Subject</FormGroup.Label>
                <Div className="survey-quill-subject">
                  <RichEditor
                    onChange={(html: string, text: string) => {
                      props.setEmailData((eD: any) => ({
                        ...eD,
                        subject: getComputedValueText(text, false),
                      }));
                      // delayedSubChange();
                    }}
                    value={getComputedValueColorSub(
                      getComputedValue(props.emailData?.subject, true)
                    )}
                    insertOptions={CUSTOM_EMAIL_INSERT_OPTIONS.filter(
                      (el: any) => [3, 4, 7, 8, 9].includes(el.id)
                    )}
                    setRef={setSubRef}
                  />
                </Div>
              </FormGroup>
              <FormGroup>
                <FormGroup.Label>Email Body</FormGroup.Label>
                <Div className="survey-quill">
                  <RichEditor
                    onChange={(html: string, text: string) => {
                      props.setEmailData((eD: any) => ({
                        ...eD,
                        body: getComputedValue(html, false),
                        text: getComputedValueText(text, false),
                      }));
                    }}
                    value={getComputedValue(props.emailData?.body, true)}
                    insertOptions={CUSTOM_EMAIL_INSERT_OPTIONS.filter(
                      (el: any) => [3, 4, 7, 8].includes(el.id)
                    )}
                    setRef={setRef}
                  />
                  <Div>
                    <FormControl.Textarea
                      className="email-body-foot"
                      disabled
                      value={`This is an automated email. Please do not respond to this email.
If you have any technical issues, please contact our support <NAME_EMAIL>

Unmatched by Survey Research Associates
Professional Development | People Management | Employee Engagement software`}
                      rows={5}
                      style={{ color: "#6D6D6D", fontSize: 14 }}
                    />
                  </Div>
                </Div>
              </FormGroup>
            </Form>
          </Div>
        </Div>
        {false && (
          <Card.Footer className="mr-4">
            <Button
              onClick={sendMockEmail}
              variant="outline-primary"
              className="mr-3"
            >
              Mock Send
            </Button>
            <Button
              onClick={() => setShowConfirm(true)}
              variant="outline-primary"
            >
              Send Email
            </Button>
          </Card.Footer>
        )}
      </Card>
    );
  };

  const onFilterChange = (_filter: string) => {
    setFilter(_filter);
  };

  const getNavItem = (title: string, key: string) => {
    return <UMTab
      eventKey={key}
      activeKey={filter}
      onClick={() => onFilterChange(key)}
    >
      {title}
    </UMTab>;
  };

  return (
    <Div>
      <CustomHeader
        style={{ marginLeft: layout.marginLeft, height: 107 }}
        title={
          <Div>
            <Text.H1 className="pb-2">{survey.name}</Text.H1>
            <Div className="sticky-tabs-container">
              <Nav className="nav-tabs sticky">
                {getNavItem("Emails", "emails")}
                {getNavItem("Logs", "logs")}
              </Nav>
            </Div>
          </Div>
        }
        breadcrumbs={[
          {
            label: "Surveys",
            icon: <Paste className="grey-icon__svg" />,
            route: appUrls.admin.survey.default,
          },
          { label: "Emails" },
          { label: survey.name },
        ]}
      />

      <PageContainer className="pt-4">
        <Div className="pt-2">
          {filter === "logs" ? (
            <Logs
              indexId={props.indexId}
              dedicatedTemplates="ENCRYPTED_REPORT,REPORT_ACCESS_KEY"
            />
          ) : (
            <Layout.Container fluid className="pt-3">
              {/* {getmanualRemaindersTemplate()} */}
              {getUpdateEmailTemplate()}
            </Layout.Container>
          )}
        </Div>
      </PageContainer>
      <Confirmation
        confirmText="Are you sure you want to save template?"
        show={showConfirm}
        onOk={async () => {
          await props.updateEmailData(
            {
              ...props.emailData,
            },
            false,
            setIsSaving,
            true
          );
        }}
        setShow={setShowConfirm}
      />
    </Div>
  );
};

export default Emails;
