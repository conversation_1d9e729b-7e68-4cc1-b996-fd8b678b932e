import { Div } from "@unmatchedoffl/ui-core";

export const UMTab = (props: any) => {
  const { eventKey, onClick, activeKey, children, styles = {} } = props;
  return (
    <Div
      as={Div}
      eventKey={eventKey}
      onClick={onClick}
      className={`simple-tabs-text ${eventKey === activeKey && "active"}`}
      style={{
        cursor: "pointer",
        padding: "0 10px",
        fontSize: 14,
        ...styles,
        ...(eventKey !== activeKey && { opacity: 0.5 }),
      }}
    >
      {children}
    </Div>
  );
};
