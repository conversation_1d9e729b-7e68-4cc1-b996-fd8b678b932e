import React from "react";
import { Table, Text, FormControl, Layout } from "unmatched/components";
import util from "unmatched/utils";
import { useTable } from "unmatched/hooks";
// import useFilters from "./filter-hook";
// import JSON_DATA from "./people-meta";
// import PeopleFilters from "./PeopleFilters";
import TableContainer from "./TableContainer";
// import { getPeopleAnalyticsFromId } from "../../analytics-api";
import { Link } from "react-router-dom";
import { getOverallRankingsFact } from "../analytics-api";
import AnalyticsFilters from "../Shared/AnalyticsFilters/AnalyticsFilters";
import useAnalyticsFilters from "../Shared/AnalyticsFilters/hook";
// import { util } from "prettier";
// import PropTypes from 'prop-types'

interface User {
  key: string;
  overallAverage: string;
  selfAverage: string;
  categoryAverage: string;
  frequency: string;
  target: {
    id: string;
    empId: string;
    firstName: string;
    lastName: string;
    email: string;
    metadata: any;
  };
  index: {
    id: string;
    title: string;
  };
}
// interface Request {
//   page?: number;
// }

const getSurveys = (_filters: any) => {
  const hasGroups = _filters.selected.groups.length;
  return {
    groups: _filters.selected.groups,
    surveys: hasGroups ? [] : _filters.selected.surveys,
    type: hasGroups ? ["surveyindex360"] : _filters.selected.types || [],
  };
};

const Rankings = () => {
  // const { id } = props;
  // const filters = useFilters();
  const tableMeta = useTable({
    page: 1,
    size: 10,
    totalPages: 0,
    search: "",
    sort: "",
  });
  const [users, setUsers] = React.useState<Array<User>>([]);
  const filters = useAnalyticsFilters();

  const [columns, setColumns]: any = React.useState({
    emp_id: { label: "Emp ID", sortValue: "", hasSort: true, order: 1 },
    name: {
      label: "Name",
      sortValue: "",
      hasSort: true,
      order: 2,
      pushDynamic: true,
    },
    overall_avg: {
      label: "Overall Avg",
      sortValue: "",
      hasSort: true,
      order: 3,
      // pushDynamic: true,
    },
    self_avg: {
      label: "Self Avg",
      sortValue: "",
      hasSort: true,
      order: 4,
      // pushDynamic: true,
    },
    frequency: {
      label: "Number of ratings",
      sortValue: "",
      hasSort: true,
      order: 5,
      // pushDynamic: true,
    },
  });

  const [dynamicColumns, setDynamicColumns] = React.useState({});

  React.useEffect(() => {
    onTableLoad();
    //eslint-disable-next-line
  }, []);

  const onTableLoad = (year?: any) => {
    filters
      .getFilters({
        year,
      })
      .then(
        (_filters: any) => {
          const [selectedSurvey] = _filters.surveys || [];
          getPeopleAnalytics(
            {},
            {
              ..._filters,
              surveys: _filters.surveys.filter(
                (item: any) => selectedSurvey?.type === item.type
              ),
              selected: {
                ..._filters.selected,
                surveys:
                  selectedSurvey && selectedSurvey.id
                    ? [selectedSurvey.id]
                    : [],
                types: selectedSurvey ? [selectedSurvey.type] : [],
                groups: [],
                // groups: params?.groups || _filters.selected.groups,
              },
            }
          );
        },
        (err: any) => {
          console.log(err);
          tableMeta.setLoading(false);
        }
      );
  };

  const getPeopleAnalytics = ({ page, search, sort }: any, _filters?: any) => {
    if (!_filters.isDownload) {
      tableMeta.setLoading(true);
    }
    return getOverallRankingsFact(
      {
        sort: sort || tableMeta.sort,
        search,
        page: page ?? 1,
        year: _filters.selected.years,
        isDownload: _filters.isDownload,
        // type: ,
        ...getSurveys(_filters),
      },
      _filters.meta
    ).then(
      (peopleAnalytics: any) => {
        if (_filters.isDownload) {
          return;
        }
        filters.setFilters(_filters);
        tableMeta.updatePagination({
          totalPages: peopleAnalytics.data.totalPages,
          totalItems: peopleAnalytics.data.totalElements
        });
        tableMeta.setSort(sort || tableMeta.sort);
        tableMeta.setLoading(false);
        setUsers(peopleAnalytics.data.results);
        setDynamicColumns((_columns: any) => {
          let output = {};
          peopleAnalytics.columns.forEach((item: any) => {
            output = {
              ...output,
              [item.key]: {
                label: item.label,
                hasSort: true,
                isDynamic: true,
                sortValue: "",
                // pushDynamic: true,
              },
            };
          });
          return {
            ..._columns,
            ...output,
          };
        });
      },
      (err: any) => {
        console.log(err);
        tableMeta.setLoading(false);
      }
    );
  };

  const onSearch = (search: any) => {
    getPeopleAnalytics({ search }, filters);
  };

  const getSearchInput = () => {
    return (
      <FormControl.Search
        placeholder="Search for name, id"
        size="md"
        className="bg-white w-100"
        // style={{ width: "auto" }}
        value={tableMeta.search}
        onChange={(evt: any) => tableMeta.setSearch(evt.target.value)}
        onSearch={onSearch}
      />
    );
  };

  const getFiltersTemplate = () => {
    return (
      <>
        <Layout.Row>
          <Layout.Col>
            <AnalyticsFilters
              filters={filters}
              hideGroups
              config={{
                surveys: {
                  multiple: false,
                },
                layout: {
                  year: {
                    xl: 2,
                    md: 4,
                    sm: 6,
                  },
                  type: {
                    xl: 3,
                    md: 4,
                    sm: 6,
                  },
                  survey: {
                    xl: 3,
                    md: 4,
                    sm: 6,
                  },
                  group: {
                    xl: 3,
                    md: 4,
                    sm: 6,
                  },
                },
              }}
              onYearChange={(item: any) => {
                onTableLoad(item.key);
              }}
              onTypeChange={(item: any) => {
                const surveys =
                  filters.surveysData.filter((_survey: any) => {
                    if (
                      _survey.type === "surveyindex360group" &&
                      item.key === "surveyindex360"
                    )
                      return true;
                    return _survey.type === item.key;
                  }) || [];
                const [selectedSurvey]: any = surveys || {};
                const groups: any = selectedSurvey.rater_groups?.map(
                  (group: any) => ({
                    id: group.id,
                    key: group.id,
                    title: group.title,
                    groupId: selectedSurvey.id,
                    groupKey: group["rater_group"],
                  })
                );
                getPeopleAnalytics(
                  {},
                  {
                    ...filters,
                    groups,
                    selected: {
                      ...filters.selected,
                      types: [
                        item.key === "surveyindex360"
                          ? "surveyindex360group"
                          : item.key,
                      ],
                      surveys:
                        selectedSurvey && selectedSurvey.id
                          ? [selectedSurvey.id]
                          : [],
                      groups: [],
                    },
                  }
                );
              }}
              onSurveyChange={(items: any) => {
                const selectedSurvey: any =
                  filters.surveys.find((item: any) =>
                    items.includes(item.id)
                  ) || {};
                const groups: any = selectedSurvey.rater_groups?.map(
                  (group: any) => ({
                    id: group.id,
                    key: group.id,
                    title: group.title,
                    groupId: selectedSurvey.id,
                    groupKey: group["rater_group"],
                  })
                );
                getPeopleAnalytics(
                  {},
                  {
                    ...filters,
                    groups,
                    selected: {
                      ...filters.selected,
                      surveys: items,
                      groups: [],
                    },
                  }
                );
              }}
              onGroupChange={(items: any) => {
                getPeopleAnalytics(
                  {},
                  {
                    ...filters,
                    selected: {
                      ...filters.selected,
                      groups: util.lib.compact(items),
                    },
                  }
                );
              }}
            />
          </Layout.Col>
        </Layout.Row>
        <Layout.Row>
          <Layout.Col xl={8} md={8} sm={8} xs={0} className="mt-1"></Layout.Col>
          <Layout.Col
            style={{ display: "flex", justifyContent: "end" }}
            xl={4}
            md={4}
            sm={4}
            xs={12}
            className="mt-1"
          >
            {getSearchInput()}
          </Layout.Col>
        </Layout.Row>
      </>
    );
  };

  const getColumnsData = () => {
    const columnsArray = tableMeta.getColumnsArray(columns);
    const [userInfo, avgs] = tableMeta.splitColumns(columnsArray);
    const output = [
      ...userInfo,
      ...tableMeta.getColumnsArray(dynamicColumns),
      ...avgs,
    ];
    return output;
  };

  const getRowsTemplate = () => {
    return users.map((item: any, index: number) => {
      const isEven = index % 2 === 0 || index === 0;
      return (
        <Table.Row even={!isEven} key={item.empId}>
          {/* <Table.Data width="70px">
            <Text.P1>{index + 1}.</Text.P1>
          </Table.Data> */}
          <Table.Data>
            <Text.P1>{item.empId}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>
              <Link
                to={util.appUrls.admin.analytics.people.getAnalyticsUrl(
                  item.id
                )}
              >
                {item.name}
                {/* {item.target.firstName} {item.target.lastName} */}
              </Link>
            </Text.P1>
          </Table.Data>
          {tableMeta.getColumnsArray(dynamicColumns).map(({ key }: any) => {
            return (
              <Table.Data key={key}>
                <Text.P1>
                  {item[key] || (
                    <span className="text-muted">
                      <i>Unavailable</i>
                    </span>
                  )}
                </Text.P1>
              </Table.Data>
            );
          })}
          {/* <Table.Data>
            <Text.P1>
              {item.title || (
                <span className="text-muted">
                  <i>Unavailable</i>
                </span>
              )}
            </Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>
              {item.department ?? (
                <span className="text-muted">
                  <i>Unavailable</i>
                </span>
              )}
            </Text.P1>
          </Table.Data> */}
          <Table.Data>
            <Text.P1>{item.overallAverage || "-"}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.selfAverage || "-"}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.frequency || "-"}</Text.P1>
          </Table.Data>
        </Table.Row>
      );
    });
  };

  const onPageSelect = async (page: number) => {
    tableMeta.updatePagination({ page });
    await getPeopleAnalytics({ page }, filters);
  };

  if (!users?.length) return null;

  return (
    <TableContainer
      title={"Overall Ranking list"}
      filters={getFiltersTemplate()}
      download={() => getPeopleAnalytics({}, { isDownload: true, ...filters })}
    >
      <Table
        tableClass="bg-white"
        columns={getColumnsData()}
        isLoading={tableMeta.isLoading}
        rows={users}
        customRows
        render={() => getRowsTemplate()}
        onSort={(item: any) => {
          const sortParam = util.label.getSortingLabel(
            item.key,
            item.sortValue
          );
          getPeopleAnalytics(
            {
              // page: tableMeta.page || 1,
              sort: sortParam,
            },
            filters
          ).then(() => {
            if (item.isDynamic) {
              setDynamicColumns((_columns: any) => {
                return tableMeta.resetColumns(_columns, item);
              });
            } else {
              setColumns((_columns: any) => {
                return tableMeta.resetColumns(_columns, item) || {};
              });
            }
          });
        }}
        hasPagination
        activePage={tableMeta.page}
        pages={tableMeta.totalPages}
        onPageSelect={onPageSelect}
        notFoundMsg={util.noSearchRecordsFoundMsg}
        size={tableMeta.size}
        totalItems={tableMeta.totalItems}
      />
    </TableContainer>
  );
};

// Rankings.propTypes = {

// }

export default Rankings;
