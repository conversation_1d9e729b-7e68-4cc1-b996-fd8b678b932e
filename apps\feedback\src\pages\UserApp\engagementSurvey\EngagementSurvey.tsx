import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router';
import { Button } from '@repo/ui/components/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@repo/ui/components/card';
import { Progress } from '@repo/ui/components/progress';
import { Skeleton } from '@repo/ui/components/skeleton';
import { Alert, AlertDescription } from '@repo/ui/components/alert';
import { Badge } from '@repo/ui/components/badge';
import { USER_ROUTES } from '@/app.routes';
import axiosInstance from '@/lib/axios';

interface SurveyMeta {
  title: string;
  endDate: string;
  canSubmit: boolean;
  nextTitle: string;
  buttonType: string;
  hideBack: boolean;
}

interface Section {
  id: number;
  title: string;
  completed: boolean;
}

interface Question {
  id: number;
  title: string;
  type: string;
  feedback?: string;
  hasFeedback: boolean;
}

const EngagementSurvey: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [meta, setMeta] = useState<SurveyMeta | null>(null);
  const [sections, setSections] = useState<Section[]>([]);
  const [currentSection, setCurrentSection] = useState<Section | null>(null);
  const [questions, setQuestions] = useState<Question[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [responseId, setResponseId] = useState<string>('');
  const [completion, setCompletion] = useState(0);
  const [confirmSubmit, setConfirmSubmit] = useState(false);
  const [questionResponses, setQuestionResponses] = useState<Record<string, any>>({});

  useEffect(() => {
    if (id) {
      fetchSurveyData();
    }
  }, [id]);

  const fetchSurveyData = async () => {
    try {
      setIsLoading(true);

      // First get the survey version ID from the survey index ID (like legacy app)
      console.log('Getting survey version for index:', id);
      const surveyInfoResponse = await axiosInstance.get(`/survey/index/${id}/`);
      console.log('Survey info response:', surveyInfoResponse.data);

      // Extract version ID from the surveys array
      const surveys = surveyInfoResponse.data.surveys;
      if (!surveys || surveys.length === 0) {
        throw new Error('No survey versions found');
      }

      const versionId = surveys[0].id; // Use first version
      console.log('Using version ID:', versionId);

      // Now create survey response using the version ID (like legacy app)
      const response = await axiosInstance.post('/survey/survey-response/', {
        survey: versionId,
        resourcetype: 'SurveyResponseEngagement'
      });

      console.log('Engagement survey response:', response.data);

      // Use actual response data from API
      const surveyData = response.data;
      const surveyDetail = surveyData.survey_detail;
      const indexData = surveyData.index;

      // Extract sections and questions from the actual API response
      const apiSections = surveyDetail.sections.map((section: any) => ({
        id: section.id,
        title: section.name,
        completed: false // TODO: Calculate based on question responses
      }));

      // Extract questions from all sections
      const allQuestions = surveyDetail.sections.flatMap((section: any) =>
        section.components.map((component: any) => ({
          id: component.id,
          title: component.label,
          type: component.type,
          hasFeedback: component.collect_feedback,
          mandatory: component.mandatory,
          options: component.options,
          sectionId: section.id,
          resourcetype: component.resourcetype
        }))
      );

      const surveyMeta = {
        title: indexData.title,
        endDate: new Date(indexData.deadline).toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        }),
        canSubmit: false,
        nextTitle: 'Next',
        buttonType: 'primary',
        hideBack: false
      };

      setMeta(surveyMeta);
      setSections(apiSections);
      setCurrentSection(apiSections[0]);
      setQuestions(allQuestions);
      setResponseId(surveyData.id);
      setCompletion(0); // TODO: Calculate based on answered questions
      setError(null);
    } catch (err) {
      console.error('Error fetching survey data:', err);
      setError('Failed to load survey data');
    } finally {
      setIsLoading(false);
    }
  };

  const handleNext = () => {
    if (!currentSection || !sections) return;

    const currentIndex = sections.findIndex(s => s.id === currentSection.id);
    if (currentIndex < sections.length - 1) {
      setCurrentSection(sections[currentIndex + 1]);
    } else if (meta?.canSubmit) {
      setConfirmSubmit(true);
    }
  };

  const handleBack = () => {
    if (!currentSection || !sections) return;

    const currentIndex = sections.findIndex(s => s.id === currentSection.id);
    if (currentIndex > 0) {
      setCurrentSection(sections[currentIndex - 1]);
    }
  };

  const handleSubmit = async () => {
    try {
      setIsSaving(true);
      // Submit survey using the response ID from the survey response creation
      await axiosInstance.post(`/survey/survey-response/${responseId}/submit/`);
      navigate(USER_ROUTES().dashboard.surveyList);
    } catch (err) {
      console.error('Error submitting survey:', err);
      setError('Failed to submit survey');
    } finally {
      setIsSaving(false);
    }
  };

  const handleSectionSelect = (section: Section) => {
    setCurrentSection(section);
    setConfirmSubmit(false);
  };

  const handleQuestionResponse = (questionId: string, value: any) => {
    setQuestionResponses(prev => ({
      ...prev,
      [questionId]: value
    }));

    // Calculate completion percentage
    const totalQuestions = questions.length;
    const answeredQuestions = Object.keys({...questionResponses, [questionId]: value}).length;
    const newCompletion = Math.round((answeredQuestions / totalQuestions) * 100);
    setCompletion(newCompletion);
  };

  if (error) {
    return (
      <div className="p-6">
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  if (confirmSubmit) {
    return (
      <div className="min-h-screen flex items-center justify-center p-6">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Submit Survey</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p>Are you ready to submit your responses?</p>
            <div className="flex gap-2">
              <Button 
                variant="outline" 
                onClick={() => setConfirmSubmit(false)}
                className="flex-1"
              >
                Back
              </Button>
              <Button 
                onClick={handleSubmit}
                disabled={isSaving}
                className="flex-1"
              >
                {isSaving ? 'Submitting...' : 'Submit'}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Left Sidebar - matches legacy app */}
      <div className="w-64 bg-white border-r border-gray-200 flex flex-col">
        {/* Back button */}
        <div className="p-4 border-b border-gray-200">
          <Button
            variant="ghost"
            onClick={() => navigate(USER_ROUTES().dashboard.surveyList)}
            className="text-blue-600 hover:text-blue-700 p-0 h-auto font-normal"
          >
            ← Back
          </Button>
        </div>

        {/* Survey Info */}
        <div className="p-4 space-y-4">
          <div>
            <h3 className="text-sm font-medium text-gray-700 mb-1">Survey Ends On</h3>
            <p className="text-sm text-gray-900">
              {isLoading ? <Skeleton className="h-4 w-32" /> : meta?.endDate || 'Mar 24, 2026, 08:23 PM'}
            </p>
          </div>

          <div>
            <h3 className="text-sm font-medium text-gray-700 mb-1">Survey Status</h3>
            <div className="space-y-1">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Questions</span>
                <span className="font-medium">{questions.length}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Completed</span>
                <span className="font-medium">{Math.round((completion / 100) * questions.length)}</span>
              </div>
            </div>
          </div>

          <div>
            <h3 className="text-sm font-medium text-gray-700 mb-2">Categories</h3>
            <div className="space-y-1">
              {isLoading ? (
                Array(3).fill(0).map((_, index) => (
                  <Skeleton key={index} className="h-8 w-full" />
                ))
              ) : (
                sections.map((section) => (
                  <button
                    key={section.id}
                    onClick={() => handleSectionSelect(section)}
                    className={`w-full text-left px-3 py-2 text-sm rounded ${
                      currentSection?.id === section.id
                        ? 'bg-blue-100 text-blue-700 font-medium'
                        : 'text-gray-600 hover:bg-gray-50'
                    }`}
                  >
                    {section.title}
                    <div className="text-xs text-gray-500 mt-1">
                      {section.completed
                        ? 'Completed'
                        : `${questions.filter(q => q.sectionId === section.id).length} questions left`
                      }
                    </div>
                  </button>
                ))
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col">
        {/* Header - matches legacy app lighter blue header */}
        <div className="bg-blue-300 text-gray-800 px-6 py-3">
          <div className="flex justify-between items-center">
            <h1 className="text-lg font-medium">
              {isLoading ? <Skeleton className="h-6 w-64 bg-blue-200" /> : meta?.title || 'Test Engagement report - 2'}
            </h1>
            <div className="flex items-center space-x-6 text-sm">
              <span>{completion}% Completed</span>
              <span>Saved Jun 03, 2025, 07:36 PM</span>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 bg-gray-50 p-6">
          {isLoading ? (
            <div className="space-y-6">
              <Skeleton className="h-8 w-3/4" />
              <Skeleton className="h-32 w-full" />
              <Skeleton className="h-32 w-full" />
            </div>
          ) : (
            <div className="max-w-4xl">
              {/* White container to match legacy app */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h2 className="text-lg font-medium mb-6">{currentSection?.title || 'Section 1'}</h2>

                {/* Actual Questions from API - matches legacy app design */}
                <div className="space-y-8">
                {questions
                  .filter(q => q.sectionId === currentSection?.id)
                  .map((question, index) => (
                    <div key={question.id} className="space-y-4">
                      <h3 className="font-medium">{index + 1}. {question.title}</h3>

                      {question.type === 'Rating' && (
                        <div className="flex items-start space-x-8">
                          {/* Render rating scale from 5 to 1, then 0 for "No basis" - matches legacy layout */}
                          {[5, 4, 3, 2, 1, 0].map((value) => (
                            <div key={value} className="flex flex-col items-center space-y-1">
                              <div className="font-medium text-lg">{value}</div>
                              <input
                                type="radio"
                                name={`question_${question.id}`}
                                value={value}
                                checked={questionResponses[question.id] === value}
                                onChange={() => handleQuestionResponse(question.id, value)}
                                className="w-4 h-4 cursor-pointer"
                              />
                              <div className="text-xs text-gray-600 text-center max-w-16">
                                {value === 0 ? 'No basis for rating' : question.options?.[value] || `Scale - ${value}`}
                              </div>
                            </div>
                          ))}
                        </div>
                      )}

                      {/* Show feedback input if collect_feedback is true */}
                      {question.hasFeedback && (
                        <div className="mt-4">
                          <textarea
                            placeholder="Optional feedback..."
                            className="w-full p-3 border border-gray-300 rounded-md resize-none"
                            rows={3}
                          />
                        </div>
                      )}
                    </div>
                  ))}

                {/* Show remaining questions indicator - matches legacy app */}
                <div className="text-center py-6">
                  <div className="text-gray-600 text-sm">
                    {questions.filter(q => q.sectionId === currentSection?.id).length - 3 > 0
                      ? `${questions.filter(q => q.sectionId === currentSection?.id).length - 3} Questions left in the section`
                      : ''
                    }
                  </div>
                </div>

                {/* Submit Button - matches legacy app positioning */}
                <div className="mt-8 flex justify-end">
                  <Button
                    onClick={handleSubmit}
                    disabled={isSaving}
                    className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded"
                  >
                    {isSaving ? 'Submitting...' : 'Submit my response'}
                  </Button>
                </div>

                {/* Footer - matches legacy app */}
                <div className="mt-8 pt-6 border-t border-gray-200 text-center text-xs text-gray-500">
                  <div className="flex justify-center items-center space-x-4">
                    <a href="#" className="hover:text-gray-700">Privacy Policy</a>
                    <span>•</span>
                    <a href="#" className="hover:text-gray-700">Terms</a>
                    <span>•</span>
                    <span className="font-medium">unmatched</span>
                    <span>Employee well-being. Engagement. Upliftment. Culture.</span>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Feedback Widget - matches legacy app */}
      <div className="fixed right-0 top-1/2 transform -translate-y-1/2 z-50">
        <div className="bg-pink-500 text-white px-2 py-8 rounded-l-lg shadow-lg cursor-pointer hover:bg-pink-600 transition-colors">
          <div className="transform -rotate-90 text-sm font-medium whitespace-nowrap">
            Feedback
          </div>
        </div>
      </div>
    </div>
  );
};

export default EngagementSurvey;
