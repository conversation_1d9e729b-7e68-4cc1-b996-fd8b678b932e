import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router';
import { Button } from '@repo/ui/components/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@repo/ui/components/card';
import { Progress } from '@repo/ui/components/progress';
import { Skeleton } from '@repo/ui/components/skeleton';
import { Alert, AlertDescription } from '@repo/ui/components/alert';
import { Badge } from '@repo/ui/components/badge';
import { USER_ROUTES } from '@/app.routes';
import axiosInstance from '@/lib/axios';

interface SurveyMeta {
  title: string;
  endDate: string;
  canSubmit: boolean;
  nextTitle: string;
  buttonType: string;
  hideBack: boolean;
}

interface Section {
  id: number;
  title: string;
  completed: boolean;
}

interface Question {
  id: number;
  title: string;
  type: string;
  feedback?: string;
  hasFeedback: boolean;
}

const EngagementSurvey: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [meta, setMeta] = useState<SurveyMeta | null>(null);
  const [sections, setSections] = useState<Section[]>([]);
  const [currentSection, setCurrentSection] = useState<Section | null>(null);
  const [questions, setQuestions] = useState<Question[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [responseId, setResponseId] = useState<string>('');
  const [completion, setCompletion] = useState(0);
  const [confirmSubmit, setConfirmSubmit] = useState(false);

  useEffect(() => {
    if (id) {
      fetchSurveyData();
    }
  }, [id]);

  const fetchSurveyData = async () => {
    try {
      setIsLoading(true);

      // First get the survey version ID from the survey index ID (like legacy app)
      console.log('Getting survey version for index:', id);
      const surveyInfoResponse = await axiosInstance.get(`/survey/index/${id}/`);
      console.log('Survey info response:', surveyInfoResponse.data);

      // Extract version ID from the surveys array
      const surveys = surveyInfoResponse.data.surveys;
      if (!surveys || surveys.length === 0) {
        throw new Error('No survey versions found');
      }

      const versionId = surveys[0].id; // Use first version
      console.log('Using version ID:', versionId);

      // Now create survey response using the version ID (like legacy app)
      const response = await axiosInstance.post('/survey/survey-response/', {
        survey: versionId,
        resourcetype: 'SurveyResponseEngagement'
      });

      console.log('Engagement survey response:', response.data);

      // Use actual response data if available, otherwise use mock data
      const surveyData = response.data;
      const mockData = {
        meta: {
          title: surveyData?.survey?.title || 'Engagement Survey',
          endDate: surveyData?.survey?.deadline || '2024-12-31',
          canSubmit: false,
          nextTitle: 'Next',
          buttonType: 'primary',
          hideBack: false
        },
        sections: [
          { id: 1, title: 'General Questions', completed: false },
          { id: 2, title: 'Work Environment', completed: false },
          { id: 3, title: 'Career Development', completed: false }
        ],
        questions: [
          { id: 1, title: 'How satisfied are you with your current role?', type: 'radio', hasFeedback: true },
          { id: 2, title: 'Rate your work-life balance', type: 'rating', hasFeedback: false }
        ],
        responseId: surveyData?.id || 'resp_123',
        completion: 25
      };

      setMeta(mockData.meta);
      setSections(mockData.sections);
      setCurrentSection(mockData.sections[0]);
      setQuestions(mockData.questions);
      setResponseId(mockData.responseId);
      setCompletion(mockData.completion);
      setError(null);
    } catch (err) {
      console.error('Error fetching survey data:', err);
      setError('Failed to load survey data');
    } finally {
      setIsLoading(false);
    }
  };

  const handleNext = () => {
    if (!currentSection || !sections) return;

    const currentIndex = sections.findIndex(s => s.id === currentSection.id);
    if (currentIndex < sections.length - 1) {
      setCurrentSection(sections[currentIndex + 1]);
    } else if (meta?.canSubmit) {
      setConfirmSubmit(true);
    }
  };

  const handleBack = () => {
    if (!currentSection || !sections) return;

    const currentIndex = sections.findIndex(s => s.id === currentSection.id);
    if (currentIndex > 0) {
      setCurrentSection(sections[currentIndex - 1]);
    }
  };

  const handleSubmit = async () => {
    try {
      setIsSaving(true);
      // Submit survey using the response ID from the survey response creation
      await axiosInstance.post(`/survey/survey-response/${responseId}/submit/`);
      navigate(USER_ROUTES().dashboard.surveyList);
    } catch (err) {
      console.error('Error submitting survey:', err);
      setError('Failed to submit survey');
    } finally {
      setIsSaving(false);
    }
  };

  const handleSectionSelect = (section: Section) => {
    setCurrentSection(section);
    setConfirmSubmit(false);
  };

  if (error) {
    return (
      <div className="p-6">
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  if (confirmSubmit) {
    return (
      <div className="min-h-screen flex items-center justify-center p-6">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Submit Survey</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p>Are you ready to submit your responses?</p>
            <div className="flex gap-2">
              <Button 
                variant="outline" 
                onClick={() => setConfirmSubmit(false)}
                className="flex-1"
              >
                Back
              </Button>
              <Button 
                onClick={handleSubmit}
                disabled={isSaving}
                className="flex-1"
              >
                {isSaving ? 'Submitting...' : 'Submit'}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100 flex">
      {/* Left Sidebar - matches legacy app */}
      <div className="w-64 bg-white border-r border-gray-200 flex flex-col">
        {/* Back button */}
        <div className="p-4 border-b border-gray-200">
          <Button
            variant="ghost"
            onClick={() => navigate(USER_ROUTES().dashboard.surveyList)}
            className="text-blue-600 hover:text-blue-700 p-0 h-auto font-normal"
          >
            ← Back
          </Button>
        </div>

        {/* Survey Info */}
        <div className="p-4 space-y-4">
          <div>
            <h3 className="text-sm font-medium text-gray-700 mb-1">Survey Ends On</h3>
            <p className="text-sm text-gray-900">
              {isLoading ? <Skeleton className="h-4 w-32" /> : meta?.endDate || 'Mar 24, 2026, 08:23 PM'}
            </p>
          </div>

          <div>
            <h3 className="text-sm font-medium text-gray-700 mb-1">Survey Status</h3>
            <div className="space-y-1">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Questions</span>
                <span className="font-medium">{questions.length || 4}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Completed</span>
                <span className="font-medium">{Math.round((completion / 100) * (questions.length || 4)) || 0}</span>
              </div>
            </div>
          </div>

          <div>
            <h3 className="text-sm font-medium text-gray-700 mb-2">Categories</h3>
            <div className="space-y-1">
              {isLoading ? (
                Array(3).fill(0).map((_, index) => (
                  <Skeleton key={index} className="h-8 w-full" />
                ))
              ) : (
                sections.map((section) => (
                  <button
                    key={section.id}
                    onClick={() => handleSectionSelect(section)}
                    className={`w-full text-left px-3 py-2 text-sm rounded ${
                      currentSection?.id === section.id
                        ? 'bg-blue-100 text-blue-700 font-medium'
                        : 'text-gray-600 hover:bg-gray-50'
                    }`}
                  >
                    {section.title}
                    <div className="text-xs text-gray-500 mt-1">
                      {section.completed ? 'Completed' : `${questions.length || 4} questions left`}
                    </div>
                  </button>
                ))
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col">
        {/* Header - matches legacy app blue header */}
        <div className="bg-blue-500 text-white px-6 py-4">
          <div className="flex justify-between items-center">
            <h1 className="text-xl font-medium">
              {isLoading ? <Skeleton className="h-6 w-64 bg-blue-400" /> : meta?.title || 'Test Engagement report - 2'}
            </h1>
            <div className="flex items-center space-x-4 text-sm">
              <span>{completion}% Completed</span>
              <span>Saved Jun 03, 2025, 07:36 PM</span>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 bg-white p-6">
          {isLoading ? (
            <div className="space-y-6">
              <Skeleton className="h-8 w-3/4" />
              <Skeleton className="h-32 w-full" />
              <Skeleton className="h-32 w-full" />
            </div>
          ) : (
            <div className="max-w-4xl">
              <h2 className="text-lg font-medium mb-6">{currentSection?.title || 'Section 1'}</h2>

              {/* Sample Questions - matches legacy app design */}
              <div className="space-y-8">
                <div className="space-y-4">
                  <h3 className="font-medium">1. Question 1</h3>
                  <div className="flex items-center space-x-4">
                    {[5, 4, 3, 2, 1, 0].map((value) => (
                      <label key={value} className="flex flex-col items-center space-y-2 cursor-pointer">
                        <span className="text-lg font-medium">{value}</span>
                        <input type="radio" name="q1" value={value} className="w-4 h-4" />
                        <span className="text-xs text-gray-600 text-center">
                          {value === 5 ? 'Scale - 5' :
                           value === 4 ? 'Scale - 4' :
                           value === 3 ? 'Scale - 3' :
                           value === 2 ? 'Scale - 2' :
                           value === 1 ? 'Scale - 1' :
                           'No basis for rating'}
                        </span>
                      </label>
                    ))}
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="font-medium">2. Question 2</h3>
                  <div className="flex items-center space-x-4">
                    {[5, 4, 3, 2, 1, 0].map((value) => (
                      <label key={value} className="flex flex-col items-center space-y-2 cursor-pointer">
                        <span className="text-lg font-medium">{value}</span>
                        <input type="radio" name="q2" value={value} className="w-4 h-4" />
                        <span className="text-xs text-gray-600 text-center">
                          {value === 5 ? 'Scale - 5' :
                           value === 4 ? 'Scale - 4' :
                           value === 3 ? 'Scale - 3' :
                           value === 2 ? 'Scale - 2' :
                           value === 1 ? 'Scale - 1' :
                           'No basis for rating'}
                        </span>
                      </label>
                    ))}
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="font-medium">3. Question 3</h3>
                  <div className="text-center py-8">
                    <div className="bg-pink-100 border border-pink-200 rounded p-4 inline-block">
                      <span className="text-pink-700">4 Questions left in the section</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Submit Button - matches legacy app */}
              <div className="mt-8 text-center">
                <Button
                  onClick={handleSubmit}
                  disabled={isSaving}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2"
                >
                  {isSaving ? 'Submitting...' : 'Submit my response'}
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default EngagementSurvey;
