import { combineReducers } from "redux";
import util from "unmatched/utils";
// import { generateReducer } from "unmatched/utils/store";
import questionsReducer from "./CreateQuestions/questions-reducer";

const { generateReducer } = util.store;

const initialState = {};

const ACTIONS = {};

export default combineReducers({
  meta: generateReducer(ACTIONS, initialState),
  questions: questionsReducer,
});
