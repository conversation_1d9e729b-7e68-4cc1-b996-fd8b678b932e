import React from "react";
import { useDispatch, useSelector } from "unmatched/hooks";
import util from "unmatched/utils";
import { getMetaLabelsFact } from "./filters-api";
// import { ErrorType } from "unmatched/types";
import actions from "./slice";

export default function useFilter(id?: any, exclude?: boolean) {
  const filters: Array<any> = useSelector(
    (state: any) => state.filters.filters
  );
  const [selected, setSelected] = React.useState<any>({});
  const dispatch = useDispatch();

  const fetchFilters = (cb?: Function) => {
    getMetaLabelsFact({ survey_id: id, exclude_disabled: exclude }).then(
      (response: any) => {
        dispatch(actions.set(response));
        if (cb) cb(response);
      }
    );
  };

  const getFilters = (cb?: Function) => {
    if (!util.lib.isEmpty(filters)) {
      if (cb) cb(filters);
    } else {
      fetchFilters(cb);
    }
  };

  const onSelect = (item: any) => {
    // console.log(item);
    setSelected(item);
  };

  const getParams: any = (_selected: any) => {
    let params = {};
    Object.keys(_selected).forEach((key: string) => {
      const values = util.lib.get(_selected, key);
      if (!values.length) return;
      params = { ...params, [key]: util.getCommaSeperatedFromArray(values) };
    });
    return params;
  };

  return {
    filters,
    selected,
    getFilters,
    onSelect,
    getParams,
  };
}
