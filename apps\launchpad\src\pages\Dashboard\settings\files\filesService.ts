import axiosInstance from "@/lib/axios";
import { AxiosResponse } from "axios";
import { API_ENDPOINTS } from "@/constants";

// Define types for API parameters and responses
export interface FileListParams {
  page?: number;
  page_size?: number;
  search?: string;
  ordering?: string;
  [key: string]: unknown; // Allow additional parameters
}

export interface ApiFile {
  id: string;
  title: string;
  file: string;
  format: string;
  uploaded_on?: string;
  created_at?: string;
  tags: string[];
  error_log_file?: string;
  result?: {
    success_upload_count: number;
    users_added_count: number;
    users_updated_count: number;
    total_file_records: number;
    rejected?: {
      duplicate_email?: number;
      duplicate_users?: number;
      invalid_last_name?: number;
      invalid_first_name?: number;
      email_already_exists?: number;
      email_not_updatable?: number;
      invalid_email?: number;
    };
  };
}

export interface FileListResponse {
  results: File[];
  totalPages: number;
  totalElements: number;
}

export interface ApiEmployee {
  id: string;
  email: string;
  emp_id: string;
  first_name: string;
  last_name: string;
  metadata: Record<string, unknown>;
  is_terminated: boolean;
}

export interface Employee {
  id: string;
  email: string;
  empId: string;
  firstName: string;
  lastName: string;
  metadata: Record<string, unknown>;
  departed: string;
}

export interface EmployeeListResponse {
  results: Employee[];
  totalPages: number;
  totalElements: number;
}

export interface File {
  id: string;
  title: string;
  fileUrl: string;
  format: string;
  successRecords: number;
  rejectedRecords: number;
  totalRecords: number;
  uploadedOn: string;
  tags: string[];
  errorLogFile?: string;
}

/**
 * Get a list of files with optional filtering, sorting, and pagination
 */
export const getFilesService = async (params: FileListParams): Promise<FileListResponse> => {
  try {
    interface ApiResponse {
      results: ApiFile[];
      count_pages: number;
      count_items: number;
    }

    const response: AxiosResponse<ApiResponse> = await axiosInstance.get(API_ENDPOINTS.EMPLOYEE.UPDATE_UPLOAD, {
      params,
    });

    // Log the complete response to see the actual structure
    if (response.data.results.length > 0) {
      console.log("Complete API response:", JSON.stringify(response.data.results[0], null, 2));
    }

    return {
      results: response.data.results.map((item: ApiFile) => {
        // Log the complete item for debugging
        console.log("Complete item data:", JSON.stringify(item, null, 2));
        // Extract the record counts from the API response
        const successRecords = item.result?.success_upload_count || 0;
        const totalRecords = item.result?.total_file_records || 0;

        // Calculate rejected records based on the API response
        let rejectedRecords = 0;

        // If we have rejected data in the response, sum it up
        if (item.result?.rejected) {
            // Sum up all the rejection reasons
            const rejectionValues = Object.values(item.result.rejected).filter(val => typeof val === 'number') as number[];
            rejectedRecords = rejectionValues.reduce((sum, val) => sum + val, 0);
        } else {
            // Otherwise calculate as total - success
            rejectedRecords = Math.max(0, totalRecords - successRecords);
        }

        console.log(`File ${item.id} counts:`, {
            successRecords,
            rejectedRecords,
            totalRecords
        });

        // Log the created_at value for debugging
        console.log(`File ${item.id} created_at:`, item.created_at);

        // Create the file object with the calculated values
        const fileObj = {
          id: item.id,
          title: item.title || "Untitled",
          fileUrl: item.file,
          format: item.format,
          successRecords: successRecords,
          rejectedRecords: rejectedRecords,
          totalRecords: totalRecords,
          uploadedOn: item.uploaded_on || "N/A", // Use the actual date from the API
          tags: item.tags || [],
          errorLogFile: item.error_log_file,
        };

        console.log("Created file object:", fileObj);
        return fileObj;
      }),
      totalPages: response.data.count_pages,
      totalElements: response.data.count_items,
    };
  } catch (error: unknown) {
    console.error("Error fetching files:", error);
    throw error;
  }
};

/**
 * Delete a file by ID
 * @param id - ID of the file to delete
 * @returns A promise that resolves to true if deletion was successful
 */
export const deleteFileService = async (id: string): Promise<boolean> => {
  try {
    await axiosInstance.delete(`${API_ENDPOINTS.EMPLOYEE.UPDATE_UPLOAD}${id}/`);
    return true;
  } catch (error: unknown) {
    console.error("Error deleting file:", error);
    throw error;
  }
};

/**
 * Get file details by ID
 */
export const getFileDetailsService = async (id: string): Promise<ApiFile> => {
  try {
    const response: AxiosResponse<ApiFile> = await axiosInstance.get(`${API_ENDPOINTS.EMPLOYEE.UPDATE_UPLOAD}${id}/`);
    return response.data;
  } catch (error: unknown) {
    console.error("Error fetching file details:", error);
    throw error;
  }
};

/**
 * Download a file
 * @param fileUrl - URL of the file to download
 * @param fileId - Optional file ID to fetch file URL if not provided directly
 */
export const downloadFileService = async (fileUrl: string, fileId?: string): Promise<void> => {
  try {
    // If we have a direct URL, use it
    if (fileUrl) {
      window.open(fileUrl);
      return;
    }

    // If we have a file ID but no URL, try to fetch the file details to get the file URL
    if (fileId) {
      const fileDetails = await getFileDetailsService(fileId);
      if (fileDetails.file) {
        window.open(fileDetails.file);
        return;
      }
    }

    // If we reach here, no file is available
    throw new Error("File not available for download");
  } catch (error: unknown) {
    console.error("Error downloading file:", error);
    throw error;
  }
};

/**
 * Download error log file
 * @param errorLogUrl - URL of the error log file to download
 * @param fileId - Optional file ID to fetch error log URL if not provided directly
 */
export const downloadErrorLogService = async (errorLogUrl: string, fileId?: string): Promise<void> => {
  try {
    // If we have a direct URL, use it
    if (errorLogUrl) {
      window.open(errorLogUrl);
      return;
    }

    // If we have a file ID but no URL, try to fetch the file details to get the error log URL
    if (fileId) {
      const fileDetails = await getFileDetailsService(fileId);
      if (fileDetails.error_log_file) {
        window.open(fileDetails.error_log_file);
        return;
      }
    }

    // If we reach here, no error log file is available
    throw new Error("No rejected records available for this file");
  } catch (error: unknown) {
    console.error("Error downloading error log:", error);
    throw error;
  }
};

/**
 * Get employees in a file with optional filtering, sorting, and pagination
 */
export const getFileEmployeesService = async (fileId: string, params: FileListParams = {}): Promise<EmployeeListResponse> => {
  try {
    interface ApiResponse {
      results: ApiEmployee[];
      count_pages: number;
      count_items: number;
    }

    // Use the correct API endpoint for fetching file employees
    const requestParams = {
      ...params,
      file_id: fileId
    };

    // Make sure page_size is set to 10 if not already specified
    if (!requestParams.page_size) {
      requestParams.page_size = 10;
    }

    const response: AxiosResponse<ApiResponse> = await axiosInstance.get(API_ENDPOINTS.STAFF.ABSTRACT_USERS, {
      params: requestParams,
    });

    const mappedResults = response.data.results.map((item: ApiEmployee) => ({
      id: item.id,
      email: item.email,
      empId: item.emp_id,
      firstName: item.first_name,
      lastName: item.last_name,
      metadata: item.metadata,
      departed: item.is_terminated ? "Yes" : "No",
    }));

    return {
      results: mappedResults,
      totalPages: response.data.count_pages,
      totalElements: response.data.count_items,
    };
  } catch (error: unknown) {
    console.error("Error fetching file employees:", error);
    throw error;
  }
};
