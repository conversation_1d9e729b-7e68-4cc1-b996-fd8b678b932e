// Node modules
import React, { useState } from "react";
import SetPasswordSchema, {
  ChangePasswordSchema,
  validatePassword,
} from "../Password/password-form";
import { useCallback, useDispatch } from "@unmatched/hooks";
// import {
// Button,

// Form,
// FormGroup,
// FormControl,
// } from "unmatched/components";
// import Loader from "../Loader";
import PasswordForm from "../Password/PasswordForm";
// import { useState } from "unmatched/hooks";
import useSession from "@unmatched/modules/session/hook";
import { setPasswordFact } from "../auth.api";
// import { getFieldErrorMessage, isFieldValid } from "unmatched/utils/formik";
import { getUserFact } from "@unmatched/modules/session/api";
import actions from "@unmatched/modules/session/slice";
// import useToastr from "@unmatched/modules/toastr/hook";
import { SetPasswordSuccess } from "./SetPasswordSuccess";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@repo/ui/components/form";
import { useForm } from "react-hook-form";
import { Button } from "@repo/ui/components/button";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Input } from "@repo/ui/components/input";
import { toast } from "sonner";
// import util from "@unmatched/utils";

// const { getFieldErrorMessage, isFieldValid } = util.formik;

const initialMeta = {
  content: "",
  success: false,
};

export default function ChangePassword(props: {
  user: any;
  closeModal: () => void;
  setPassSetComplete: () => void;
}) {
  const {
    user,
    user: { isPasswordSet },
    closeModal,
    setPassSetComplete: superPassSetComplete,
  } = props;

  const initState = {
    password: "",
    confirmPassword: "",
    tooltip: {
      number: false,
      alphabet: false,
      min: false,
      ...(isPasswordSet && { same: false }),
    },
    loading: false,
    error: {
      statusCode: 0,
      msg: "",
    },
  };
  const session = useSession();
  const [meta, setMeta] = useState(initialMeta);
  const [state, setState] = useState(initState);
  const [passSetComplete, setPassSetComplete] = useState(false);
  const dispatch = useDispatch();
  // const toastr = useToastr();

  const onSetChangePassword = async (data: any) => {
    setState((state: any) => ({
      ...state,
      loading: true,
    }));
    setMeta(initialMeta);
    try {
      const res = await setPasswordFact({ ...data });
      if (res?.data?.token) {
        session.resetSession();
        const {
          data: { token: authToken, expiry },
        } = res;
        const userResponse = await getUserFact({ token: authToken });
        session.login({
          token: authToken,
          expiry,
          user: userResponse,
        });
        toast.success("Password changed successfully.");
        closeModal();
      } else {
        dispatch(actions.setUser({ ...user, isPasswordSet: true }));
        setPassSetComplete(true);
        superPassSetComplete(true);
      }
    } catch (err) {
      setMeta((meta) => ({
        ...meta,
        content: "Wrong password. Please try again.",
        success: false,
      }));
    } finally {
      setState((state: any) => ({
        ...state,
        loading: false,
      }));
    }
  };
  // const formikOptions = {
  //   initialValues: {
  //     ...(isPasswordSet && { oldPassword: "" }),
  //     password: "",
  //     confirmPassword: "",
  //   },
  //   validationSchema: isPasswordSet ? ChangePasswordSchema : SetPasswordSchema,
  //   onSubmit: (values: any) => {
  //     onSetChangePassword(values);
  //   },
  // };

  // const form = useForm({
  //   defaultValues: {
  //     ...(isPasswordSet && { oldPassword: "" }),
  //     password: "",
  //     confirmPassword: "",
  //   },
  // validationSchema: isPasswordSet ? ChangePasswordSchema : SetPasswordSchema,
  // onSubmit: (values: any) => {
  //   onSetChangePassword(values);
  // },
  // });
  // const onPasswordChange = useCallback(
  //   (e: React.ChangeEvent<any>) => {
  //     formik.handleChange(e);
  //     const tooltip: any = validatePassword(e.target.value);
  //     setState((s) => ({
  //       ...s,
  //       tooltip: {
  //         ...tooltip,
  //         ...(isPasswordSet && {
  //           same: formik.values.oldPassword !== e.target.value,
  //         }),
  //       },
  //     }));
  //   },
  //   [formik]
  // );

  const schema = z
    .object({
      ...(isPasswordSet && { oldPassword: z.string().min(8) }),
      password: z.string().min(8),
      confirmPassword: z.string().min(8),
    })
    .refine((data) => data.password === data.confirmPassword, {
      message: "Passwords do not match",
      path: ["confirmPassword"],
    });
  // const FeedbackTag = meta.success
  //   ? FormGroup.ValidFeedback
  //   : FormGroup.InValidFeedback;

  const form = useForm({
    defaultValues: {
      ...(isPasswordSet && { oldPassword: "" }),
      password: "",
      confirmPassword: "",
    },
    resolver: zodResolver(schema),
  });

  return !passSetComplete ? (
    //onSubmit={formik.handleSubmit}
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSetChangePassword)}>
        {/* <div>
        <FeedbackTag text={meta.content} />
      </div> */}
        <div className="py-3">
          {isPasswordSet && (
            // <FormGroup>
            //   <FormGroup.Label>Old Password</FormGroup.Label>
            //   <FormControl.Password
            //     name="oldPassword"
            //     autoFocus
            //     value={formik.values["oldPassword"] || ""}
            //     isValid={isFieldValid(formik, "oldPassword")}
            //     onBlur={formik.handleBlur}
            //     onChange={formik.handleChange}
            //     placeholder="Enter old password"
            //     style={{
            //       ...(getFieldErrorMessage(formik, "oldPassword") && {
            //         borderColor: "#f34115",
            //       }),
            //     }}
            //   />
            //   <FormGroup.InValidFeedback
            //     text={getFieldErrorMessage(formik, "oldPassword")}
            //   />
            // </FormGroup>
            <FormField
              control={form.control}
              name="oldPassword"
              render={({ field }) => (
                <FormItem className="relative mb-3">
                  <FormLabel>Old Password</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="Enter old password" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}
          <PasswordForm
            form={form}
            // onPasswordChange={onPasswordChange}
            // tooltip={state.tooltip}
            // colXL={12}
          ></PasswordForm>
        </div>
        <Button
          disabled={state.loading}
          className="float-right"
          // variant="primary"
          type="submit"
        >
          Submit
        </Button>
      </form>
    </Form>
  ) : (
    <SetPasswordSuccess onDone={closeModal} />
  );
}
