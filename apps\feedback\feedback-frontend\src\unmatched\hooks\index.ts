import { hooks } from "@unmatchedoffl/ui-core";
export { useFormik } from "formik";
import { isEqual } from "lodash";
import { useSelector } from "react-redux";
import { useLocation } from "react-router-dom";
export { useHistory, useParams, useLocation } from "react-router-dom";
export { useSelector, useDispatch } from "react-redux";
export { useState, useEffect, useCallback, useRef } from "react";
export { useMedia, useDebounce } from "react-use";

export const useQuery = () => {
  return new URLSearchParams(useLocation().search);
};

export const useCheckSelector = (selector: any) =>
  useSelector(selector, isEqual);

export const { useTable, useXHR, useLayout } = hooks;
