// import { useFormik } from "formik";
// import * as yup from "yup";
import React from "react";
// import {
//   Button,
//   Card,
//   div,
//   Form,
//   FormControl,
//   FormGroup,
//   Layout,
//   Text,
// } from "unmatched/components";
import Loader from "../Loader";
import AuthContainer from "../AuthContainer";
import { useState } from "@unmatched/hooks";
// import icons from "../../../assets/icons/icons";
import { reqResetREQFact } from "../auth.api";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@repo/ui/components/form";
import { useForm } from "react-hook-form";
import { Button } from "@repo/ui/components/button";
// import { Card } from "@repo/ui/components/card";
import { Text } from "@repo/ui/components/Text";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Input } from "@repo/ui/components/input";
import { MailCheck } from "lucide-react";
// import util from "@unmatched/utils";

// const { getFieldErrorMessage, isFieldInvalid, isFieldValid } = util.formik;

const META_DATA = {
  title: "Password Reset",
  content: "Enter the email address associated with your account",
  loadingTitle: "Check your mailbox",
  loadingContent:
    "Password reset link has been emailed to you. Click the link in the email to reset your password.",
};

export default function ReqestPasswordChange() {
  const meta = META_DATA;

  const [state, setState] = useState({
    isLoading: false,
    sent: false,
    error: {
      statusCode: 0,
      errorCode: "",
      msg: "",
    },
  });

  const onFetchFail = (err: any, payload: any) => {
    setState((state: any) => ({
      ...state,
      isLoading: false,
      ...payload,
      error: err,
    }));
  };

  const onRequest = async (data: any) => {
    setState((state: any) => ({
      ...state,
      isLoading: true,
    }));
    try {
      await reqResetREQFact(data);
      setState((state: any) => ({
        ...state,
        sent: true,
      }));
      // history.push(appUrls.auth.login);
    } catch (err) {
      form.setValue("email", formData.email);
      // formik.setFieldError("email", Error(state.error.msg).title)
      onFetchFail(err, {});
    }
  };

  const [formData, setFormData] = React.useState({
    email: "",
  });

  // let initialValues =
  const schema = z.object({
    email: z.string().email("Enter a valid email id"),
  });
  const form = useForm({
    defaultValues: {
      email: formData.email,
    },
    resolver: zodResolver(schema),
    // validationSchema: yup.object().shape({
    //   email: yup
    //     .string()
    //     .required("Enter your email id")
    //     .email("Enter a valid email id"),
    // }),
    // onSubmit: (values: any) => {
    //   setFormData({ email: values["email"] });
    //   onRequest(values);
    //   // formik.setFormikState(email: values.email})
    // },
  });

  const onSubmit = (data: any) => {
    setFormData({ email: data.email });

    // console.log(data);
    onRequest(data);
  };

  let Content = null;

  const Error = (error: string) => {
    const errors: any = {
      USER_NOT_ACTIVE: {
        title: "This email id is not activated",
        description:
          "Check your email to activate or click below to resend the activation email",
      },
      USER_NOT_FOUND: {
        title: "Unable to reset the password",
        description: "Check your email id and try again",
      },
      OTHER: {
        title: "Something went wrong",
        description: "Please try again",
      },
    };

    return errors[error] ?? errors.OTHER;
  };
  // const form = useForm();

  if (state.sent) {
    // const { CheckMail } = icons;
    Content = (
      <>
        <Loader
          title={meta.loadingTitle}
          iconTemplate={<MailCheck className="size-12 mx-auto text-primary" />}
          content={meta.loadingContent}
        ></Loader>
      </>
    );
  } else if (state.isLoading) {
    Content = <Loader hideContent></Loader>;
  } else {
    Content = (
      // onReset={formik.handleReset} onSubmit={formik.handleSubmit}
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <Text.H2 className="text-primary pb-2">{meta.title}</Text.H2>
          <Text.P1 className="pb-4">
            {meta.content}
            <div className="py-3">
              <div className="py-3 grid grid-cols-12">
                <div className="col-span-12">
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  {/* <FormGroup>
                  <FormGroup.Label>Email</FormGroup.Label>
                  <FormControl.Email
                    name="email"
                    isInvalid={isFieldInvalid(formik, "email")}
                    isValid={isFieldValid(formik, "email")}
                    onBlur={formik.handleBlur}
                    onChange={(e: any) => {
                      formik.handleChange(e);
                      setFormData({ email: e.target.value });
                      setState({
                        ...state,
                        error: {
                          statusCode: 0,
                          errorCode: "",
                          msg: "",
                        },
                      });
                    }}
                    placeholder="Email address"
                    value={formik.values["email"] || ""}
                  ></FormControl.Email>
                  <FormGroup.InValidFeedback
                    text={getFieldErrorMessage(formik, "email")}
                  ></FormGroup.InValidFeedback>
                  {state.error.statusCode >= 400 ? (
                    <div className="mt-3">
                      <FormGroup.InValidFeedback>
                        {Error(state.error.msg).title}
                      </FormGroup.InValidFeedback>
                      <FormGroup.Description className="pt-1">
                        {Error(state.error.msg).description}
                      </FormGroup.Description>
                    </div>
                  ) : (
                    ""
                  )}
                </FormGroup> */}
                </div>
              </div>
            </div>
          </Text.P1>
          <div>
            <Button className="text-xs" type="submit">
              Request Password Reset
            </Button>
          </div>
        </form>
      </Form>
    );
  }
  return (
    <AuthContainer className="max-w-lg mx-auto w-full">
      <title>Forgot Password - Unmatched</title>
      {Content}
    </AuthContainer>
  );
}
