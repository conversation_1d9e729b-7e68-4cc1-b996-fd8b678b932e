import DownloadExcel from "pages/AdminApp/Analytics/DownloadExcel";
import useFilter from "pages/CommonFilters/hook";
import React from "react";
import { Table, Div, Text, Layout } from "unmatched/components";
import { useTable } from "unmatched/hooks";
import util from "unmatched/utils";
import { getDetailOverallDataFact } from "../aggregate-api";
// import PropTypes from 'prop-types'

const defaultSelected = { id: "", key: "", title: "", values: [] };

const getFilterOptions = (_filters: any) => {
  const filters = _filters || {};
  return util.lib.entries(filters).map((item: any) => {
    const [key, value] = item;
    return {
      id: key,
      key,
      title: value.label,
      values: value.values,
    };
  });
};

const Detailed = () => {
  // const { userId } = props;
  const filtersState = useFilter();
  const [groups, setGroups] = React.useState([]);
  const [group, setGroup]: any = React.useState({});
  const [columns, setColumns] = React.useState([]);
  const tableMeta = useTable({});
  const [selected, setSelected] = React.useState(defaultSelected);

  const [list, setList] = React.useState([]);

  let filterOptions = getFilterOptions(filtersState.filters);

  const getColumns = () => {
    const output = [
      { key: 1, label: "No.", hasSort: false, disableSelfLabel: true },
      {
        key: 2,
        label: "Year",
        hasSort: false,
        disableSelfLabel: true,
      },
      {
        key: 21,
        label: "Survey Title",
        hasSort: false,
        disableSelfLabel: true,
      },
      // {
      //   key: 3,
      //   label: "Reviewee Average",
      //   hasSort: false,
      // },
      { key: 41, label: "Firm Average", hasSort: false },
      ...(columns || []),
      // { key: 5, label: "Department Average", hasSort: false },
      // { key: 6, label: "Practice Group Average", hasSort: false },
      // { key: 7, label: "Title Average", hasSort: false },
      // { key: 8, label: "Self Average", hasSort: false, disableSelf: true },
      // { key: 9, label: "Comment Sentiment", hasSort: false, disableSelf: true },
    ];
    return output;
  };

  const getRowsTemplate = () => {
    return list.map((item: any, index: number) => {
      return (
        <Table.Row>
          <Table.Data>
            <Text.P1>{index + 1}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.year}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.name}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.firmAverage}</Text.P1>
          </Table.Data>
          {(columns || []).map((row: any) => {
            return (
              <Table.Data>
                <Text.P1>{item[row.key] || "0"}</Text.P1>
              </Table.Data>
            );
          })}
        </Table.Row>
      );
    });
  };

  const getDynamicColumns = (meta?: any) => {
    const options =
      filterOptions.find((item: any) => item.key === meta)?.values || [];
    return options.map((item: any) => {
      return {
        id: item,
        key: item,
        label: `${item} Average`,
      };
    });
  };

  const getTableData = (params?: any, meta?: any) => {
    return getDetailOverallDataFact(params?.group, meta).then(
      (response: any) => {
        setColumns(getDynamicColumns(meta));
        setList(response.data);
        setGroups(response.groups);
        setGroup(response.group);
      }
    );
  };

  React.useEffect(() => {
    filtersState.getFilters((_filters: any) => {
      filterOptions = getFilterOptions(_filters);
      const [item] = filterOptions;
      if (item) {
        setSelected(item);
        getTableData({}, item.key);
      }
    });
  }, []);

  const onSelect = (_selected: any) => {
    getTableData(
      {
        group: group.id,
      },
      _selected.key
    ).then(() => {
      setSelected(_selected);
    });
  };
  if (list?.length === 0) return null;
  return (
    <Div>
      {/* <Layout.Row>
        <Layout.Col xl={4}>
        
        </Layout.Col>
      </Layout.Row> */}
      <DownloadExcel
        hideYearFilter
        surveys={{
          selected: group?.title,
          options: groups,
        }}
        onSurveySelect={(item: any) => {
          // setSurvey(item.key);
          getTableData(
            {
              group: item.key,
            },
            selected.key
          );
        }}
        metaSlot={
          <Layout.Col>
            <Table.Filter
              title="Select"
              selected={selected.key}
              selectedLabel={selected.title}
              options={filterOptions || []}
              onSelect={onSelect}
            />
          </Layout.Col>
        }
        title="360 Degree Average by Rater Groups"
      />
      <Table
        columns={getColumns()}
        isLoading={tableMeta.isLoading}
        rows={list}
        customRows
        render={() => getRowsTemplate()}
        hasPagination
        activePage={tableMeta.page}
        pages={tableMeta.totalPages}
        onPageSelect={tableMeta.onPageSelect}
      />
    </Div>
  );
};

// Detailed.propTypes = {}

export default Detailed;
