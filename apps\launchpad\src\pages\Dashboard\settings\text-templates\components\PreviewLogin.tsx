import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogFooter,
} from "@repo/ui/components/dialog";
import { Button } from "@repo/ui/components/button";
import { Text } from "@repo/ui/components/Text";

interface PreviewLoginProps {
  showPreview: boolean;
  setShowPreview: (show: boolean) => void;
  loginText?: string;
}

export const PreviewLogin: React.FC<PreviewLoginProps> = ({
  showPreview = false,
  setShowPreview = () => {},
  loginText = "",
}) => {
  // In a real implementation, we would use an actual login page preview image
  // For now, we'll create a simple representation

  // Ensure we have a valid boolean for showPreview
  const isOpen = Boolean(showPreview);

  // Ensure we have a valid string for loginText
  const safeLoginText = loginText || "";

  return (
    <Dialog open={isOpen} onOpenChange={setShowPreview}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Login Page Preview</DialogTitle>
        </DialogHeader>
        <div className="relative p-4 border rounded-md bg-gray-50">
          <div className="flex justify-center mb-8">
            <div className="w-32 h-12 bg-gray-200 rounded-md flex items-center justify-center">
              <Text.P2 className="text-gray-500">Company Logo</Text.P2>
            </div>
          </div>

          <div className="max-w-md mx-auto bg-white p-6 rounded-md border shadow-sm">
            <Text.H3 className="mb-4">Welcome</Text.H3>

            <div className="mb-6">
              <Text.P2 className="text-gray-700">
                {safeLoginText || "Your welcome message will appear here."}
              </Text.P2>
            </div>

            <div className="space-y-4">
              <div className="h-10 bg-gray-100 rounded-md"></div>
              <div className="h-10 bg-gray-100 rounded-md"></div>
              <Button className="w-full" disabled>Sign In</Button>
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button onClick={() => setShowPreview(false)}>Close</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
