import React, { useState } from "react";
import { useTable } from "unmatched/hooks";
import Header from "../../Header";
import {
  PageContainer,
  Text,
  Layout,
  Table,
  Div,
} from "unmatched/components";
import { getEngagementReportsDownloadRequests } from "../../../reports-api";
import useFilter from "../../../../../CommonFilters/hook";
import util from "unmatched/utils";
import { keys, map, get, isEqual } from "lodash";
import iconsSvgs from "assets/icons/icons";
import appUrls from "unmatched/utils/urls/app-urls";
import useToastr from "unmatched/modules/toastr/hook";

const { Page } = iconsSvgs;

const Downloads = (props: any) => {
  const { survey, layout, surveyId } = props;
  const tableMeta = useTable({
    size: 15
  });
  const toastr = useToastr();
  // const [filters, setFilters] = React.useState({
  //   showUnflagged: false,
  //   cohart: {
  //     options: COHARTS,
  //     selected: "department",
  //   },
  //   applied: {
  //     options: [],
  //     selected: "Option1",
  //   },
  // });
  // const { showToast } = useToastr();
  const [isLoading, setLoading] = React.useState(false);
  const [downloadRequests, setDownloadRequests] = React.useState<any>([]);
  const [nIntervId, setNIntervId] = useState<any>(null);
  const filtersState = useFilter();

  React.useEffect(() => {
    filtersState.getFilters();
  }, []);

  React.useEffect(() => {
    getDownloadRequests();
  }, [props.activeTab]);

  React.useEffect(() => {
    if (
        downloadRequests.some((req: any) => req?.status === "QUE") &&
      !nIntervId
    ) {
      const nIntervIdL = setInterval(async () => {
        try {
          const response = await getEngagementReportsDownloadRequests({
            surveyId,
            page_size: tableMeta.size,
          });
          const computedData = getComputedData(response.results);
          if (
            !isEqual(
              downloadRequests.filter((cd: any) => cd.status === 'QUE').length,
              computedData.filter((cd: any) => cd.status === 'QUE').length
            )
          ) {
            getDownloadRequests();
            toastr.onSucces({
              title: "Request successfull",
              content:
                "Your download request was successfull. Check table below, for results",
            });
          }
        } catch (err) {
          console.log(err);
        }
      }, 10000);
      setNIntervId(nIntervIdL);
    } else {
      clearInterval(nIntervId);
      setNIntervId(null);
    }
  }, [downloadRequests]);

  //   const onSearch = (search: string) => {
  //     getDownloadRequests(null, {
  //       search,
  //     });
  //   };

  const getComputedData = (data: any) => {
    return data.map((el: any) => ({
        parameters: Object.values(el.parameters?.filter_by || {}).join(', ') || 'None',
        moreParameters: el.parameters?.report_for,
        requestedBy: el.requested_by?.email,
        requestedOn: el.requested_on,
        status: el.task?.status,
        file: el.file,
    }))
  }

  const getDownloadRequests = async (params?: any) => {
    try {
      setLoading(true);
      const res = await getEngagementReportsDownloadRequests({
        surveyId,
        page_size: 10,
        ...params,
      });
      const computedData = getComputedData(res.results);
      setDownloadRequests(computedData);
      tableMeta.setPagination({
        page: 1,
        totalPages: res.count_pages,
        size: tableMeta.size,
        totalItems: res.count_items,
        ...(params && params),
      });
      setLoading(false);
    } catch (err) {
      console.log(err);
      setLoading(false);
    }
  };

  // TableColumn templates

  const getColumnsData = () => {
    return [
      { key: 2, label: "No.", hasSort: false },
      {
        key: 4,
        label: "Requested Filters",
        hasSort: true,
        sortValue: "asc",
        sortKey: "parameters",
      },
      {
        key: 4,
        label: "More Filters",
        hasSort: true,
        sortValue: "asc",
        sortKey: "moreParameters",
      },
      {
        key: 5,
        label: "Requested By",
        hasSort: true,
        sortValue: "asc",
        sortKey: "requestedBy",
      },
      {
        key: 6,
        label: "Requested On",
        hasSort: true,
        sortValue: "dsc",
        sortKey: "requestedBy",
      },
      {
        key: 6,
        label: "Status",
        hasSort: true,
        sortValue: "dsc",
        sortKey: "status",
      },
      { key: 9, label: "Actions", hasSort: false },
    ];
  };

  const [columnsData] = useState<any>(getColumnsData());

  const getColumns = () => {
    let columnsList = keys(columnsData);
    columnsList = map(columnsList, (key: string) => ({
      ...get(columnsData, key),
      key,
    }));
    return columnsList;
  };

  // Table Row Templates

  const getStatusText = (status: string) => ({
    SUC: 'Ready',
    FAIL: 'Failed',
    QUE: 'Preparing'
  })[status];

  const getRowsTemplate = () => {
    return downloadRequests.map((item: any, index: number) => {
      const isEven = index % 2 === 0 || index === 0;
      return (
        <Table.Row even={!isEven} key={item.key}>
          <Table.Data width="60px">
            <Text.P1>{tableMeta.getSNo(index)}.</Text.P1>
          </Table.Data>
          {/* <Table.Data width="30px">
            <Text.P1>{getFlagTemplate(false)}</Text.P1>
          </Table.Data> */}
          <Table.Data>
            <Text.P1>{item.parameters}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.moreParameters}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.requestedBy}</Text.P1>
          </Table.Data>
          <Table.Data width="190px">
            <Text.P1>
              {util.date.getBrowserTime(
                item.requestedOn,
                "MMM dd, yyyy, HH:mm"
              )}
            </Text.P1>
          </Table.Data>
          <Table.Data width="190px">
            <Text.P1>{getStatusText(item.status)}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Div
              className="cursor-pointer"
              style={{
                color: "#2F80ED",
                fontSize: 14,
                textDecoration: "underline",
              }}
              onClick={() => {
                item.file && window.open(item.file, "_blank");
              }}
            >
              {item.status === 'SUC' && 'Download'}
            </Div>
          </Table.Data>
        </Table.Row>
      );
    });
  };

  return (
    <PageContainer>
      <Header
        survey={survey}
        layout={layout}
        title="Download Requests"
        metaTemplate={null}
        breadcrumbs={[
          {
            label: "Reports",
            icon: <Page className="grey-icon__svg" />,
            route: appUrls.admin.reports.default,
          },
          { label: "Download Requests" },
          { label: survey.name },
        ]}
      />
      {/* tableMeta.selected.length && */}
      <Layout.Container className="pt-4" fluid>
        <Div className="pt-4">
          <Table
            columns={getColumns()}
            isLoading={isLoading}
            rows={downloadRequests}
            customRows
            render={() => getRowsTemplate()}
            hasPagination
            activePage={tableMeta.page}
            pages={tableMeta.totalPages}
            onPageSelect={(number: number) => {
              tableMeta.onPageSelect(number);
              getDownloadRequests({ page: number });
            }}
            size={tableMeta.size}
            totalItems={tableMeta.totalItems}
            // onSort={(item: any) => {
            //   const label = util.label.getSortingLabel(
            //     item.sortKey,
            //     item.sortValue
            //   );
            //   setColumnsData((_columns: any) => {
            //     return tableMeta.resetColumns(_columns, item);
            //   });
            //   getUsers(null, { ordering: label, search: tableMeta.search });
            // }}
            // {...(tableMeta.search && {
            //   notFoundMsg: util.noSearchRecordsFoundMsg,
            // })}
          />
        </Div>
      </Layout.Container>
    </PageContainer>
  );
};

export default Downloads;
