import React from "react";
import appUrls from "unmatched/utils/urls/app-urls";
import useToastr from "unmatched/modules/toastr/hook";
// Components
import {
  Text,
  Layout,
  Button,
  Div,
  Table,
  FormControl,
  Placeholder,
  Dropdown,
} from "unmatched/components";
import CustomHeader from "../../Shared/CustomHeader/CustomHeader";
import DeletePair from "./DeletePair";
import AddPairingModal from "./AddPairingModal";
import {
  fetchAdminOngoingPairingsFact,
  pairingsFileDownload,
} from "../dataload-api";
import { useParams } from "react-router-dom";
import { useTable } from "unmatched/hooks";
import iconsSvgs from "assets/icons/icons";
import { getSurveyByIdFact } from "pages/AdminApp/Survey/survey-api";

const { Group } = iconsSvgs;

interface Pairing {
  // key: string;
  count_items: number;
  count_pages: number;
  results: Array<Pairs>;
  surveys: Array<{ id: number; name: string }>;
}
interface Pairs {
  rater: PairInfo;
  target: PairInfo;
  file: string;
  survey?: any;
}
interface PairInfo {
  emp_id: string;
  first_name: string;
  last_name: string;
  email: string;
  is_active: boolean;
  metadata: any;
  is_tenant_admin: boolean;
}

// enum ModalState {
//   VIEW = "VIEW",
//   MODIFY = "MODIFY",
// }

const ManageOngoingData = () => {
  const { showToast } = useToastr();
  const tableMeta = useTable({});
  const { id } = useParams<any>();
  const [isLoading, setIsLoading] = React.useState(true);
  const [fileInfo, setFileInfo] = React.useState<any>({});
  const [pairings, setPairings] = React.useState<Pairing>({
    count_items: 0,
    count_pages: 0,
    results: [],
    surveys: [],
  });
  const [filters, setFilters] = React.useState({
    search: "",
    page: 1,
    totalPages: 4,
  });
  const [selected, setSelected] = React.useState<Array<string>>([]);
  const [showModal, setShowModal] = React.useState<boolean>(false);
  const [showDeletePopUp, setShowDeletePopUp] = React.useState<boolean>(false);
  const [selectedVersion, setSelectedVersion] = React.useState<any>();

  React.useEffect(() => {
    setSelectedVersion(pairings.surveys?.[0]?.id);
    console.log(pairings);
  }, [pairings]);

  React.useEffect(() => {
    getOngoingPairings();
  }, [filters]);

  const getOngoingPairings = async () => {
    try {
      setIsLoading(true);
      const fileInfo = await getSurveyByIdFact(id);
      setFileInfo({ title: (fileInfo as any).name });
      const adminPairings = await fetchAdminOngoingPairingsFact(id, {
        index_id: id,
        page: filters.page,
        page_size: 10,
        search: filters.search.length > 0 ? filters.search : undefined,
      });
      setPairings(adminPairings.data);
      tableMeta.updatePagination({
        totalPages: adminPairings.data?.count_pages,
      });
      setIsLoading(false);
    } catch (err) {
      console.log(err);
      setIsLoading(false);
    }
  };

  const onDeletePair = (item: Array<string>) => {
    showToast({
      show: true,
      title: "Pairing Deleted",
      variant: "success",
      content: "Selectd pairing was deleted successfully.",
    });
    setShowDeletePopUp(false);
    setSelected([]);
    item.forEach((item) => {
      pairings.results.splice(
        pairings.results.findIndex((el) => el.target.emp_id === item),
        1
      );
    });
  };

  const onSearch = (search: string) => {
    setFilters((_filters) => ({
      ..._filters,
      search,
    }));
  };

  const onPageSelect = (page: number) => {
    setFilters((_filters) => ({
      ..._filters,
      page,
    }));
  };

  const getColumns = () => {
    const basic = [{ key: 2, label: "No.", hasSort: false }];

    return [
      ...basic,
      { key: 3, label: "Rater Full Name", hasSort: true },
      { key: 4, label: "Rater Employee ID", hasSort: false },
      { key: 5, label: "Rater Email", hasSort: true },
      { key: 6, label: "Target Full Name", hasSort: true },
      { key: 7, label: "Target Employee ID", hasSort: false },
      { key: 8, label: "Target Email", hasSort: true },
      { key: 9, label: "Survey Version", hasSort: false },
    ];
  };

  const getRows = () => {
    return pairings.results.map((item: Pairs, index: number) => {
      const isEven = index % 2 === 0 || index === 0;
      return (
        <Table.Row even={!isEven} key={item.target.emp_id}>
          <Table.Data width="70px">
            <Text.P1>{filters.page * 10 - 10 + index + 1}.</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>
              {item.rater.first_name} {item.rater.last_name}
            </Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.rater.emp_id}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.rater.email}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>
              {item.target.first_name} {item.target.last_name}
            </Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.target.emp_id}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.target.email}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.survey.name}</Text.P1>
          </Table.Data>
        </Table.Row>
      );
    });
  };

  const getFilterLayout = () => {
    // if (selected.length) {
    //   return (
    //     <Layout.Row className="pb-3">
    //       <Layout.Col xl={8}>
    //         <Text.H3 className="text-primary">
    //           {selected.length} Pairing{selected.length > 1 ? "s" : ""} Selected
    //         </Text.H3>
    //       </Layout.Col>
    //       <Layout.Col xl={4} className="text-right">
    //         {selected.length === 1 && (
    //           <Button
    //             onClick={onViewPairing}
    //             className="ml-2"
    //             variant="outline-primary"
    //           >
    //             View
    //           </Button>
    //         )}
    //         <Button
    //           onClick={() => setShowDeletePopUp(true)}
    //           className="ml-2"
    //           variant="outline-danger"
    //         >
    //           Delete
    //         </Button>
    //       </Layout.Col>
    //     </Layout.Row>
    //   );
    // }
    return (
      <Layout.Row className="py-3">
        <Layout.Col xl={3}>
          <FormControl.Search
            value={filters.search}
            onChange={(evt: any) => onSearch(evt.target.value)}
            placeholder="Search for name, email or emp id"
          />
        </Layout.Col>
        <Layout.Col xl={2}>
          <Table.CompareFilter
            title="Survey Version"
            selected={"All"}
            options={pairings?.surveys?.map((s: any) => ({
              title: s.name,
              key: s.id,
            }))}
            placeholder="Survey Title"
            onSelect={(item: string) => {
              console.log(item);
            }}
          />
        </Layout.Col>
      </Layout.Row>
    );
  };

  const getDeleteView = () => {
    if (selected.length > 0) {
      return (
        <DeletePair
          show={showDeletePopUp}
          onAction={() => onDeletePair(selected)}
          onHide={() => setShowDeletePopUp(false)}
        />
      );
    }
  };

  return (
    <Div className="bg-white px-3 pb-5">
      <CustomHeader
        metaItem={
          !selected.length && (
            <>
              <Button
                type="button"
                className="mx-1"
                variant="primary"
                size="lg"
                onClick={() => setShowModal(true)}
              >
                Add Pairings
              </Button>
              <Dropdown>
                <Dropdown.Toggle
                  variant="outline-primary"
                  size="sm"
                  className="mx-1"
                >
                  ...
                </Dropdown.Toggle>

                <Dropdown.Menu
                  align="right"
                  style={{ zIndex: 1090, width: 200 }}
                  className="mt-2"
                >
                  <Div
                    onClick={async () => {
                      await pairingsFileDownload({ index_id: id, f: "xlsx" });
                    }}
                    className="cursor-pointer pl-md-3 my-1"
                  >
                    Download
                  </Div>
                </Dropdown.Menu>
              </Dropdown>
            </>
          )
        }
        title={
          <Div>
            {!selected.length && (
              <Text.H2 className="pt-2 d-block text-primary">
                {isLoading ? <Placeholder width="col-12" /> : fileInfo.title}
              </Text.H2>
            )}
          </Div>
        }
        breadcrumbs={[
          {
            label: "Dataload",
            icon: <Group className="grey-icon__svg" />,
            route: appUrls.admin.dataLoad.default,
          },
          { label: "Ongoing / Associated" },
          { label: fileInfo.title }
        ]}
      />
      <Layout.Container fluid className="px-0 pt-3">
        {getFilterLayout()}
        <Table
          columns={getColumns()}
          rows={pairings.results}
          customRows
          render={() => getRows()}
          hasPagination
          activePage={filters.page}
          pages={tableMeta.totalPages}
          onPageSelect={onPageSelect}
          isLoading={isLoading}
        />
      </Layout.Container>
      {getDeleteView()}
      <AddPairingModal
        onHide={() => setShowModal(false)}
        show={showModal}
        setShowModal={setShowModal}
        selectedVersion={selectedVersion}
        surveyIndex={id}
        getPairings={getOngoingPairings}
      />
    </Div>
  );
};

export default ManageOngoingData;
