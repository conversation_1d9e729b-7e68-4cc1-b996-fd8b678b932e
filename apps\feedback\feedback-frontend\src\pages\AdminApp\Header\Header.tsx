import React from "react";
import { Link } from "react-router-dom";
import {
  Navbar,
  Nav,
  Dropdown,
  Image,
  Div,
  Layout,
  Text,
} from "unmatched/components";
import useSession from "unmatched/modules/session/hook";
import util from "unmatched/utils";

// import PropTypes from 'prop-types'

const Header = (props: {
  onLogout: Function;
  name: string;
  togglePasswordModal: Function;
}) => {
  const { onLogout, name, togglePasswordModal } = props;
  const { client, user } = useSession();
  const settingRoutes = [
    {
      id: 3,
      title: "Switch to user",
      route: util.appUrls.user.default,
    },
    {
      id: 2,
      title: "Change password",
      onClick: () => togglePasswordModal(true)
    },
    {
      id: 1,
      title: "Logout",
      route: "",
      onClick: () => onLogout && onLogout(),
    },
  ];

  const getOptionsTemplate = () => {
    return (
      <Dropdown>
        <Dropdown.Toggle
          variant="primary"
          size="sm"
          style={{ width: 30, height: 30 }}
        >
          {name}
        </Dropdown.Toggle>

        <Dropdown.Menu
          align="right"
          style={{ zIndex: 1090, width: 200 }}
          className="mt-2"
        >
          <Div className="font-weight-bold pl-md-3 my-1">
            <Text.H3>{`${user?.firstName} ${user?.lastName}`}</Text.H3>
          </Div>
          <Div className="fs-12 pl-md-3 pr-3 text-break text-muted text-truncate">
            {user?.email}
          </Div>
          <hr className="my-2" />
          {settingRoutes.map((item: any) => {
            return (
              <Link key={item.id} to={item.route || "#"}>
                <Dropdown.Item
                  as={Div}
                  className="fs-14 cursor-pointer px-3"
                  onSelect={() => item.onClick && item.onClick()}
                >
                  {item.title}
                </Dropdown.Item>
              </Link>
            );
          })}
        </Dropdown.Menu>
      </Dropdown>
    );
  };

  return (
    <Navbar
      bg="none"
      variant="dark"
      className="p-0"
      fixed="top"
      style={{ height: 60, background: "#213356" }}
    >
      <Layout.Row className="w-100 h-100">
        <Layout.Col
          xl={2}
          lg={2}
          md={2}
          sm={3}
          xs={2}
          className="align-self-center py-1"
        >
          <Link to={util.appUrls.admin.default}>
            <Image
              src={client.darkLogo}
              width="100%"
              height={"36px"}
              className="mx-3"
            />
          </Link>
        </Layout.Col>
        <Layout.Col
          xl={10}
          lg={10}
          md={10}
          sm={9}
          xs={10}
          className="d-flex align-self-center align-items-center py-0"
          style={{ height: 40 }}
        >
          <Nav className="ml-auto">
            {getOptionsTemplate()}
            {/* <SidebarMenuItem
              isActive
              buttonContent={name}
              bottom
              title="Session"
              items={settingRoutes}
            /> */}
            {/* <Nav.Link href="#pricing">Pricing</Nav.Link> */}
          </Nav>
        </Layout.Col>
      </Layout.Row>
    </Navbar>
  );
};

Header.propTypes = {};

export default Header;
