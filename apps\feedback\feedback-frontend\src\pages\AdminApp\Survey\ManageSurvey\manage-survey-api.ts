import axios, { AxiosResponse } from "axios";
import util from "unmatched/utils";

export const getMetaLabelsFact = (params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return axios
    .get(`${util.apiUrls.META_LABELS}`, config)
    .then(({ data }: AxiosResponse) => {
      return data;
    });
};

export const getAllDndUsers = (surveyID: string, params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return axios
    .get(`${util.apiUrls.DND_SERVICE(surveyID)}`, config)
    .then(({ data }: AxiosResponse) => {
      return data;
    });
};
export const addDndUsers = (surveyID: string, data: any) => {
  return axios.post(`${util.apiUrls.DND_SERVICE(surveyID)}`, data);
};

export const deleteDndUsers = (surveyID: string, data: any) => {
  console.log(data);
  return axios.delete(`${util.apiUrls.DND_SERVICE(surveyID)}`, { data });
};
