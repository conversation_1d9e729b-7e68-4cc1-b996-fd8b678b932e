image: nikolaik/python-nodejs:latest

#AWS access keys stored as secret variables
variables:
  AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
  AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY

#This declares the pipeline stages
stages:
  - build
  - deploy

cache:
  key: "$CI_PIPELINE_ID"
  paths:
    - node_modules/
    - build/

build:
  stage: build
  script:
    - npm install
    - CI=false npm run build
    - mkdir build/feedback/
    - mv build/static/ build/feedback/static/
  artifacts:
    paths:
      - build/

deploy:
  stage: deploy
  environment:
  before_script:
    - pip3 install awscli
  script:
    - aws configure set profile unmatched-v2-staging
    - aws configure set aws_access_key_id "$AWS_ACCESS_KEY_ID"
    - aws configure set aws_secret_access_key "$AWS_SECRET_ACCESS_KEY"
    - npm run-script deploy-$CI_COMMIT_BRANCH
  dependencies:
    - build
  rules:
    - if: '$CI_COMMIT_BRANCH == "master" || $CI_COMMIT_BRANCH ==  "development" || $CI_COMMIT_BRANCH ==  "review"'
      when: always
