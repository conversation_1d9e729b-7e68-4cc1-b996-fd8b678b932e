import { Layout, Button, Icon } from "@unmatchedoffl/ui-core";
import React from "react";
import styled from "styled-components";
// import { Layout, Button, Icon } from "../../../components";

const HoveredFlex = styled(Layout.Flex).attrs({
  className: "my-2",
})`
  .close-icon {
    display: none;
  }
  &:hover {
    .close-icon {
      display: inline;
    }
  }
`;

const HoveredViewOnlyFlex = styled(Layout.Flex).attrs({
  className: "my-2",
})`
  .close-icon {
    display: none;
  }
`;

interface Props {
  typeItem: React.ReactNode;
  children: React.ReactNode;
  options: any;
  viewOnly?: any;
  onRemove: Function;
  quesType?: any;
}

{
  /* <OptionLabel
          setValidations={options.setValidations}
          text={options.value}
          onSubmit={options.onSubmit}
          option={options}
          viewOnly={viewOnly}
        /> */
}

const QuestionTypeWrapper = (props: Props) => {
  const { typeItem, options, viewOnly, onRemove, quesType } = props;
  const Wrap = viewOnly ? HoveredViewOnlyFlex : HoveredFlex;
  return (
    <Wrap key={options.id}>
      <Layout.FlexItem className="pt-2 pr-2">{typeItem}</Layout.FlexItem>
      <Layout.FlexItem>{props.children}</Layout.FlexItem>
      <Layout.FlexItem>
        {!!options.id && quesType !=="QuestionRating" && (
          <Button
            variant="link"
            preventDefault
            stopPropogation
            onClick={() => {
              onRemove(options.id);
            }}
            className="close-icon text-dark"
          >
            <Icon icon="fal fa-times" />
          </Button>
        )}
      </Layout.FlexItem>
    </Wrap>
  );
};

export default QuestionTypeWrapper;
