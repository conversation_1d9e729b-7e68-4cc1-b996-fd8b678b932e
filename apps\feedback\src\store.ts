import { combineReducers, configureStore } from "@reduxjs/toolkit";
// import logger from "redux-logger";
import {
  persistStore,
  FLUSH,
  REHYDRATE,
  PAUSE,
  PERSIST,
  PURGE,
  REGISTER,
} from "redux-persist";
import util from "unmatched/utils";
import rootReducer from "./pages/app-reducer";

export const store = configureStore({
  reducer: combineReducers(rootReducer),
  middleware: (getDefaultMiddleware: Function) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
      },
    }),
  devTools: !util.env.isProd,
});

export const persistor = persistStore(store);
