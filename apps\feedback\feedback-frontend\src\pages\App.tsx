// Node modules
import React, { useState } from "react";
import { HashRouter as Router, Route } from "react-router-dom";
import styled from "styled-components";
// Unmatched helpers
import VerifySession from "unmatched/modules/session/Session";
import useToastr from "unmatched/modules/toastr/hook";
import { Button, Div, Icon, PageNotFound, Toast } from "unmatched/components";
import { useHistory } from "unmatched/hooks";
// Helpers
import ROUTES from "./app-routes";
// Components
import AppRoutes from "./AppRoutes";
import ChangePasswordModal from "./ChangePasswordModal";
import CacheInvalidator from "../unmatched/components/CacheInvalidator";

export const PasswordModalContext = React.createContext<any>({
  show: false,
  toggle: () => undefined,
});

const ToastrContainer = styled.div`
  position: fixed;
  right: 30px;
  top: 30px;
  max-width: 900px;
  min-width: 400px;
  z-index: 999999;
`;
const ScrollToTop = () => {
  const history = useHistory();

  React.useEffect(() => {
    const unlisten = history.listen((location, action) => {
      if (action !== "POP") {
        window.scrollTo(0, 0);
      }
    });
    return () => unlisten();
  }, [history]);

  return null;
};

const App = () => {
  const { toastrs, resetToast } = useToastr();
  const [showChangePassModal, setShowChangePassModal] = useState(false);

  return (
    <CacheInvalidator>
      {({ loading, isLatestVersion, refreshCacheAndReload }: any) => {
        if (loading) return null;
        if (!isLatestVersion) {
          refreshCacheAndReload();
        }
        return (
          <Router>
            <ScrollToTop />
            <Button
              style={{
                position: "fixed",
                bottom: 250,
                width: 150,
                height: 35,
                right: "-58px",
                zIndex: 9999,
                transform: "rotate(-90deg)",
                borderRadius: "8px 8px 0px 0px",
                background: "#14171D",
              }}
              variant="primary-outline"
              className=" px-4 py-2 shadow-lg border-0 d-flex justify-content-center align-items-center text-white fs-12"
              onClick={() => window.Beacon("toggle")}
            >
              {/* <Div
                style={{  width: 150, height: 50, zIndex: 999, background: "#e6e6e6"}}
                className="text-white d-flex flex-row"
              > */}
              <Icon icon="far fa-comment mr-2" /> Need Help?
              {/* </Div> */}
            </Button>
            <VerifySession>
              <Div>
                <PasswordModalContext.Provider
                  value={{
                    show: showChangePassModal,
                    toggle: setShowChangePassModal,
                  }}
                >
                  <AppRoutes routes={ROUTES}>
                    <Route path="*">
                      <PageNotFound />
                    </Route>
                  </AppRoutes>
                </PasswordModalContext.Provider>
                <ToastrContainer>
                  {toastrs.map((item: any, index: number) => (
                    <Toast
                      variant={item.variant || "danger"}
                      show
                      key={`item.title-${index}`}
                      title={item.title}
                      delay={item.delay || 4000}
                      onClose={() => resetToast(index)}
                      content={item.content}
                    />
                  ))}
                </ToastrContainer>
                <ChangePasswordModal
                  show={showChangePassModal}
                  onHide={() => setShowChangePassModal(false)}
                  aria-labelledby="contained-modal-title-vcenter"
                  centered
                  dialogClassName="modal-60w"
                />
              </Div>
            </VerifySession>
          </Router>
        );
      }}
    </CacheInvalidator>
  );
};

export default App;
