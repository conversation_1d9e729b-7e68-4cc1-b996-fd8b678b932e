import React from "react";
import styled from "styled-components";
import { Div, Placeholder } from "unmatched/components";

interface QuestionsProgressProps {
  label: string | React.ReactNode;
  total: number;
  status: string;
  isLoading: boolean;
}

const QuestionsProgress = (props: QuestionsProgressProps) => {
  const { total, status, label, isLoading } = props;

  const getTemplate = () => {
    if (isLoading) {
      return (
        <>
          <Placeholder width="col-2" />
          <Placeholder width="col-2" />
        </>
      );
    }
    return (
      <>
        <p className="m-0">{total}</p>
        <p className="m-0 label">{label}</p>
      </>
    );
  };

  return (
    <QuestionStatus className={`text-${status} pl-4 py-2`}>
      {getTemplate()}
    </QuestionStatus>
  );
};

const QuestionStatus = styled(Div)`
  font-size: 14px;
  font-weight: 600;
  margin: 5px 0;
  .label {
    font-size: 12px;
  }
`;

export default QuestionsProgress;
