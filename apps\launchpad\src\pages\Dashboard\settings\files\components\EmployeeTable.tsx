import React from "react";
import { Table } from "@repo/ui/components/table";
import { EmployeeTableHeader } from "./EmployeeTableHeader";
import { EmployeeTableBody } from "./EmployeeTableBody";
import { Employee } from "../filesService";

interface EmployeeTableProps {
  loadingEmployees: boolean;
  employees: Employee[];
  pagination: {
    pageIndex: number;
    pageSize: number;
    pageCount: number;
    totalItems: number;
  };
  handleSort: (field: keyof Employee | string) => void;
  getSortIcon: (field: keyof Employee | string) => React.ReactNode;
  onRowClick?: (employee: Employee) => void;
}

export const EmployeeTable: React.FC<EmployeeTableProps> = ({
  loadingEmployees,
  employees,
  pagination,
  handleSort,
  getSortIcon,
  onRowClick,
}) => {
  return (
    <div className="rounded-md border">
      <Table>
        <EmployeeTableHeader
          handleSort={handleSort}
          getSortIcon={getSortIcon}
        />
        <EmployeeTableBody
          loadingEmployees={loadingEmployees}
          employees={employees}
          pagination={pagination}
          onRowClick={onRowClick}
        />
      </Table>
    </div>
  );
};
