import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router';
import { USER_ROUTES } from '@/app.routes';
import axiosInstance from '@/lib/axios';

interface SurveyInfo {
  id: string;
  title: string;
  description: string;
  type: string;
  resourcetype?: string;
  deadline?: string;
  start?: string;
}

const Terms: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [surveyInfo, setSurveyInfo] = useState<SurveyInfo | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchSurveyInfo = async () => {
      if (!id) {
        setError('Survey ID is required');
        setIsLoading(false);
        return;
      }

      try {
        // Use the same API endpoint as the legacy app
        const response = await axiosInstance.get(`/survey/index/${id}/`);
        const surveyData = response.data;

        console.log('Survey data received:', surveyData);

        // Check if survey is still active (similar to legacy app logic)
        const deadline = surveyData.deadline;
        if (deadline && new Date(deadline) < new Date()) {
          console.log('Survey deadline passed, redirecting to survey list');
          navigate(USER_ROUTES().dashboard.surveyList);
          return;
        }

        const surveyInfo: SurveyInfo = {
          id: surveyData.id || id,
          title: surveyData.title || 'Survey',
          description: surveyData.description || 'No description available.',
          type: surveyData.resourcetype || surveyData.type || 'Unknown',
          resourcetype: surveyData.resourcetype,
          deadline: surveyData.deadline,
          start: surveyData.start
        };

        console.log('Processed survey info:', surveyInfo);
        setSurveyInfo(surveyInfo);
        setIsLoading(false);
      } catch (err) {
        console.error('Error fetching survey info:', err);

        // For testing purposes, use mock data when API fails
        console.log('Using mock data for testing...');

        // Test different survey types based on the survey ID
        let surveyType = 'SurveyIndexUpward'; // default
        let surveyTitle = 'Test Upward Review Survey';

        if (id?.includes('engagement') || id?.includes('eng')) {
          surveyType = 'SurveyIndexEngagement';
          surveyTitle = 'Test Engagement Survey';
        } else if (id?.includes('self') || id?.includes('eval')) {
          surveyType = 'SurveyIndexSelf';
          surveyTitle = 'Test Self Evaluation Survey';
        } else if (id?.includes('exit')) {
          surveyType = 'SurveyIndexExit';
          surveyTitle = 'Test Exit Survey';
        } else if (id?.includes('360')) {
          surveyType = 'SurveyIndex360';
          surveyTitle = 'Test 360 Degree Survey';
        }

        const mockSurveyInfo: SurveyInfo = {
          id: id || 'test-survey',
          title: surveyTitle,
          description: `This is a test ${surveyType} for navigation testing.\n\nPlease click "Start Survey" to test the navigation functionality.\n\nSurvey Type: ${surveyType}`,
          type: surveyType,
          resourcetype: surveyType,
          deadline: '2024-12-31',
          start: '2024-01-01'
        };

        console.log('Using mock survey info:', mockSurveyInfo);
        setSurveyInfo(mockSurveyInfo);
        setIsLoading(false);
        // Don't set error for testing
      }
    };

    fetchSurveyInfo();
  }, [id, navigate]);

  const navigateToSurvey = () => {
    if (!surveyInfo) return;

    const routes = USER_ROUTES().dashboard;
    // Use the same logic as legacy app - check both resourcetype and type fields
    const surveyType = surveyInfo.resourcetype || surveyInfo.type;

    console.log('Survey type for navigation:', surveyType, 'Survey info:', surveyInfo);

    // Navigate based on survey type (matching legacy app logic exactly)
    switch (surveyType) {
      case 'SurveyIndex360':
      case 'SurveyIndexUpward':
        navigate(routes.upwardReview.getUrl(id!));
        break;
      case 'SurveyIndexEngagement':
        navigate(routes.getEngagmentUrl(id!));
        break;
      case 'SurveyIndexSelf':
        navigate(routes.getSelfEvaluationUrl(id!));
        break;
      case 'SurveyIndexExit':
        navigate(routes.getExitUrl(id!));
        break;
      default:
        console.warn('Unknown survey type:', surveyType, 'Available routes:', routes);
        // Fallback to upward review for unknown types (same as legacy app)
        navigate(routes.upwardReview.getUrl(id!));
        break;
    }
  };

  const onStartSurvey = async () => {
    try {
      // Log survey visit (similar to legacy app)
      try {
        await axiosInstance.post('/survey/visit/', { index: id });
      } catch (visitError) {
        console.warn('Failed to log survey visit:', visitError);
        // Continue anyway - this is not critical
      }

      navigateToSurvey();
    } catch (error) {
      console.error('Error starting survey:', error);
      // TODO: Add proper error handling/toast notification
    }
  };

  if (error) {
    return (
      <div className="flex min-h-screen w-full items-center justify-center text-red-500">
        {error}
      </div>
    );
  }

  return (
    <div className="w-full p-6">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
          {/* Header */}
          <div className="bg-blue-500 text-white p-6 rounded-t-lg">
            <h1 className="text-2xl font-semibold">
              {isLoading ? (
                <div className="h-8 bg-blue-400 rounded w-3/4 animate-pulse"></div>
              ) : (
                surveyInfo?.title
              )}
            </h1>
          </div>

          {/* Body */}
          <div className="p-6 min-h-[400px]">
            <div className="prose max-w-none">
              {isLoading ? (
                <div className="space-y-3">
                  <div className="h-4 bg-gray-200 rounded w-full animate-pulse"></div>
                  <div className="h-4 bg-gray-200 rounded w-11/12 animate-pulse"></div>
                  <div className="h-4 bg-gray-200 rounded w-full animate-pulse"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/3 animate-pulse"></div>
                </div>
              ) : (
                <div className="text-gray-700 leading-relaxed">
                  {surveyInfo?.description.split('\n').map((line, index) => {
                    if (line.trim() === '') {
                      return <br key={index} />;
                    }
                    return (
                      <span key={index}>
                        {line}
                        <br />
                      </span>
                    );
                  })}
                </div>
              )}
            </div>

            {/* Action Buttons */}
            <div className="flex gap-3 mt-8">
              <button
                onClick={onStartSurvey}
                disabled={isLoading}
                className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-6 py-2 rounded-md font-medium transition-colors"
              >
                Start Survey
              </button>
              
              {/* TODO: Add FAQ button when FAQ functionality is implemented */}
              {/* <button
                onClick={() => setShowFAQ(true)}
                disabled={isLoading}
                className="border border-blue-600 text-blue-600 hover:bg-blue-50 px-6 py-2 rounded-md font-medium transition-colors"
              >
                FAQs
              </button> */}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Terms;
