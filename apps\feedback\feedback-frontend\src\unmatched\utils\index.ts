import assets from "assets";
import urls from "./urls";
import canAccess from "./permissions";
import api from "./api";
import { util } from "@unmatchedoffl/ui-core";

const utilModule = {
  ...util,
  ...assets,
  ...urls,
  enums: {
    ...util.enums,
    Survey: {
      ...util.enums.Survey,
      Exit: "SurveyIndexExit",
    },
  },
  api,
  canAccess,
  noSearchRecordsFoundMsg:
    "Please check and try again with a different keyword, or filters.",
  getSurveyTypes: {
    SurveyIndexUpward: "Upward Feedback",
    SurveyIndexEngagement: "Engagement Survey",
    SurveyIndexSelf: "Self Assessment Survey",
    SurveyIndex360: "360 Degree Feedback",
    SurveyIndexExit: "Exit Survey"
  },
  daysBetween: (date1: any, date2: any) => {

    // The number of milliseconds in one day
    const ONE_DAY = 1000 * 60 * 60 * 24;

    // Calculate the difference in milliseconds
    const differenceMs = Math.abs(date1 - date2);

    // Convert back to days and return
    return Math.round(differenceMs / ONE_DAY);

},
getTimeZone: () => {
  const offset = new Date().getTimezoneOffset(), o = Math.abs(offset);
  return (offset < 0 ? "+" : "-") + ("00" + Math.floor(o / 60)).slice(-2) + ":" + ("00" + (o % 60)).slice(-2);
}
};

export default utilModule;
