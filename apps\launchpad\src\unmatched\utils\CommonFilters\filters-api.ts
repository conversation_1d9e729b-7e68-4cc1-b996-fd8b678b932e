import axios, { AxiosResponse } from "axios";
// import util from "unmatched/utils";
import api from "../api";
import apiUrls from "../urls/api-urls";
import _ from "lodash";

export const getMetaLabelsFact = (params?: any, meta?: any) => {
  const config = api.getConfigurations(params, {
    ...meta,
  });
  return axios
    .get(`${apiUrls.META_LABELS}`, config)
    .then(({ data }: AxiosResponse) => {
      let response = {};
      data.labels.forEach(({ field, display_name, type }: any) => {
        response = {
          ...response,
          [field]: {
            label: display_name,
            values: _.get(data.values, field),
            type
          },
        };
      });
      return response;
    });
};
