import axios, { AxiosResponse } from "axios";
import util from "unmatched/utils";

export const reportStatsFact = (params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return axios
    .get(`${util.apiUrls.REPORT_STATS}`, config)
    .then(({ data }: AxiosResponse) => {
      if (!data) return { data: [], totalPages: 0 };
      const { results, count_pages, count_items } = data || {};
      if (!results) return { data: [], totalPages: 0 };
      return {
        data: results.map((item: any) => {
          return {
            totalReviewee: item.total_reviewee,
            receivingReports: item.receiving_reports,
            notReceiving: item.not_receiving,
            reportByWaiver: item.report_by_waiver,
            group: item.group,
          };
        }),
        totalPages: count_pages,
        totalElements: count_items,
      };
    });
};

export const targetReturnFact = (params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
    responseType: "blob",
  });
  return axios.get(`${util.apiUrls.TOTAL_TARGET_RESPONSES}`, config).then(
    ({ data }: AxiosResponse) => {
      const url = window.URL.createObjectURL(new Blob([data]));
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", `data.zip`);
      document.body.appendChild(link);
      link.click();
    },
    (err: any) => err
  );
};

export const downloadPartcipantsFact = (params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
    responseType: "blob",
  });
  return axios.get(`${util.apiUrls.PARTICIPANTS_DOWNLOAD}`, config).then(
    ({ data }: AxiosResponse) => {
      const url = window.URL.createObjectURL(new Blob([data]));
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", `participants_stats.xlsx`);
      document.body.appendChild(link);
      link.click();
    },
    (err: any) => err
  );
};
