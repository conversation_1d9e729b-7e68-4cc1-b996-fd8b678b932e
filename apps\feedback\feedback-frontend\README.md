## Available Scripts

In the project directory, you can run:

### `npm start`

Runs the app in the development mode.\
Open [http://localhost:3000](http://localhost:3000) to view it in the browser.

The page will reload if you make edits.\
You will also see any lint errors in the console.

### `npm test`

Launches the test runner in the interactive watch mode.\
See the section about [running tests](https://facebook.github.io/create-react-app/docs/running-tests) for more information.

### `npm run build`

Builds the app for production to the `build` folder.\
It correctly bundles React in production mode and optimizes the build for the best performance.

The build is minified and the filenames include the hashes.\
Your app is ready to be deployed!

## Project style guide

### Component structure:

- Import node modules (components/helpers etc)
- Import helpers (custom hooks/modules/functions etc)
- Import components
- import styles
- define interfaces
- define constants/types/enums
- Pure functions
- Reusable/styled components which are only needed in current component
- const ComponentName = // implementation
- ComponentName.defaultPops = {}
- export default ComponentName;

### Component Implementation:

- define hooks
- define constants/variables
- define functions
- define Template functions
- return template

### Redux structure:

- If module’s store is simple, implement selectors/actions/action types in one file and export reducer.
  - module-store.ts
- If modules store is complex split store into 3 files.
  - module-selectors.ts
  - module-actions(actions + action types)
  - module-reducer(exports reducer).

### Naming conventions:

- PascalCase

  - Component folder name
  - Pages
  - Component file name
  - Component function name
  - types
  - interfaces

- camelCase

  - functions
  - let/var variable
  - const variables(Only Objects and Arrays).

- UPPER_CASE

  - constant values
  - enums

- lower-case
  - store/actions/selectors file names
  - helper file name
  - folders name
  - styles (file-names/classes/id’s)

### Styles:

- Should try to use util classes for (padding/margin, color/background colors) provided by bootstrap, instead of implementing our own.
- Should only use colors in the form of (‘primary’, ‘secondary’, ‘success’ etc).
- We can use styled-components when there is no need to extend CSS from bootstrap/any other plugins(For reference Text component).

### Folder structure:

- components(contains design system/ reusable components which are used across system)
- helpers
  - hooks
  - constants(API_URLS/ appUrls or fixed constants that doesnt change)
  - enums (Types/Status.)
  - formik helpers
  - types
  - api
  - store
- assets
  - images across application
  - custom icons.
- styles
  - global style helpers/styles
- pages
  - Auth -> (Login,Activation…...)
  - UserApp
  - AdminApp
- CreateSurvey(Choose, Create, Questions etc).
  - ManageSurvey.
