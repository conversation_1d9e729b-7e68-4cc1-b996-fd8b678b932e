import React from "react";
import styled from "styled-components";
// import * as yup from "yup";
import {
  Modal,
  Text,
  Button,
  Form,
  FormGroup,
  FormControl,
  Icon,
} from "unmatched/components";
import { useFormik } from "unmatched/hooks";
import util from "unmatched/utils";

const { getFieldErrorMessage, isFieldInvalid, yup } = util.formik;
const _ = util.lib;

const ModalHeader = styled(Modal.Header)`
  background: #fbfbfb;
`;

const ModalFooter = styled(Modal.Footer)`
  border: none;
  padding: 2rem;
`;

const updateUserFact = () =>
  new Promise((resolve: Function) => {
    window.setTimeout(() => {
      resolve();
    }, 400);
  });

const UserItem = ({ title, value }: any) => {
  return (
    <FormGroup className="pb-4">
      <FormGroup.Label>{title}</FormGroup.Label>
      <Text.H3>{value}</Text.H3>
    </FormGroup>
  );
};

const ModifyUserDetails = (props: any) => {
  const { show, onHide, user, onSave } = props;

  const initialValues = _.pick(user, ["firstName", "lastName", "email"]);

  const onSubmit = async (data: any) => {
    formik.setSubmitting(true);
    try {
      await updateUserFact();
      onSave({
        ...user,
        ...data,
      });
      formik.setSubmitting(false);
    } catch (err) {
      formik.setSubmitting(false);
    }
  };

  const formik = useFormik({
    initialValues,
    validationSchema: yup.object().shape({
      firstName: yup.string().required(),
      lastName: yup.string().required(),
      email: yup.string().email().required(),
    }),
    onSubmit,
  });

  return (
    <Modal
      show={show}
      onHide={onHide}
      size="lg"
      aria-labelledby="contained-modal-title-vcenter"
      centered
    >
      <Form onSubmit={formik.handleSubmit}>
        <ModalHeader closeButton>
          <Modal.Title id="contained-modal-title-vcenter">
            User Details
          </Modal.Title>
        </ModalHeader>
        <Modal.Body>
          <FormGroup className="pb-4">
            <FormGroup.Label>First Name</FormGroup.Label>
            <FormControl.Text
              name="firstName"
              isInvalid={isFieldInvalid(formik, "firstName")}
              autoFocus
              value={formik.values["firstName"]}
              onBlur={formik.handleBlur}
              onChange={formik.handleChange}
              placeholder="First Name"
            ></FormControl.Text>
            <FormGroup.InValidFeedback
              text={getFieldErrorMessage(formik, "firstName")}
            ></FormGroup.InValidFeedback>
          </FormGroup>
          <FormGroup className="pb-4">
            <FormGroup.Label>Last Name</FormGroup.Label>
            <FormControl.Text
              name="lastName"
              isInvalid={isFieldInvalid(formik, "lastName")}
              autoFocus
              value={formik.values["lastName"]}
              onBlur={formik.handleBlur}
              onChange={formik.handleChange}
              placeholder="Last Name"
            ></FormControl.Text>
            <FormGroup.InValidFeedback
              text={getFieldErrorMessage(formik, "lastName")}
            ></FormGroup.InValidFeedback>
          </FormGroup>
          <FormGroup className="pb-4">
            <FormGroup.Label>Email</FormGroup.Label>
            <FormControl.Email
              name="email"
              isInvalid={isFieldInvalid(formik, "email")}
              autoFocus
              value={formik.values["email"]}
              onBlur={formik.handleBlur}
              onChange={formik.handleChange}
              placeholder="Email address"
            ></FormControl.Email>
            <FormGroup.InValidFeedback
              text={getFieldErrorMessage(formik, "email")}
            ></FormGroup.InValidFeedback>
          </FormGroup>
          <UserItem title="Location" value={user.location} />
          <UserItem title="Department" value={user.department} />
          <UserItem title="Employee ID" value={user.empId} />
        </Modal.Body>
        <ModalFooter>
          <Button
            disabled={formik.isSubmitting}
            variant="outline-primary"
            className="mx-1"
            type="submit"
          >
            {formik.isSubmitting && <Icon icon="fal fa-spinner" spin />} Modify
          </Button>
        </ModalFooter>
      </Form>
    </Modal>
  );
};

export default ModifyUserDetails;
