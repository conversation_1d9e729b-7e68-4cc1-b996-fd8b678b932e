import React, { useEffect, useState } from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Upload, Loader2 } from "lucide-react";
import { toast } from "sonner";
import { AxiosError } from "axios";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@repo/ui/components/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
  useFormField,
} from "@repo/ui/components/form";
import { Label } from "@repo/ui/components/label";
import { Input } from "@repo/ui/components/input";
import { Button } from "@repo/ui/components/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@repo/ui/components/select";
import {
  FormattedMetadataField,
  getRequiredUserFieldService,
  addEmployeeService,
  AddEmployeePayload
} from "../employeesService";
import API_URLS from "@/unmatched/utils/urls/api-urls";

// Custom FormLabel that doesn't turn red on error
const CustomFormLabel = ({ className, children, ...props }: React.ComponentProps<typeof Label>) => {
  const { formItemId } = useFormField();

  return (
    <Label
      data-slot="form-label"
      className={className}
      htmlFor={formItemId}
      {...props}
    >
      {children}
    </Label>
  );
};

interface AddEmployeeModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  metadataFields: FormattedMetadataField[];
  onAddEmployee: (data: AddEmployeePayload) => void;
}

export const AddEmployeeModal: React.FC<AddEmployeeModalProps> = ({
  open,
  onOpenChange,
  metadataFields,
  onAddEmployee,
}) => {
  // State to control whether to show the form or just the field list
  const [showForm, setShowForm] = useState(false);
  // State to store required fields
  const [requiredFields, setRequiredFields] = useState<string[]>([]);
  const [isLoadingRequiredFields, setIsLoadingRequiredFields] = useState(false);
  // State to track form submission
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch required fields when modal opens
  useEffect(() => {
    if (open) {
      const fetchRequiredFields = async () => {
        setIsLoadingRequiredFields(true);
        try {
          // Simplified implementation matching the old app
          const fields = await getRequiredUserFieldService();
          console.log("Required fields fetched:", fields);
          setRequiredFields(fields);
        } catch (error) {
          console.error("Error fetching required fields:", error);
          // Set default required fields if fetch fails
          setRequiredFields([
            "emp_id",
            "email",
            "first_name",
            "last_name",
          ]);
        } finally {
          setIsLoadingRequiredFields(false);
        }
      };

      fetchRequiredFields();
    }
  }, [open]);

  // Create form with dynamic schema
  const dynamicSchema = React.useMemo(() => {
    const schema: Record<string, z.ZodType<string | undefined>> = {
      emp_id: z.string().optional(),
      email: z.string().email({ message: "Please enter a valid email address" }),
      first_name: z.string().min(1, { message: "First name is required" }),
      last_name: z.string().min(1, { message: "Last name is required" }),
    };

    // Add dynamic fields from metadata as optional
    metadataFields.forEach((field) => {
      schema[field.field] = z.string().optional();
    });

    return z.object(schema);
  }, [metadataFields]);

  const form = useForm<z.infer<typeof dynamicSchema>>({
    resolver: zodResolver(dynamicSchema),
    defaultValues: {
      emp_id: "",
      email: "",
      first_name: "",
      last_name: "",
      // Dynamic fields will be initialized with empty strings
      ...Object.fromEntries(
        metadataFields.map((field) => [field.field, ""])
      ),
    },
  });

  // Reset form when metadata fields change
  useEffect(() => {
    // Update form default values when metadata fields change
    form.reset({
      ...form.getValues(),
      ...Object.fromEntries(
        metadataFields.map((field) => [field.field, ""])
      ),
    });
  }, [metadataFields, form]);

  const onSubmit = async (formData: z.infer<typeof dynamicSchema>) => {
    try {
      setIsSubmitting(true);

      // Format the data according to the API requirements
      const employeeData: AddEmployeePayload = {
        emp_id: formData.emp_id === "" ? undefined : formData.emp_id,
        email: formData.email || "",
        first_name: formData.first_name || "",
        last_name: formData.last_name || "",
        is_active: true,
        metadata: {}
      };

      // Extract metadata fields from the form data
      metadataFields.forEach(field => {
        const fieldName = field.field;
        const fieldValue = formData[fieldName as keyof typeof formData];
        if (fieldValue) {
          employeeData.metadata[fieldName] = fieldValue;
        }
      });

      // Call the API to add the employee
      await addEmployeeService(employeeData);

      // Show success message
      toast.success("Employee added successfully", {
        description: `${formData.first_name} ${formData.last_name} has been added to the system.`,
      });

      // Call the parent component's callback
      onAddEmployee(employeeData);

      // Reset the form and close the modal
      form.reset();
      setShowForm(false);
      onOpenChange(false);
    } catch (error: unknown) {
      // Add detailed logging to see the exact structure of the error
      console.error("Error adding employee:", error);

      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as AxiosError<Record<string, unknown>>;
        console.log("Axios error response:", axiosError.response);
        console.log("Response data:", axiosError.response?.data);

        if (axiosError.response?.data) {
          console.log("Response data type:", typeof axiosError.response.data);
          console.log("Response data keys:", Object.keys(axiosError.response.data));
        }
      }

      // Extract error message from the server response
      let errorMessage = "An unexpected error occurred";

      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as AxiosError<Record<string, unknown>>; // Type assertion for axios error

        // Check for different error response formats
        if (axiosError.response?.data?.e && typeof axiosError.response.data.e === 'string') {
          // Format from the screenshot: { e: "An user is already registered with this email address" }
          errorMessage = axiosError.response.data.e;
        } else if (axiosError.response?.data?.email) {
          // Django REST format for field errors
          const emailError = axiosError.response.data.email;
          if (Array.isArray(emailError) && emailError.length > 0 && typeof emailError[0] === 'string') {
            errorMessage = emailError[0];
          } else if (typeof emailError === 'string') {
            errorMessage = emailError;
          }
        } else if (axiosError.response?.data?.detail && typeof axiosError.response.data.detail === 'string') {
          // Common Django REST format
          errorMessage = axiosError.response.data.detail;
        } else if (axiosError.response?.data?.message && typeof axiosError.response.data.message === 'string') {
          // Another common format
          errorMessage = axiosError.response.data.message;
        } else if (typeof axiosError.response?.data === 'string') {
          // Plain string error
          errorMessage = axiosError.response.data;
        } else if (axiosError.response?.data && typeof axiosError.response.data === 'object') {
          // Try to find any string property that might contain an error message
          for (const key in axiosError.response.data) {
            const value = axiosError.response.data[key];
            if (typeof value === 'string') {
              errorMessage = value;
              break;
            } else if (Array.isArray(value) && value.length > 0 && typeof value[0] === 'string') {
              errorMessage = value[0];
              break;
            }
          }
        }
      } else if (error instanceof Error) {
        // Standard JS error
        errorMessage = error.message;
      }

      // Show error message - use the error message as the main title for better visibility
      toast.error(errorMessage, {
        description: "Please correct the error and try again.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDownloadSample = () => {
    // Open the sample file download URL in a new tab
    window.open(API_URLS.SAMPLE_EMPLOYEE_DOWNLOAD, "_blank");
  };

  // Reset the form state when the modal is closed
  useEffect(() => {
    if (!open) {
      setShowForm(false);
    }
  }, [open]);

  // Debug effect to log state changes
  useEffect(() => {
    console.log("Required fields state changed:", requiredFields);
  }, [requiredFields]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[798px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{showForm ? "Add Employees Manually" : "Add Employees"}</DialogTitle>
          <DialogDescription>
            {showForm
              ? "Enter all the required fields and add an employee to the employee list."
              : "Add employees using the sample file by providing all the below details and upload the file. (or) Add employees manually."
            }
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          {/* Required fields list - only shown when form is not visible */}
          {!showForm && (
            <>
              <h3 className="font-medium mb-2">Required fields :</h3>

              {/* List of required fields */}
              <div className="space-y-1 mb-4 text-sm">
                {/* Debug info */}
                {isLoadingRequiredFields ? (
                  <p className="text-gray-500">Loading required fields...</p>
                ) : requiredFields.length > 0 ? (
                  requiredFields.map((field) => (
                    <p key={field} className="text-gray-700">{field}</p>
                  ))
                ) : (
                  <p className="text-gray-500">No required fields found</p>
                )}
              </div>

              <div className="flex items-center gap-2 pt-2 mb-4">
                <span className="text-sm">ℹ</span>
                <Button
                  type="button"
                  variant="link"
                  className="h-auto p-0 text-sm"
                  onClick={handleDownloadSample}
                >
                  Download sample excel(.xlsx) file
                </Button>
              </div>
            </>
          )}

          {/* Form for manual entry - only shown after clicking "Manually Add" */}
          {showForm && (
            <>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                  {/* Static fields */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={form.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem className="w-full">
                          <CustomFormLabel>Email *</CustomFormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="Enter email address"
                              type="email"
                              className="w-full"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="emp_id"
                      render={({ field }) => (
                        <FormItem className="w-full">
                          <CustomFormLabel>Emp ID</CustomFormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="Enter employee ID"
                              className="w-full"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={form.control}
                      name="first_name"
                      render={({ field }) => (
                        <FormItem className="w-full">
                          <CustomFormLabel>First Name *</CustomFormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="Enter first name"
                              className="w-full"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="last_name"
                      render={({ field }) => (
                        <FormItem className="w-full">
                          <CustomFormLabel>Last Name *</CustomFormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="Enter last name"
                              className="w-full"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Dynamic fields from metadata */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {metadataFields.map((metaField) => (
                      <FormField
                        key={metaField.field}
                        control={form.control}
                        name={metaField.field as keyof z.infer<typeof dynamicSchema>}
                        render={({ field: formField }) => (
                          <FormItem className="w-full">
                            <CustomFormLabel>{metaField.displayName || metaField.field}</CustomFormLabel>
                            <FormControl>
                              {metaField.values && metaField.values.length > 0 ? (
                                <Select
                                  onValueChange={formField.onChange}
                                  defaultValue={formField.value}
                                >
                                  <SelectTrigger className="w-full">
                                    <SelectValue
                                      placeholder={`Select ${metaField.displayName || metaField.field}`}
                                    />
                                  </SelectTrigger>
                                  <SelectContent>
                                    {metaField.values.map((option) => (
                                      <SelectItem key={option.value} value={option.value}>
                                        {option.label}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                              ) : (
                                <Input
                                  {...formField}
                                  placeholder={`Enter ${metaField.displayName || metaField.field}`}
                                  className="w-full"
                                />
                              )}
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    ))}
                  </div>

                  <DialogFooter className="flex justify-between">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setShowForm(false)}
                    >
                      Back
                    </Button>
                    <Button type="submit" disabled={isSubmitting}>
                      {isSubmitting ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Adding...
                        </>
                      ) : (
                        "Add Employee"
                      )}
                    </Button>
                  </DialogFooter>
                </form>
              </Form>
            </>
          )}

          {/* Footer buttons - only shown when form is not visible */}
          {!showForm && (
            <DialogFooter className="flex justify-between sm:justify-between gap-2">
              <Button
                type="button"
                onClick={() => setShowForm(true)}
              >
                Manually Add
              </Button>
              <Button type="button" variant="outline" className="flex items-center gap-2">
                <Upload className="h-4 w-4" />
                Upload a File
              </Button>
            </DialogFooter>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};
