import React from "react";
import { Div } from "unmatched/components";
import util from "unmatched/utils";
// import { useHistory } from "unmatched/hooks";
import useSession from "unmatched/modules/session/hook";
import { logoutUserFact } from "unmatched/modules/session/api";
import Loader from "./Auth/Loader";
// import PropTypes from 'prop-types'

const Logout = () => {
  const session = useSession();
  // const history = useHistory();

  const navigateToLogin = () => {
    window.location.href = window.location.origin;
    // history.push(util.appUrls.auth.login);
  };

  const onLogout = async () => {
    try {
      await logoutUserFact();
      session.resetSession();
      window.Beacon("logout");
      window.Beacon("config", {
        docsEnabled: false,
      })
      window.Beacon('navigate', '/ask/chat/')
      navigateToLogin();
    } catch (err) {
      session.resetSession();
      window.Beacon("logout");
      window.Beacon("config", {
        docsEnabled: false,
      })
      window.Beacon('navigate', '/ask/chat/')
      navigateToLogin();
    }
  };

  React.useEffect(() => {
    if (util.session.getToken()) {
      onLogout();
    } else {
      window.Beacon("logout");
      window.Beacon("config", {
        docsEnabled: false,
      })
      window.Beacon('navigate', '/ask/chat/')
      navigateToLogin();
    }
  }, []);

  return (
    <Div>
      <Loader title="Logging out..." content="Please wait" />
    </Div>
  );
};

Logout.propTypes = {};

export default Logout;
