import React from "react";
import { Div, Text } from "unmatched/components";
import styled from "styled-components";
import icons from "assets/icons/icons";

const { Manage, Send, Wait, Users, Active, PercentUp } = icons;

const Wrap = styled(Div)`
  max-width: 75%;
  height: 142px;
  background: #ffffff;
  border: 1px solid #f2f2f2;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.05);
  border-radius: 10px;
`;

const HeadWrap = styled(Div)`
  height: 40px;
  background: #f2f2f2;
  border: 1px solid #f2f2f2;
  border-radius: 10px 10px 0px 0px;
  padding: 0 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const Link = styled(Div)`
  font-family: "Inter";
  font-style: normal;
  font-weight: 600;
  font-size: 12px;
  line-height: 15px;
  color: #518cff;
  display: flex;
  align-items: center;
  cursor: pointer;
`;

const PercentWrap = styled.span`
  font-family: "Inter";
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 15px;
  color: #36b37e;
`;

export default function CountsView() {
  return (
    <Wrap>
      <HeadWrap>
        <Div>
          <Text.P1>Ongoing - Upward Review Jan 2022</Text.P1>
        </Div>
        <Div className="d-flex">
          <Link>
            <Send className="mr-1" />
            Send Reminder Email
          </Link>
          <Link className="pl-4">
            <Manage className="mr-1" />
            Manage Survey
          </Link>
        </Div>
      </HeadWrap>
      <Div className="d-flex justify-content-between p-4">
        <Count Icon={Users} title="Total Reviewers" count={200} />
        <Count Icon={Active} title="Activated" count={200} percent={25} />
        <Count Icon={Wait} title="Not Activated" count={200} />
        <Count Icon={Active} title="Participated" count={80} percent={12} />
      </Div>
    </Wrap>
  );
}

const Count = (props: any) => {
  const { Icon = Users, title, count, percent } = props;
  return (
    <Div className="d-flex">
      <Div>
        <Icon />
      </Div>
      <Div className="ml-3">
        <Text.P1>{title}</Text.P1>
        <Text.H2>
          {count}{" "}
          {percent && (
            <PercentWrap>
              (+{percent}
              <PercentUp />)
            </PercentWrap>
          )}{" "}
        </Text.H2>
      </Div>
    </Div>
  );
};
