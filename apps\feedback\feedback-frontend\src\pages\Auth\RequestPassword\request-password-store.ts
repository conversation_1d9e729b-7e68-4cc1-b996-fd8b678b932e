import { cloneDeep } from "lodash";
import { ErrorType } from "unmatched/types";
import { setState, generateReducer } from "unmatched/utils/store";

interface StateType {
  emailData: {
    email: string;
    isLoading: boolean;
    error: ErrorType;
  };
  resendData: {
    isLoading: boolean;
    error: ErrorType;
  };
}

export const TYPES = {
  EMAIL_FETCH: "app/auth/requestPassword/email/fetch",
  EMAIL_FETCH_SUCCESS: "app/auth/requestPassword/email/fetch-success",
  EMAIL_FETCH_FAILURE: "app/auth/requestPassword/email/fetch-failure",
  RESEND_FETCH: "app/auth/requestPassword/resend-email/fetch",
  RESEND_FETCH_SUCCESS: "app/auth/requestPassword/resend-email/fetch-success",
  RESEND_FETCH_FAILURE: "app/auth/requestPassword/resend-email/fetch-failure",
  RESET: "app/auth/requestPassword/reset",
};

const initialState: StateType = {
  emailData: {
    isLoading: false,
    error: {
      statusCode: 0,
      errorCode: "",
      msg: "",
    },
    email: "",
  },
  resendData: {
    isLoading: false,
    error: {
      statusCode: 0,
      errorCode: "",
      msg: "",
    },
  },
};

export const sendResetLinkAction = () => {
  return () => {
    // dispatch({ type: TYPES.SESSION_FETCH });
    // getSessionFact('', '').then((response: AxiosResponse) => {
    //   dispatch({ type: TYPES.SESSION_FETCH_SUCCESS, payload: response.data.payload });
    // }, (error: Error) => {
    //     dispatch({ type: TYPES.SESSION_FETCH_FAILURE, payload: { msg: 'asdasd' } });
    // });
  };
};

export const resendResetLinkAction = () => {
  return () => {
    // dispatch({ type: TYPES.SESSION_FETCH });
    // getSessionFact('', '').then((response: AxiosResponse) => {
    //   dispatch({ type: TYPES.SESSION_FETCH_SUCCESS, payload: response.data.payload });
    // }, (error: Error) => {
    //     dispatch({ type: TYPES.SESSION_FETCH_FAILURE, payload: { msg: 'asdasd' } });
    // });
  };
};

const ACTIONS = {
  [TYPES.EMAIL_FETCH]: (state: StateType) =>
    setState(state, {
      emailData: {
        ...state.emailData,
        isLoading: true,
      },
    }),
  [TYPES.EMAIL_FETCH_SUCCESS]: (state: StateType, payload: any) =>
    setState(state, {
      emailData: {
        ...payload,
        isLoading: false,
      },
    }),
  [TYPES.EMAIL_FETCH_FAILURE]: (state: StateType, payload: any) =>
    setState(state, {
      emailData: {
        ...state.emailData,
        error: payload,
        isLoading: false,
      },
    }),
  [TYPES.RESEND_FETCH]: (state: StateType) =>
    setState(state, {
      resendData: {
        ...state.resendData,
        isLoading: true,
      },
    }),
  [TYPES.RESEND_FETCH_SUCCESS]: (state: StateType, payload: any) =>
    setState(state, {
      resendData: {
        ...payload,
        isLoading: false,
      },
    }),
  [TYPES.RESEND_FETCH_FAILURE]: (state: StateType, payload: any) =>
    setState(state, {
      resendData: {
        ...state.resendData,
        error: payload,
        isLoading: false,
      },
    }),
  [TYPES.RESET]: (state: StateType) => setState(state, cloneDeep(initialState)),
};

export default generateReducer(ACTIONS, initialState);
