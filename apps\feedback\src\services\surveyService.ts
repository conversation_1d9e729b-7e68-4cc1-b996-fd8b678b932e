import axiosInstance from '../lib/axios';
import { Survey } from '../types/survey';
import { SurveyStats, SurveyStatsMap } from '../types/stats';

export const surveyService = {
  getSurveys: async (page: number = 1, pageSize: number = 10): Promise<any> => {
    try {
      // Using the same endpoint as the old codebase
      const response = await axiosInstance.get(`/survey/survey-index`, {
        params: {
          page,
          page_size: pageSize,
        },
      });
      
      // Handle different response structures
      if (Array.isArray(response.data)) {
        return response.data; // If the API returns an array directly
      } else if (response.data?.data) {
        return response.data.data; // If the API returns { data: [...] }
      } else if (response.data?.surveys) {
        return response.data.surveys; // If the API returns { surveys: [...] }
      }
      
      return [];
    } catch (error) {
      console.error('Error fetching surveys:', error);
      return [];
    }
  },

  getSurveyById: async (id: string): Promise<Survey> => {
    const response = await axiosInstance.get(`/surveys/${id}`);
    return response.data;
  },

  getSurveyStats: async (surveyId: string): Promise<SurveyStats> => {
    try {
      const response = await axiosInstance.get(`/stats/statistics-core/stats/index/${surveyId}/general/`);
      // Map the response to match our expected format
      return {
        questions_left: response.data?.questions_left || 0,
        questions_completed: response.data?.questions_completed || 0,
        percentage_completed: response.data?.percentage_completed || 0,
        total_questions: response.data?.total_questions || 0
      };
    } catch (error) {
      console.error(`Error fetching stats for survey ${surveyId}:`, error);
      return {
        questions_left: 0,
        questions_completed: 0,
        percentage_completed: 0,
        total_questions: 0
      };
    }
  },

  getSurveysWithStats: async (surveys: Survey[]): Promise<SurveyStatsMap> => {
    const statsPromises = surveys.map(async (survey) => {
      try {
        const stats = await surveyService.getSurveyStats(survey.id);
        return { [survey.id]: stats };
      } catch (error) {
        console.error(`Failed to fetch stats for survey ${survey.id}:`, error);
        return { [survey.id]: {
          TODO: 0,
          PROG: 0,
          SUBM: 0,
          DECL: 0,
          percentage_completed: 0
        } as SurveyStats };
      }
    });

    const statsArray = await Promise.all(statsPromises);
    return statsArray.reduce((acc, stat) => ({
      ...acc,
      ...stat
    }), {});
  }
};
