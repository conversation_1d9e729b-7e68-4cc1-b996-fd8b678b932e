import { Outlet } from "react-router";
import useSession from "@/unmatched/modules/session/hook";
import { NavLink } from "react-router";
import ROUTES from "@/routes";

function PublicLayout() {
  const { client } = useSession();
  return (
    <div>
      <header className="bg-card p-3 border-b">
        <nav className="flex flex-row max-w-7xl mx-auto items-center">
          <div className="flex-grow flex justify-start">
            <img
              src={client?.lightLogo}
              alt="CLIENT_LOGO"
              className="h-[20px]"
            />
          </div>
          <div className="flex justify-center">
            <div className="h-full">
              <NavLink className="fs-14 h-full mx-3 pt-3" to={ROUTES.ROOT}>
                Home
              </NavLink>
            </div>
          </div>
        </nav>
      </header>
      <div className="max-w-7xl mx-auto">
        <Outlet />
      </div>
    </div>
  );
}

export default PublicLayout;
