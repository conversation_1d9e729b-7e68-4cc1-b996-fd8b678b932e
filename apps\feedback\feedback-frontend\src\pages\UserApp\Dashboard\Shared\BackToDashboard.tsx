import React from "react";
import { <PERSON> } from "react-router-dom";
import appUrls from "unmatched/utils/urls/app-urls";
// import { Icon } from "unmatched/components";
import { Div } from "unmatched/components";

export default function BackToDashboard() {
  return (
    <Div className="pl-3 py-2">
      <Link to={appUrls.user.default}>
        {" "}
        Back
        {/* <Icon icon="far fa-angle-left" /> */}
      </Link>
    </Div>
  );
}
