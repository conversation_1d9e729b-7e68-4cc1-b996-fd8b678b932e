import React from "react";
import { Input } from "@repo/ui/components/input";
import { Search } from "lucide-react";

interface FileHeaderProps {
  searchValue: string;
  onSearchChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onAddFile?: () => void;
}

export const FileHeader: React.FC<FileHeaderProps> = ({
  searchValue,
  onSearchChange,
}) => {
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h1 className="text-xl font-bold">All Files</h1>
        <div className="flex items-center gap-2">
          <div className="relative w-64">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search for file title or tags"
              value={searchValue}
              onChange={onSearchChange}
              className="pl-8"
            />
          </div>
        </div>
      </div>
    </div>
  );
};
