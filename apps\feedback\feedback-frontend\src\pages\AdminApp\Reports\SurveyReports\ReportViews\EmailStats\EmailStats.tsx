import React from "react";
// import styled from "styled-components";
// import PropTypes from 'prop-types'
import {
  ActivityTracker,
  Button,
  Layout,
  PageContainer,
  Text,
  // TrendingCard,
} from "unmatched/components";
import Header from "../../Header";
import Status from "./Status";
import appUrls from "unmatched/utils/urls/app-urls";
import iconsSvgs from "assets/icons/icons";

const { Page } = iconsSvgs;

// const StatsRow = styled(Layout.Row)`
//   padding-top: 20px;
//   padding-bottom: 20px;
//   background: #ffffff;
//   border: 1px solid #f2f2f2;
//   box-sizing: border-box;
//   border-radius: 14px;
// `;

const EmailStats = (props: any) => {
  const { survey, layout } = props;

  const [modal, setModal] = React.useState({
    show: false,
    data: {},
  });
  // const [stats] = React.useState([
  //   { id: 1, title: "Total Participants", count: "178", failed: false },
  //   { id: 2, title: "Mails Sent", count: "124", failed: false },
  //   { id: 3, title: "Yet to Mail", count: "178", failed: false },
  //   { id: 4, title: "Yet to Mail", count: "124", failed: true },
  // ]);

  const [activities] = React.useState([
    { id: 1, count: 15, time: 0 },
    { id: 2, count: 15, time: 0 },
    { id: 3, count: 15, time: 0 },
    { id: 4, count: 15, time: 0 },
    { id: 5, count: 15, time: 0 },
  ]);

  return (
    <PageContainer>
      <Header
        survey={survey}
        layout={layout}
        breadcrumbs={[
          {
            label: "Reports",
            icon: <Page className="grey-icon__svg" />,
            route: appUrls.admin.reports.default,
          },
          { label: "Email Statistics" },
          { label: survey.name },
        ]}
      />
      <Layout.Container fluid>
        {/* <Text.H3 className="pt-5 pb-2">Email Statistics</Text.H3> */}
        {/* <StatsRow>
          {stats.map((item: any, index: number) => {
            const getCountTemplate = () => {
              if (item.failed) {
                return (
                  <>
                    {item.count}{" "}
                    <Button
                      className="fs-16 py-0 px-1 text-underline"
                      variant="link"
                    >
                      Retry
                    </Button>
                  </>
                );
              }
              return item.count;
            };
            return (
              <Layout.Col
                className={index < stats.length ? "border-right" : ""}
                key={item.id}
              >
                <TrendingCard
                  veryLow={item.failed}
                  high={item.count > 150}
                  count={getCountTemplate()}
                  title={item.title}
                />
              </Layout.Col>
            );
          })}
        </StatsRow> */}
        <Text.H3 className="py-5">Activity</Text.H3>
        <Layout.Row>
          <Layout.Col>
            {activities.map((item: any, index: number) => {
              return (
                <ActivityTracker
                  variant="primary"
                  key={item.id}
                  hasBorder={index < activities.length - 1}
                >
                  <Text.P1>
                    Emailed Report to{" "}
                    <Button
                      variant="link"
                      className="p-0 text-underline"
                      onClick={() => {
                        setModal({
                          show: true,
                          data: item,
                        });
                      }}
                    >
                      {item.count} participants
                    </Button>
                  </Text.P1>
                  <Text.P1 className="text-muted">
                    {new Date().toDateString()}
                  </Text.P1>
                </ActivityTracker>
              );
            })}
          </Layout.Col>
        </Layout.Row>
      </Layout.Container>
      <Status
        show={modal.show}
        onHide={() => {
          setModal({
            show: false,
            data: {},
          });
        }}
        activity={modal.data}
      />
    </PageContainer>
  );
};

// EmailStats.propTypes = {

// }

export default EmailStats;
