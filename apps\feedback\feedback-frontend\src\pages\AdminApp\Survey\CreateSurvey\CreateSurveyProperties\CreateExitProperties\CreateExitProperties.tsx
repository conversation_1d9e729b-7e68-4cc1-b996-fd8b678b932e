// Node modules
import React from "react";
import _ from "lodash";
// import { isFieldInvalid, yup } from "unmatched/utils/formik";
import { useFormik } from "unmatched/hooks";
import Header from "../../Header";
import {
  Text,
  Layout,
  Form,
  FormikInput,
  FormikAutoSave,
  Div,
  Icon,
} from "unmatched/components";
import { surveyProperties } from "unmatched/survey/creation/components";
import { patchSurveyFact } from "../../../survey-api";
import util from "unmatched/utils";
import { DateTime } from "luxon";
const { yup } = util.formik;

const getValue = (obj: any, key: string) => _.get(obj, key);

const Pannel = (props: any) => {
  const { children, heading } = props;
  return (
    <Div className="py-3">
      {heading && <Text.H3 className="pb-3">{heading}</Text.H3>}
      {children}
    </Div>
  );
};

const CreateExitProperties = (props: any) => {
  // activeVersionId
  const { survey, surveyId, updateValidators, updateBuilderStatus } = props;
  // const history = useHistory();
  // const toastr = useToastr();
  const initialValues = _.pick(survey.data, ["name", "description"]);

  const validationSchema: any = {
    name: yup.string().required("Name field is required"),
    description: yup
      .string()
      .required("Survey Description is a required field"),
  };

  const formikOptions = {
    initialValues,
    validationSchema: yup.object().shape(validationSchema),
    onSubmit: (_values: any) => {
      onFormPatch(_values);
    },
  };

  // const isUpwardReview = survey.data.type === S

  const formik = useFormik(formikOptions);

  const { values, handleSubmit, handleReset } = formik;

  React.useEffect(() => {
    if (!props.viewOnly) {
      const key = `survey-${survey.data.id}`;
      updateValidators({
        key,
        validate: () => {
          return util.formik.formikSubmitForm(formik);
        },
      });
      updateBuilderStatus(util.enums.SurveyStatus.Properties);
      return () => {
        updateValidators({ key, validate: null });
      };
    }
  }, []);

  const onFormPatch = (_values: any) => {
    const payload = {
      ...survey.data,
      ..._values,
    };
    survey.setSaving(true);
    return patchSurveyFact(surveyId, payload).then(
      (updatedData: any) => {
        survey.setSaving(false);
        survey.setData({
          ...payload,
          updatedTime: util.date.getFormatedTime(
            new Date(),
            " MMMM dd, yyyy, HH:mm"
          ),
          updatedAt: util.date.getFormatedTime(
            new Date(updatedData.updatedAt),
            " MMMM dd, yyyy, HH:mm"
          ),
        });
        return true;
      },
      () => {
        survey.setSaving(false);
      }
    );
  };

  return (
    <Form
      onReset={handleReset}
      onSubmit={handleSubmit}
      className="mb-3 mr-xl-5 pr-xl-5"
    >
      <FormikAutoSave
        values={values}
        initialValues={initialValues}
        onSave={onFormPatch}
      >
        <Header
          title={<Text.H1 className="pb-2">{getValue(values, "name")}</Text.H1>}
          metaItem={
            <Text.P1>
              {survey.isSaving ? (
                <>
                  <Icon spin icon="fal fa-spinner-third" /> Saving
                </>
              ) : (
                DateTime.fromISO(new Date(survey.data.updatedAt).toISOString()).toFormat(
                  " LLL dd, yyyy, hh:mm a"
                )
              )}
            </Text.P1>
          }
          breadcrumbs={props.breadcrumbs}
        />
        <Layout.Container fluid>
          <Layout.Row>
            <Layout.Col xl={10}>
              <Pannel>
                <Layout.Row>
                  <Layout.Col xl={6}>
                    <FormikInput.Text
                      label="Survey Name"
                      keyName="name"
                      placeholder="Survey Name"
                      formik={formik}
                    />
                  </Layout.Col>
                </Layout.Row>
                <Layout.Row>
                  <Layout.Col xl={12}>
                    {surveyProperties.getDescriptionTemplate(formik)}
                  </Layout.Col>
                </Layout.Row>
              </Pannel>
            </Layout.Col>
          </Layout.Row>
        </Layout.Container>
      </FormikAutoSave>
    </Form>
  );
};

export default CreateExitProperties;
