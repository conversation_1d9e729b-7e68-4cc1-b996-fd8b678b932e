import ROUTES from "@/routes";
import { Text } from "@repo/ui/components/Text";
import { ChevronLeft } from "lucide-react";
import { Link } from "react-router";


function NotFOund() {
  return (
    <div className="min-h-screen flex items-center justify-center flex-col gap-5">
      <Text.H2>404 Not Found</Text.H2>
      <Link
        className="bg-primary text-primary-foreground text-xs px-3 py-2 rounded flex items-center gap-1"
        to={ROUTES.ROOT}
      >
       <ChevronLeft className="inline" /> Go Back
      </Link>
    </div>
  );
}

export default NotFOund;
