import React from "react";
import {
  Modal,
  Button,
  Icon,
  FormGroup,
  FormControl,
  Text,
  Layout,
  Card,
  Div,
} from "unmatched/components";
import ModalHeader from "../../../../ModalHeader";

const icons = {
  DOWNLOAD: "fal fa-file-download",
  EMAIL: "fal fa-paper-plane",
};

export default function DownloadReports(props: any) {
  const { show, onHide, selected, user, users, total, downloadBulkZip } = props;
  const [options, setOptions] = React.useState<number | null>(null);

  const onDownload = async () => {
    if (options === 0) {
      // props.onDownload(options);
      try {
        users.length > 0;
      } catch (error) {}
    } else if (options === 1) {
      try {
        // props.onDownload(options);
        await downloadBulkZip({}, { f: "zip" });
      } catch (error) {}
    } else {
    }
  };

  const getMessageTemplate = () => {
    if (user.id || user.empId) {
      return (
        <>
          <Text.P2>Download report of participant</Text.P2>
          <Card className="my-3">
            <Div className="p-3">
              <Layout.Flex>
                <Layout.FlexItem className="pr-5 pl-1">
                  <Text.H3>
                    {user.firstName} {user.lastName}
                  </Text.H3>
                </Layout.FlexItem>
                <Layout.FlexItem className="pr-5 pl-1">
                  <Text.H3>{user.email}</Text.H3>
                </Layout.FlexItem>
              </Layout.Flex>
            </Div>
          </Card>
        </>
      );
    }
    return (
      <FormGroup className="p-3">
        {selected.length > 0 && (
          <FormControl.Radio>
            <FormControl.Radio.Label>
              <Text.H3>
                Download individual reports of selected participants
              </Text.H3>
              <Text.P2>
                Downloads individual reports of {selected.length} participants.
              </Text.P2>
            </FormControl.Radio.Label>
            <FormControl.Radio.Input
              checked={options === 0}
              name="download_type"
              onChange={() => setOptions(0)}
              disabled
            ></FormControl.Radio.Input>
          </FormControl.Radio>
        )}
        <FormControl.Radio className="mt-3">
          <FormControl.Radio.Label>
            <Text.H3>Download all individual reports</Text.H3>
            <Text.P2>Downloads all {total} individual reports.</Text.P2>
          </FormControl.Radio.Label>
          <FormControl.Radio.Input
            checked={options === 1}
            name="download_type"
            onChange={() => setOptions(1)}
          ></FormControl.Radio.Input>
        </FormControl.Radio>
        <FormControl.Radio className="mt-3">
          <FormControl.Radio.Label className="w-75">
            <Layout.Row>
              <Layout.Col className="pr-0" lg={6}>
                <Text.H3>Download all individual reports of</Text.H3>
                <Text.P2>Choose a criteria.</Text.P2>
              </Layout.Col>
              <Layout.Col sm={6} lg={3} className="pr-0">
                <FormControl.Select variant="white" value={"Department"}>
                  <FormControl.SelectItem>Department</FormControl.SelectItem>
                  {/* <FormControl.SelectItem>Option 2</FormControl.SelectItem>
                  <FormControl.SelectItem>Option 2</FormControl.SelectItem> */}
                </FormControl.Select>
              </Layout.Col>
              <Layout.Col sm={6} lg={3}>
                <FormControl.Select variant="white" value={"All"}>
                  <FormControl.SelectItem>All</FormControl.SelectItem>
                  {/* <FormControl.SelectItem>Option 2</FormControl.SelectItem>
                  <FormControl.SelectItem>Option 2</FormControl.SelectItem> */}
                </FormControl.Select>
              </Layout.Col>
            </Layout.Row>
          </FormControl.Radio.Label>
          <FormControl.Radio.Input
            checked={options === 2}
            name="download_type"
            onChange={() => setOptions(2)}
            disabled
          ></FormControl.Radio.Input>
        </FormControl.Radio>
      </FormGroup>
    );
  };

  return (
    <Modal show={show} backdrop="static" centered size="lg">
      <ModalHeader
        title="Download Reports"
        onHide={() => {
          onHide();
          setOptions(null);
        }}
      />
      <Modal.Body>{getMessageTemplate()}</Modal.Body>
      <Modal.Footer className="text-right my-4 border-none">
        <Button
          variant="primary"
          className="mr-2"
          onClick={() => {
            if (user.id || user.empId) {
              window.open(user.file);
              props.onHide();
            } else {
              onDownload();
              props.onHide();
            }
          }}
        >
          <Icon icon={icons.DOWNLOAD} className="mr-1" />
          Download Report
        </Button>
      </Modal.Footer>
    </Modal>
  );
}
