import React from "react";
// import _ from "lodash";
import {
  PageContainer,
  Icon,
  Text,
  Layout,
  Button,
  Div,
  Card,
} from "unmatched/components";
import Header from "../../Header";
import useFilter from "pages/CommonFilters/hook";
import {
  getFirmReportsByIdFact,
  requestNewFirmReport,
} from "pages/AdminApp/Reports/reports-api";
import useToastr from "unmatched/modules/toastr/hook";
import util from "unmatched/utils";
import appUrls from "unmatched/utils/urls/app-urls";
import iconsSvgs from "assets/icons/icons";

const { Page } = iconsSvgs;

const icons = {
  DOWNLOAD: "fal fa-file-download",
  EMAIL: "fal fa-paper-plane",
  CHECKED: "fal fa-check-circle",
  LOADER: "far fa-circle-notch",
};

export default function EngagementReports({ survey, layout }: any) {
  const [reports, setReports] = React.useState<Array<any>>([]);

  const toastr = useToastr();
  const [isLoading, setLoading] = React.useState(false);

  const filtersState = useFilter();

  React.useEffect(() => {
    filtersState.getFilters();
  }, []);

  React.useEffect(() => {
    getReports();
  }, []);

  const getReports = async () => {
    try {
      setLoading(true);
      const response = await getFirmReportsByIdFact({ index_id: survey.id });
      setReports(response.data);
      setLoading(false);
    } catch (err) {
      setLoading(false);
    }
  };

  const generateNewReport = async () => {
    setLoading(true);
    try {
      await requestNewFirmReport({ index: survey.id });
      toastr.onSucces({
        title: "Success",
        content: "Reports will be available soon.",
      });
      setLoading(false);
    } catch (err: any) {
      setLoading(false);
    }
    // setTimeout(() => {
    //   const report = {};
    //   setReports([report]);
    //   s
    // }, 3000);
  };

  const getFilterTemplate = () => {
    if (isLoading || reports.length) return null;
    return (
      <>
        <Text.P1 className="py-2 mb-5">
          Click to generate report for the survey.
        </Text.P1>
        <Div>
          <Button
            onClick={() => {
              generateNewReport();
            }}
          >
            Generate
          </Button>
        </Div>
      </>
    );
  };

  const getLoadingTemplate = () => {
    if (!isLoading) return null;
    return (
      <Text.P1 className="py-2">
        Please wait until your report is generated.
        <Div className="py-3">
          <Icon spin icon={icons.LOADER} variant="primary" className="fs-30" />
        </Div>
      </Text.P1>
    );
  };

  const getListTemplate = () => {
    if (!reports.length) return null;
    return (
      <>
        <Text.P1 className="py-3">
          The following aggregate report has been generated.
        </Text.P1>
        {reports.map((item: any) => {
          return (
            <Layout.Row key={item.id}>
              <Layout.Col xl={5}>
                <Card key={item.id}>
                  <Card.Body>
                    <Div>
                      <Div className="row">
                        <Div className="col" style={{ maxWidth: 50 }}>
                          <span
                            style={{ top: 3 }}
                            className="position-relative mr-3"
                          >
                            <Icon icon="fas fa-check-circle text-success fs-24 " />
                          </span>
                        </Div>
                        <Div className="col">
                          <Text.H3>
                            Report Generated
                            <br />
                            <span className="fs-12 mt-3">Generated on: </span>
                            <span className="fs-12 fw-300">
                              {util.date.getBrowserTime(
                                item.updatedAt,
                                "MMMM dd, yyyy, HH:mm"
                              )}
                            </span>
                          </Text.H3>
                        </Div>
                      </Div>
                    </Div>
                  </Card.Body>
                  <Card.Footer className="pr-3">
                    <Button
                      variant="outline-primary"
                      className="mr-2"
                      onClick={() => window.open(item.file)}
                    >
                      <Icon icon={icons.DOWNLOAD} className="mr-1" />
                      Download
                    </Button>
                    <Button variant="primary">
                      <Icon icon={icons.EMAIL} className="mr-1" />
                      Email Reports
                    </Button>
                  </Card.Footer>
                </Card>
              </Layout.Col>
            </Layout.Row>
          );
        })}
        <Button
          className="px-0 mt-3"
          variant="link"
          onClick={() => {
            generateNewReport();
          }}
        >
          Regenerate report
        </Button>
      </>
    );
  };

  return (
    <PageContainer>
      <Header
        survey={survey}
        layout={layout}
        breadcrumbs={[
          {
            label: "Reports",
            icon: <Page className="grey-icon__svg" />,
            route: appUrls.admin.reports.default,
          },
          { label: "Engagement Reports" },
          { label: survey.name },
        ]}
      />
      <Layout.Container fluid>
        <Text.H3 className="pt-5">Generate Reports</Text.H3>
        {getFilterTemplate()}
        {getLoadingTemplate()}
        {getListTemplate()}
      </Layout.Container>
    </PageContainer>
  );
}
