import React from "react";
import { PageContainer } from "unmatched/components";

import { Route, Redirect } from "react-router-dom";
import AppRoutes from "../../AppRoutes";
import DATALOAD_ROUTES from "./dataload-routes";
import appUrls from "unmatched/utils/urls/app-urls";

const DataLoad = () => {
  return (
    <PageContainer>
      <AppRoutes routes={DATALOAD_ROUTES}>
        <Route exact path={appUrls.admin.dataLoad.default}>
          <Redirect to={appUrls.admin.dataLoad.create} />
        </Route>
      </AppRoutes>
    </PageContainer>
  );
};
export default DataLoad;
