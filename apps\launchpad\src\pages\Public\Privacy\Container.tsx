// import Header from "pages/UserApp/Header/Header";
// import CommonContainer from "../CommonContainer/CommonContainer";
// import React from "react";
// import util from "unmatched/utils";
import Privacy from "./Privacy";
// import PropTypes from 'prop-types'

const PrivacyContainer = () => {
  return (
    <div>
      {/* <Header hideOptions /> */}
      {/* <CommonContainer> */}
        <Privacy />
      {/* </CommonContainer> */}
    </div>
  );
};

// Faq.propTypes = {

// }

export default PrivacyContainer;
