import axios, { AxiosError, AxiosResponse } from "axios";
import util from "unmatched/utils";

const getSurveyIndex = (id: any, config: any) => {
  return axios.get(`${util.apiUrls.GET_INDIVIDUAL_SURVEY_INFO(id)}`, config);
};

const getSurveyIndexV2 = (id: any, config: any) => {
  return axios.get(`${util.apiUrls.GET_INDIVIDUAL_SURVEY_INFO_V2(id)}`, config);
};

export const getAdminSurveyInfoFact = (
  id: string,
  params?: any,
  meta?: any
) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return getAdminSurveyIndex(id, config).then(({ data }: any) => {
    const {
      stats,
      is_pairs_editable,
      title,
      description,
      resourcetype,
      deadline,
    } = data;
    return {
      data: {
        title,
        stats,
        description,
        isPairsEditable: is_pairs_editable,
        type: resourcetype,
        deadline,
      },
    };
  });
};

const getAdminSurveyIndex = (id: any, config: any) => {
  return axios.get(
    `${util.apiUrls.GET_INDIVIDUAL_SURVEY_ADMIN_INFO(id)}`,
    config
  );
};

export const getAdminSurveyInfoFactV2 = (
  id: string,
  params?: any,
  meta?: any
) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return getAdminSurveyIndexV2(id, config).then(({ data }: any) => {
    const {
      stats,
      is_pairs_editable,
      title,
      description,
      resourcetype,
      deadline,
    } = data;
    return {
      data: {
        title,
        stats,
        description,
        isPairsEditable: is_pairs_editable,
        type: resourcetype,
        deadline,
      },
    };
  });
};

const getAdminSurveyIndexV2 = (id: any, config: any) => {
  return axios.get(
    `${util.apiUrls.GET_INDIVIDUAL_SURVEY_ADMIN_INFO_V2(id)}`,
    config
  );
};

export const getSurveyInfoFact = (id: string, params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return getSurveyIndex(id, config).then(({ data }: any) => {
    const {
      stats,
      is_pairs_editable,
      title,
      description,
      resourcetype,
      deadline,
      is_declinable,
    } = data;
    return {
      data: {
        title,
        stats,
        description,
        isPairsEditable: is_pairs_editable,
        type: resourcetype,
        deadline,
        isDeclinable: is_declinable,
      },
    };
  });
};

export const getSurveyInfoFactV2 = (id: string, params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return getSurveyIndexV2(id, config).then(({ data }: any) => {
    const {
      is_pairs_editable,
      title,
      description,
      resourcetype,
      deadline,
      is_declinable,
      id
    } = data;
    return {
      data: {
        id,
        title,
        description,
        isPairsEditable: is_pairs_editable,
        type: resourcetype,
        deadline,
        isDeclinable: is_declinable,
      },
    };
  });
};

export const getSurveyVersionIdFact = (id: any, meta: any) => {
  const config = util.api.getConfigurations(
    {},
    {
      ...meta,
    }
  );
  return getSurveyIndex(id, config).then(({ data }: AxiosResponse) => {
    if (data.surveys && data.surveys.length) {
      const [version] = data.surveys;
      return {
        versionId: version.id,
        demographicsId: data.demographic_id,
      };
    }
    return {
      versionId: 0,
      demographicsId: data.demographic_id,
    };
  });
};

export const getSurveyVersionIdFactV2 = (id: any, meta: any) => {
  const config = util.api.getConfigurations(
    {},
    {
      ...meta,
    }
  );
  return getSurveyIndexV2(id, config).then(({ data }: AxiosResponse) => {
    if (data.surveys && data.surveys.length) {
      const [version] = data.surveys;
      return {
        versionId: version.id,
        demographicsId: data.demographic_id,
      };
    }
    return {
      versionId: 0,
      demographicsId: data.demographic_id,
    };
  });
};

// https://v2.unmatched.app/api/v2/survey/survey-response/

const createSurveyResponseFact = (data: any, type: any, meta?: any) => {
  const config = util.api.getConfigurations(
    {},
    {
      ...meta,
    }
  );
  return axios.post(
    "/survey/survey-response/",
    {
      ...data,
      resourcetype: type,
    },
    config
  );
};

const getSurveyData = (id: any, meta?: any) => {
  const config = util.api.getConfigurations(
    {
      survey: id,
    },
    meta
  );
  return axios.get(`/survey/survey/${id}`, config);
  // return axios.get(`${util.apiUrls.ADMIN_SURVEY_SECTIONS}`, config).then(({ data }) => {
  //   return data.results.map((item: any) => {
  //     return {
  //       id: item.id,
  //       title: item.name,
  //       totalQuestions: 10,
  //     }
  //   });
  // });
};

const getQuestions = (_questions: any, responsesData: any) => {
  return _questions.map((item: any) => {
    const responses = responsesData.data.question_responses || [];
    const comments = responsesData.data.comment_responses || [];

    // const responses: any = [];
    const response: any =
      responses.find((res: any) => res.question === item.id) || {};
    const comment: any = comments.find(
      (res: any) => res.question === item.id
    ) || { id: 0, value: "" };

    let output: any = {
      id: item.id,
      responseId: 0,
      categoryID: item.section,
      title: item.label || item.text,
      type: item.resourcetype,
      isMandatory: item.mandatory,
      isBulletin: item.is_bulletin ?? false,
      hasFeedback: item.collect_feedback,
      hasSwap: item.is_reverse_scale,
      radio: {
        selected: "",
        options: item.options || [],
      },
      checkbox: {
        selected: [],
        options: item.options || [],
      },
      rating: {
        selected: "",
        tags: item.options || {},
      },
      commentId: comment.id,
      feedback: comment.value,
    };
    if (response && response.id) {
      const { id, value, is_valid } = response;
      output = {
        ...output,
        responseId: id,
        isValid: is_valid,
        radio: {
          selected: value,
          options: item.options || [],
        },
        checkbox: {
          selected: value || [],
          options: item.options || [],
        },
        rating: {
          selected: value,
          tags: item.options || {},
        },
      };
    }
    return output;
  });
};

const extractResponses = (responsesData: any, data: any) => {
  const sections: any = [];
  let questions: any = [];
  data.sections.forEach((section: any) => {
    sections.push({
      id: section.id,
      title: section.name,
      totalQuestions: section.components.length,
      instructionsCount: section.components.filter(
        (item: any) => item.type === util.enums.QUESTION.Paragraph
      ).length,
    });
    questions = [
      ...questions,
      ...getQuestions(section.components, responsesData),
    ];
  });
  const { first_name, last_name }: any =
    responsesData &&
    responsesData.data.pairing_detail &&
    responsesData.data.pairing_detail.target
      ? responsesData.data.pairing_detail.target
      : {};
  const send = {
    questions,
    sections,
    hide_no_basis_option: data.hide_no_basis_option,
    survey: {
      id: responsesData.data.index.id,
      title: responsesData.data.index.title,
      lastModified: util.date.getBrowserTime(
        new Date(responsesData.data.updated_on).toISOString(),
        "MMM dd, yyyy, hh:mm a"
      ),
      thankyouText: responsesData.data.index.thank_text || "",
      surveyFor: first_name && last_name ? `${first_name} ${last_name}` : "",
      endDate: util.date.getBrowserTime(
        responsesData.data.index.deadline,
        "MMM dd, yyyy, hh:mm a"
      ),
    },
    wavierId: data.waiver_question,
    eligibilityId: data.report_eligibility_settings,
    // rater: {
    //   name: util.getFullName(responsesData.data.rater),
    // }
  };
  return send;
};

export const getResponsesFact = (id: any, meta?: any) => {
  const config = util.api.getConfigurations({}, meta);
  return axios.get(`/survey/survey-response/${id}`, config);
};

export const getWavierResponseFact = (id: any, wavierId: any, meta?: any) => {
  return getResponsesFact(id, meta).then(({ data }: AxiosResponse) => {
    const response =
      data.question_responses.find((item: any) => item.question === wavierId) ||
      {};
    return {
      id: response.id,
      value: response.value,
    };
  });
};

export const getSurveyResponsesFact = (
  responseId: any,
  surveyId: any,
  meta?: any
) => {
  return Promise.all([
    getResponsesFact(responseId, meta),
    getSurveyData(surveyId, meta),
  ]).then(([responsesData, { data }]: Array<AxiosResponse>) => {
    return extractResponses(responsesData, data);
  });
};

export const getEngagementResponsesFactV2 = (
  id: any,
  type: string,
  meta?: any
) => {
  const config = util.api.getConfigurations({}, meta);
  return new Promise((resolve: Function, reject: Function) => {
    getSurveyVersionIdFactV2(id, config).then(
      ({ versionId, demographicsId }: any) => {
        if (!versionId) {
          reject(new Error("No Survey Version Present"));
        } else {
          createSurveyResponseFact(
            {
              survey: versionId,
              // status: "TODO",
            },
            "SurveyResponseEngagement"
          ).then(
            ({ data }: AxiosResponse) => {
              getSurveyResponsesFact(data.id, versionId).then(
                (res: any) => {
                  resolve({
                    ...res,
                    versionId,
                    responseId: data.id,
                    demographicsId,
                  });
                },
                (err: AxiosError) => reject(err)
              );
            },
            (err: AxiosError) => reject(err)
          );
        }
      },
      (err: AxiosError) => reject(err)
    );
  });
};

export const getEngagementResponsesFact = (
  id: any,
  type: string,
  meta?: any
) => {
  const config = util.api.getConfigurations({}, meta);
  return new Promise((resolve: Function, reject: Function) => {
    getSurveyVersionIdFact(id, config).then(
      ({ versionId, demographicsId }: any) => {
        if (!versionId) {
          reject(new Error("No Survey Version Present"));
        } else {
          createSurveyResponseFact(
            {
              survey: versionId,
              // status: "TODO",
            },
            "SurveyResponseEngagement"
          ).then(
            ({ data }: AxiosResponse) => {
              getSurveyResponsesFact(data.id, versionId).then(
                (res: any) => {
                  resolve({
                    ...res,
                    versionId,
                    responseId: data.id,
                    demographicsId,
                  });
                },
                (err: AxiosError) => reject(err)
              );
            },
            (err: AxiosError) => reject(err)
          );
        }
      },
      (err: AxiosError) => reject(err)
    );
  });
};

export const getExitResponsesFactV2 = (id: any, type: string, meta?: any) => {
  const config = util.api.getConfigurations({}, meta);
  return new Promise((resolve: Function, reject: Function) => {
    getSurveyVersionIdFactV2(id, config).then(
      ({ versionId }: any) => {
        if (!versionId) {
          reject(new Error("No Survey Version Present"));
        } else {
          createSurveyResponseFact(
            {
              survey: versionId,
              // status: "TODO",
            },
            "SurveyResponseExit"
          ).then(
            ({ data }: AxiosResponse) => {
              getSurveyResponsesFact(data.id, versionId).then(
                (res: any) => {
                  resolve({
                    ...res,
                    versionId,
                    responseId: data.id,
                  });
                },
                (err: AxiosError) => reject(err)
              );
            },
            (err: AxiosError) => reject(err)
          );
        }
      },
      (err: AxiosError) => reject(err)
    );
  });
};

export const getExitResponsesFact = (id: any, type: string, meta?: any) => {
  const config = util.api.getConfigurations({}, meta);
  return new Promise((resolve: Function, reject: Function) => {
    getSurveyVersionIdFact(id, config).then(
      ({ versionId }: any) => {
        if (!versionId) {
          reject(new Error("No Survey Version Present"));
        } else {
          createSurveyResponseFact(
            {
              survey: versionId,
              // status: "TODO",
            },
            "SurveyResponseExit"
          ).then(
            ({ data }: AxiosResponse) => {
              getSurveyResponsesFact(data.id, versionId).then(
                (res: any) => {
                  resolve({
                    ...res,
                    versionId,
                    responseId: data.id,
                  });
                },
                (err: AxiosError) => reject(err)
              );
            },
            (err: AxiosError) => reject(err)
          );
        }
      },
      (err: AxiosError) => reject(err)
    );
  });
};

export const getSelfResponsesFactV2 = (id: any, type: string, meta?: any) => {
  const config = util.api.getConfigurations({}, meta);
  return new Promise((resolve: Function, reject: Function) => {
    getSurveyVersionIdFactV2(id, config).then(
      ({ versionId, demographicsId }: any) => {
        if (!versionId) {
          reject(new Error("No Survey Version Present"));
        } else {
          createSurveyResponseFact(
            {
              survey: versionId,
              // status: "TODO",
            },
            "SurveyResponseSelf"
          ).then(
            ({ data }: AxiosResponse) => {
              getSurveyResponsesFact(data.id, versionId).then(
                (res: any) => {
                  resolve({
                    ...res,
                    versionId,
                    responseId: data.id,
                    demographicsId,
                  });
                },
                (err: AxiosError) => reject(err)
              );
            },
            (err: AxiosError) => reject(err)
          );
        }
      },
      (err: AxiosError) => reject(err)
    );
  });
};

export const getSelfResponsesFact = (id: any, type: string, meta?: any) => {
  const config = util.api.getConfigurations({}, meta);
  return new Promise((resolve: Function, reject: Function) => {
    getSurveyVersionIdFact(id, config).then(
      ({ versionId, demographicsId }: any) => {
        if (!versionId) {
          reject(new Error("No Survey Version Present"));
        } else {
          createSurveyResponseFact(
            {
              survey: versionId,
              // status: "TODO",
            },
            "SurveyResponseSelf"
          ).then(
            ({ data }: AxiosResponse) => {
              getSurveyResponsesFact(data.id, versionId).then(
                (res: any) => {
                  resolve({
                    ...res,
                    versionId,
                    responseId: data.id,
                    demographicsId,
                  });
                },
                (err: AxiosError) => reject(err)
              );
            },
            (err: AxiosError) => reject(err)
          );
        }
      },
      (err: AxiosError) => reject(err)
    );
  });
};

export const patchQuestionResponseFact = (
  payload: any,
  responseId: any,
  meta?: any
) => {
  // https://v2.unmatched.app/api/v2/survey/question-response/{id}
  const config = util.api.getConfigurations({}, meta);
  const url = !payload.isDemographic
    ? `/survey/question-response/${responseId}`
    : `/survey/demographic-response/${responseId}`;
  return axios.patch(url, payload, config);
};

export const postQuestionResponseFact = (payload: any, meta?: any) => {
  const config = util.api.getConfigurations({}, meta);
  const url = !payload.isDemographic
    ? "/survey/question-response/"
    : "/survey/demographic-response/";
  return axios.post(url, payload, config);
};

export const submitSurveyResponseFact = (id: any, meta?: any) => {
  const config = util.api.getConfigurations({}, meta);
  return axios.post(`/survey/survey-response/${id}/submit/`, null, config);
};

export const removeQuestionResponseFact = (
  id: any,
  isDemographic: boolean,
  meta?: any
) => {
  const config = util.api.getConfigurations({}, meta);
  const url = !isDemographic
    ? `/survey/question-response/${id}`
    : `/survey/demographic-response/${id}`;
  return axios.delete(url, config);
};

export const postCommentResponseFact = (payload: any, meta?: any) => {
  const config = util.api.getConfigurations({}, meta);
  const url = !payload.isDemographic
    ? `/survey/comment-response/`
    : `/survey/demographic-response/`;
  return axios
    .post(
      url,
      {
        survey_response: payload.surveyResponseId,
        question: payload.questionId,
        value: payload.value,
      },
      config
    )
    .then(({ data }: AxiosResponse) => {
      return data.id;
    });
};

export const patchCommentResponseFact = (
  payload: any,
  responseId: any,
  meta?: any
) => {
  // https://v2.unmatched.app/api/v2/survey/question-response/{id}
  const config = util.api.getConfigurations({}, meta);
  const url = payload.isDemographic
    ? `/survey/demographic-response/${responseId}`
    : `/survey/comment-response/${responseId}`;
  return axios.patch(
    url,
    {
      id: payload.id,
      survey_response: payload.surveyResponseId,
      question: payload.questionId,
      value: payload.value,
    },
    config
  );
};

export const removeCommentResponseFact = (
  responseId: any,
  isDemographic?: boolean,
  meta?: any
) => {
  const config = util.api.getConfigurations({}, meta);
  const url = isDemographic
    ? `/survey/demographic-response/${responseId}`
    : `/survey/comment-response/${responseId}`;
  return axios.delete(url, config);
};

export const getWavierQuestionFact = (id: any) => {
  // https://v2.unmatched.app/api/v2/survey/admin/report-eligibility/{id}/
  return axios
    .get(`/survey/report-eligibility/${id}/`)
    .then(({ data }: AxiosResponse) => {
      const wavier = data?.waiver_question_detailed || {};
      return {
        question: wavier.label,
        default: wavier.default_option,
        options: Object.entries(wavier.options).map(([key, value]: any) => {
          return {
            id: `${key}-${value}`,
            label: key,
            value,
          };
        }),
      };
    });
};

export const logSurveyVisitFact = (payload: any, meta?: any) => {
  const config = util.api.getConfigurations({}, meta);
  
  return axios.post(util.apiUrls.SURVEY_VISIT_LOG, payload, config);
};
