import { useState, useEffect } from "react";
import { useEmployees, type Employee } from "./useEmployees";
import { useEffect as useDocumentEffect } from "react";
import { useDebounce } from "@/hooks";
import { getSortIcon } from "./utils/sortingUtils";
import { AddEmployeePayload, downloadAllEmployees } from "./employeesService";
import {
  EmployeeHeader,
  EmployeeTable,
  EmployeePagination,
  EmployeeModal
} from "./components";

export const Employees = () => {
  useDocumentEffect(() => {
    document.title = "Employees - Unmatched";
  }, []);
  const {
    employees,
    loadingUsers,
    loadingMetadata,
    pagination,
    sorting,
    searchQuery,
    metadataFields,
    metadataValues,
    selectedMetadataField,
    selectedMetadataValue,
    handleSearch,
    handleSort,
    handlePaginationChange,
    handleMetadataFieldChange,
    handleMetadataValueChange,
    formatFieldName,
    metadataLabels
  } = useEmployees();

  // State for the Employee modal
  const [isEmployeeModalOpen, setIsEmployeeModalOpen] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(null);

  // Handlers for the new buttons
  const handleAddEmployee = () => {
    setIsEditMode(false);
    setSelectedEmployee(null);
    setIsEmployeeModalOpen(true);
  };

  const handleEditEmployee = (employee: Employee) => {
    setIsEditMode(true);
    setSelectedEmployee(employee);
    setIsEmployeeModalOpen(true);
  };

  const handleEmployeeSubmit = (data: AddEmployeePayload) => {
    console.log(`Employee ${isEditMode ? 'updated' : 'added'} successfully:`, data);
    // Close the modal
    setIsEmployeeModalOpen(false);

    // Refresh the employee list by triggering a search with the current query
    // This will cause the useEffect in useEmployees to refetch the data
    handleSearch(searchQuery);
  };

  const handleDownloadEmployees = async () => {
    try {
      await downloadAllEmployees();
    } catch (error) {
      console.error("Error downloading all employees:", error);
    }
  };

  const [searchValue, setSearchValue] = useState(searchQuery);

  const debouncedSearchValue = useDebounce(searchValue, 500);

  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchValue(e.target.value);
  };

  // Effect to trigger search when debounced value changes
  useEffect(() => {
    if (debouncedSearchValue !== searchQuery) {
      handleSearch(debouncedSearchValue);
    }
  }, [debouncedSearchValue, handleSearch, searchQuery]);

  // Get sort icon for column
  const getColumnSortIcon = (field: keyof Employee | string) => {
    return getSortIcon(field, sorting.field, sorting.direction);
  };

  return (
    <div className="space-y-4 p-4">
      <EmployeeHeader
        searchValue={searchValue}
        onSearchChange={handleSearchInputChange}
        metadataFields={metadataFields}
        metadataValues={metadataValues}
        selectedMetadataField={selectedMetadataField}
        selectedMetadataValue={selectedMetadataValue}
        onMetadataFieldChange={handleMetadataFieldChange}
        onMetadataValueChange={handleMetadataValueChange}
        formatFieldName={formatFieldName}
        loadingMetadata={loadingMetadata}
        onAddEmployee={handleAddEmployee}
        onDownloadEmployees={handleDownloadEmployees}
      />

      <EmployeeTable
        loadingUsers={loadingUsers}
        employees={employees}
        handleSort={handleSort}
        getSortIcon={getColumnSortIcon}
        onRowClick={handleEditEmployee}
      />

      <EmployeePagination
        pagination={pagination}
        handlePaginationChange={handlePaginationChange}
      />

      {/* Employee Modal - handles both add and edit */}
      <EmployeeModal
        open={isEmployeeModalOpen}
        onOpenChange={setIsEmployeeModalOpen}
        metadataFields={metadataLabels}
        onAddEmployee={handleEmployeeSubmit}
        selectedEmployee={selectedEmployee}
        isEditMode={isEditMode}
      />
    </div>
  );
};