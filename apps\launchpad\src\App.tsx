import { <PERSON>hRouter } from "react-router";
// import AuthLayout from "./pages/Auth/auth.layout";
// import Login from "./pages/Auth/Login/Login";˝
// import PublicRoute from "./pages/Public/Public.route";
import AppRoutes from "./AppRoutes";
import { Toaster } from "@repo/ui/components/sonner";
import Session from "@unmatched/modules/session/Session";

function App() {
  // const session = useSession();
  return (
    <Session>
      <Toaster />
      <AppRoutes />
    </Session>
  );
}

export default App;
