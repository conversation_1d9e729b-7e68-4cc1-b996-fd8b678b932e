// Node modules
import React from "react";
import _ from "lodash";
// import { isFieldInvalid, yup } from "unmatched/utils/formik";
import { useFormik } from "unmatched/hooks";
import Header from "../../Header";
import {
  Text,
  Layout,
  Form,
  FormGroup,
  FormControl,
  FormikInput,
  DatePicker,
  FormikAutoSave,
  Div,
  Icon,
  OverlayTrigger,
  Tooltip,
} from "unmatched/components";
import { surveyProperties } from "unmatched/survey/creation/components";
import { patchSurveyFact } from "../../../survey-api";
import { useCreateSurveyContext } from "../../Provider";
import util from "unmatched/utils";
import styled from "styled-components";
import FormikTextInput from "pages/AdminApp/Survey/redundant/FormikTextInput";
import { DateTime } from "luxon";
import useSession from "unmatched/modules/session/hook";
const { isFieldInvalid, yup } = util.formik;

const getValue = (obj: any, key: string) => _.get(obj, key);

const FormControlTemplate = surveyProperties.FormControlTemplate;

const SwitchGroup = surveyProperties.SwitchGroup;

const Pannel = (props: any) => {
  const { children, heading } = props;
  return (
    <Div className="py-3">
      {heading && <Text.H3 className="pb-3">{heading}</Text.H3>}
      {children}
    </Div>
  );
};

const CreateUpwardReview = (props: any) => {
  // activeVersionId
  const { survey, updateValidators, updateBuilderStatus } =
    useCreateSurveyContext();
  const surveyId = survey.data.id;
  const { user } = useSession();
  // const history = useHistory();
  // const toastr = useToastr();
  const initialValues = _.pick(survey.data, [
    "name",
    "description",
    "startDate",
    "endDate",
    "enablePairings",
    "enableVersions",
    "enableMinResponses",
    "enableCustomWaviewer",
    "enableRatingsCommentsWaveOff",
    "enableRatingsWaveOff",
    "assesmentStart",
    "assesmentEnd",
    "thankText",
    "waiverStatus",
    "timezone",
    "availableTimeZones",
    "enableSubmittedStatus",
  ]);
  const isUpwardReview =
    survey?.data && survey.data.type === util.enums.Survey.Upward;

  let validationSchema: any = {
    name: yup.string().required("Name field is required"),
    description: yup
      .string()
      .required("Survey Description is a required field"),
    startDate: yup.string().required("Survey Start date is a required field"),
    endDate: yup.string().required("Survey End date is a required field"),
    thankText: yup.string().required("Thank you note is a required field"),
    enablePairing: yup.bool(),
    enableSubmittedStatus: yup.bool(),
  };

  if (isUpwardReview) {
    validationSchema = {
      ...validationSchema,
      enableMinResponses: yup.bool(),
      enablePairing: yup.bool(),
      enableCustomWaviewer: yup.bool(),
      enableRatingsCommentsWaveOff: yup.bool(),
      enableRatingsWaveOff: yup.bool(),
      assesmentEnd: yup
        .string()
        .required("Assessment End date is a required field"),
      assesmentStart: yup
        .string()
        .required("Assessment Start date is a required field"),
    };
  } else {
    validationSchema = {
      ...validationSchema,
      assesmentEnd: yup
        .string()
        .required("Assessment End date is a required field"),
      assesmentStart: yup
        .string()
        .required("Assessment Start date is a required field"),
    };
  }

  if (initialValues.thankText === null) initialValues.thankText = "";
  const formikOptions = {
    initialValues,
    validationSchema: yup.object().shape(validationSchema),
    onSubmit: (_values: any) => {
      onFormPatch(_values);
    },
  };

  // const isUpwardReview = survey.data.type === S

  const formik = useFormik(formikOptions);

  const { values, handleBlur, handleChange, handleSubmit, handleReset } =
    formik;

  React.useEffect(() => {
    if (!props.viewOnly) {
      const key = `survey-${survey.data.id}`;
      updateValidators({
        key,
        validate: () => {
          return util.formik.formikSubmitForm(formik);
        },
      });
      updateBuilderStatus(util.enums.SurveyStatus.Properties);
      return () => {
        updateValidators({ key, validate: null });
      };
    }
  }, []);

  const onFormPatch = (_values: any) => {
    const payload = {
      ...survey.data,
      ..._values,
    };
    survey.setSaving(true);
    return patchSurveyFact(surveyId, payload).then(
      (updatedData: any) => {
        survey.setSaving(false);
        survey.setData({
          ...payload,
          updatedTime: util.date.getFormatedTime(
            new Date(),
            " MMMM dd, yyyy, HH:mm"
          ),
          updatedAt: util.date.getFormatedTime(
            new Date(updatedData.updatedAt),
            " MMMM dd, yyyy, HH:mm"
          ),
        });
        return true;
      },
      () => {
        survey.setSaving(false);
      }
    );
  };

  // const navigate = () => {
  //   history.push(
  //     util.appUrls.admin.survey.create.getQuestionsUrl(
  //       survey.data.id,
  //       activeVersionId
  //     )
  //   );
  // };

  // const onNext = () => {
  //   util.formik
  //     .formikSubmitForm(formik)
  //     .then(navigate)
  //     .catch((error) => {
  //       toastr.showToast({
  //         show: true,
  //         variant: "warning",
  //         title: `Warning`,
  //         content: `Please check the form fields`,
  //       });
  //     });
  // };

  const getCustomWavierTemplate = () => {
    return (
      <>
        <Div className="mb-3">
          <OverlayTrigger
            placement="bottom"
            overlay={
              <ModTooltip id="button-tooltip">
                <Div className="row">
                  <Div className="col pr-0 pt-2" style={{ maxWidth: 10 }}>
                    <Icon icon="fal fa-info-circle fs-12" />
                  </Div>
                  <Div className="col">
                    <Text.P1 className="text-left fs-12 fw-300 p-2">
                      A waiver is an option given to the reviewers to let them
                      disclose their responses even when there are fewer
                      reviewers who provide feedback to the reviewee.
                    </Text.P1>
                  </Div>
                </Div>
              </ModTooltip>
            }
          >
            {({ ref, ...triggerHandler }) => (
              <>
                <span className="section-title mr-1">Wavier Details</span>
                <i
                  className="far fa-info-circle"
                  ref={ref}
                  {...triggerHandler}
                />
              </>
            )}
          </OverlayTrigger>
        </Div>
        <FormGroup>
          <FormControl.Radio>
            <FormControl.Radio.Label>
              Allow the survey taker to waive off/not waive off ratings and
              comments separately.
            </FormControl.Radio.Label>
            <FormControl.Radio.Input
              name={"waiverStatus"}
              checked={formik.values.waiverStatus === "TERNARY"}
              onBlur={handleBlur}
              onChange={handleChange}
              value="TERNARY"
            />
          </FormControl.Radio>
        </FormGroup>
        <FormGroup>
          <FormControl.Radio>
            <FormControl.Radio.Label>
              Allow the survey taker to waive off/not waive off ratings.
            </FormControl.Radio.Label>
            <FormControl.Radio.Input
              name={"waiverStatus"}
              checked={formik.values.waiverStatus === "BINARY"}
              onBlur={handleBlur}
              onChange={handleChange}
              value="BINARY"
            />
          </FormControl.Radio>
        </FormGroup>
        <FormGroup>
          <FormControl.Radio>
            <FormControl.Radio.Label>None</FormControl.Radio.Label>
            <FormControl.Radio.Input
              name={"waiverStatus"}
              checked={formik.values.waiverStatus === "NONE"}
              onBlur={handleBlur}
              onChange={handleChange}
              value="NONE"
            />
          </FormControl.Radio>
        </FormGroup>
      </>
    );
  };

  const getUpwardReviewTemplate = () => {
    if (!isUpwardReview) return null;
    return (
      <>
        {isUpwardReview}

        <Pannel>{getCustomWavierTemplate()}</Pannel>
      </>
    );
  };

  const getAssesmentTemplate = () => {
    return (
      <Pannel>
        <Div className="section-title mb-4">Assessment Period</Div>
        <Layout.Row>
          <Layout.Col
            xl={4}
            lg={4}
            md={6}
            sm={6}
            xs={12}
            style={{ maxWidth: 340 }}
          >
            <FormControlTemplate
              formik={formik}
              options={{
                label: "Survey Start",
                key: "assesmentStart",
              }}
              required
              style={{ width: "100%" }}
              input={
                <DatePicker
                  disabled={props.viewOnly}
                  onSelect={(date: Date) =>
                    formik.setFieldValue("assesmentStart", date)
                  }
                  selected={formik.values["assesmentStart"]}
                  onChange={(date: Date) =>
                    formik.setFieldValue("assesmentStart", date)
                  }
                  placeholderText={"Choose Date"}
                  customInput={<FormControl.Date placeholder={"Choose Date"} />}
                  // minDate={new Date()}
                  maxDate={new Date(formik.values["assesmentEnd"])}
                />
              }
            />
          </Layout.Col>
          <Layout.Col
            xl={4}
            lg={4}
            md={6}
            sm={6}
            xs={12}
            style={{ maxWidth: 340 }}
          >
            <FormControlTemplate
              formik={formik}
              options={{
                label: "Survey End",
                key: "assesmentEnd",
              }}
              required
              input={
                <DatePicker
                  disabled={props.viewOnly}
                  onSelect={(date: Date) =>
                    formik.setFieldValue("assesmentEnd", date)
                  }
                  minDate={
                    new Date(formik.values["assesmentStart"]) || new Date()
                  }
                  selected={formik.values["assesmentEnd"]}
                  onChange={(date: Date) =>
                    formik.setFieldValue("assesmentEnd", date)
                  }
                  placeholderText={"Choose Date"}
                  customInput={<FormControl.Date placeholder={"Choose Date"} />}
                />
              }
            />
          </Layout.Col>
        </Layout.Row>
      </Pannel>
    );
  };

  return (
    <Form
      onReset={handleReset}
      onSubmit={handleSubmit}
      className="mb-3 mr-xl-5 pr-xl-5"
    >
      <FormikAutoSave
        values={values}
        initialValues={initialValues}
        onSave={onFormPatch}
      >
        <Header
          title={<Text.H1 className="pb-2">{getValue(values, "name")}</Text.H1>}
          metaItem={
            <Text.P1>
              {survey.isSaving ? (
                <>
                  <Icon spin icon="fal fa-spinner-third" /> Saving
                </>
              ) : (
                <>
                  {DateTime.fromISO(
                    new Date(survey.data.updatedAt).toISOString()
                  ).toFormat(" LLL dd, yyyy, hh:mm a")}
                </>
              )}
            </Text.P1>
          }
          breadcrumbs={props.breadcrumbs}
        />
        <Layout.Container fluid>
          <Layout.Row className="mt-2">
            <Layout.Col xl={10}>
              <Pannel>
                <Div className="section-title my-4">General Info</Div>
                <Layout.Row>
                  <Layout.Col xl={10}>
                    <FormikTextInput
                      label="Survey Title"
                      keyName="name"
                      placeholder="Survey Title"
                      formik={formik}
                      required
                    />
                  </Layout.Col>
                </Layout.Row>
                <Layout.Row>
                  <Layout.Col xl={10}>
                    {surveyProperties.getDescriptionTemplate(formik)}
                    {surveyProperties.getThankyouTextTemplate(formik)}
                  </Layout.Col>
                </Layout.Row>
              </Pannel>
              <Pannel>
                <Div style={{ maxWidth: 250 }}>
                  <Div className="section-title mb-4">Timezone</Div>
                  <FormGroup>
                    <FormGroup.Label>Select Timezone</FormGroup.Label>
                    <FormControl.Select value={initialValues.timezone}>
                      {initialValues.availableTimeZones &&
                        initialValues.availableTimeZones.map((item: string) => (
                          <FormControl.SelectItem
                            onSelect={() =>
                              formik.setFieldValue("timezone", item)
                            }
                          >
                            {item}
                          </FormControl.SelectItem>
                        ))}
                    </FormControl.Select>
                  </FormGroup>
                </Div>
              </Pannel>
              <Pannel>
                <Div className="section-title mb-4">Survey Dates</Div>
                <Layout.Row>
                  <Layout.Col
                    xl={4}
                    lg={4}
                    md={6}
                    sm={6}
                    xs={12}
                    style={{ maxWidth: 340 }}
                  >
                    {surveyProperties.getStartDateTemplate(formik, null, {
                      disabled: props.viewOnly,
                      required: true,
                    })}
                  </Layout.Col>
                  <Layout.Col
                    xl={4}
                    lg={4}
                    md={6}
                    sm={6}
                    xs={12}
                    style={{ maxWidth: 340 }}
                  >
                    {surveyProperties.getEndDateTemplate(formik, "", {
                      disabled: props.viewOnly,
                      required: true,
                    })}
                  </Layout.Col>
                </Layout.Row>
              </Pannel>
              {getAssesmentTemplate()}
              {(survey.data.type === "SurveyIndexEngagement" ||
                survey.data.type === "SurveyIndexUpward" ||
                survey.data.type === "SurveyIndex360" ||
                survey.data.type === "SurveyIndexSelf") && (
                <Pannel>
                  {/* <Div className="section-title mb-4">Survey Dates</Div> */}
                  <SwitchGroup label="Show Submission Status">
                    <FormControl.Switch
                      name="enableSubmittedStatus"
                      isInvalid={isFieldInvalid(
                        formik,
                        "enableSubmittedStatus"
                      )}
                      checked={getValue(values, "enableSubmittedStatus")}
                      onBlur={handleBlur}
                      onChange={handleChange}
                      disabled={
                        user.isSuperUser === false &&
                        (survey.data.type === "SurveyIndexUpward" ||
                          survey.data.type === "SurveyIndex360")
                      }
                    />
                  </SwitchGroup>
                </Pannel>
              )}
              {survey.data.type !== "SurveyIndexEngagement" &&
                survey.data.type !== "SurveyIndexSelf" && (
                  <Pannel>
                    {/* <Div className="section-title mb-4">Survey Dates</Div> */}
                    <SwitchGroup label="Allow users/survey takers to add pairings for themselves">
                      <FormControl.Switch
                        name={"enablePairings"}
                        isInvalid={isFieldInvalid(formik, "enablePairings")}
                        checked={getValue(values, "enablePairings")}
                        onBlur={handleBlur}
                        onChange={handleChange}
                      />
                    </SwitchGroup>
                  </Pannel>
                )}

              {getUpwardReviewTemplate()}
              {survey.data?.type === "SurveyIndexUpward" && survey.data?.auxIndex && <Pannel>
                <Div className="section-title mb-4">Auxiliary Survey</Div>
                <Layout.Row>
                  <Layout.Col xl={10}>
                    <FormGroup.Label>Survey Title - {survey.data?.auxTitle} {` (${survey.data?.auxIndex})`}</FormGroup.Label>
                  </Layout.Col>
                </Layout.Row>
              </Pannel>}
            </Layout.Col>
          </Layout.Row>
        </Layout.Container>
      </FormikAutoSave>
      {/* <Footer hideBack onNext={onNext} /> */}
    </Form>
  );
};

export default CreateUpwardReview;

const ModTooltip = styled(Tooltip)`
  .tooltip-inner {
    max-width: 400px;
  }
`;
