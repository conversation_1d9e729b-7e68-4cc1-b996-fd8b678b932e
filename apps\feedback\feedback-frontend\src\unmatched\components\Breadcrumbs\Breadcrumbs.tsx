import React from "react";
import { Link } from "react-router-dom";
import styled from "styled-components";
import { Div } from "@unmatchedoffl/ui-core";

interface BreadcrumbsProps {
  breadcrumbs?: any;
}

const BreadcrumbsWrap = styled(Div)`
  position: absolute;
  display: flex;
  top: 6px;
`;

const Breadcrumbs = (props: BreadcrumbsProps) =>
  props.breadcrumbs?.length > 0 ? (
    <BreadcrumbsWrap>
      {props.breadcrumbs.map((bc: any, i: number) => {
        const DC = bc.route ? Link : Div;
        return (
          <DC
            style={{
              fontSize: 12,
              fontWeight: 500,
              ...(!bc.route && { color: "#6D6D6D" }),
            }}
            {...(bc.route && { to: bc.route })}
            key={i}
          >
            {bc.icon} {bc.label}{" "}
            {i < props.breadcrumbs.length - 1 && (
              <span style={{ color: "#6D6D6D" }}>{">"}&nbsp;</span>
            )}
          </DC>
        );
      })}
    </BreadcrumbsWrap>
  ) : null;

export default Breadcrumbs;
