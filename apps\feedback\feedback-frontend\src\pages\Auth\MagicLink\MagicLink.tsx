import React, { useEffect, useCallback } from "react";
import Loader from "../Loader";
import AuthContainer from "../AuthContainer";
import { useQuery, useState, useHistory } from "unmatched/hooks";
import { performMagicLinkLoginFact } from "../auth-api";
import { useParams } from "react-router-dom";
import { getUserFact } from "unmatched/modules/session/api";
import useSession from "unmatched/modules/session/hook";
import appUrls from "unmatched/utils/urls/app-urls";
import useToastr from "unmatched/modules/toastr/hook";

interface ParamTypes {
  email: string;
  token: string;
}

export default function MagicLogin() {
  // setMeta
  const [meta] = useState({
    title: "Signing in...",
    content: "Please wait while we log you in.",
  });
  const [isLoading, setIsLoading] = useState(false);
  const [, setHasError] = useState(false);
  const { email, token } = useParams<ParamTypes>();
  const history = useHistory();
  const query = useQuery();
  const session = useSession();
  const toastr = useToastr();
  const { isLoggedIn } = useSession();

  const onError = () => {
    setHasError(true);
    toastr.errorToast("Invalid email or link. Please request a new passwordless link again to login.");
    history.push(appUrls.auth.login);
  };

  const performMagicLogin = useCallback(async () => {
    try {
      setIsLoading(true);
      if (isLoggedIn()) session.resetSession();
      const redirectUrl = query.get("redirect");
      let surveyIndex = "";
      if (redirectUrl) {
        const a = redirectUrl.split("/");
        surveyIndex = a[a.length - 1];
      }
      const {
        data: { token: authToken, expiry },
      } = await performMagicLinkLoginFact({ email, token, surveyIndex });
      const userResponse = await getUserFact({ token: authToken }); // get user details
      session.login({
        // do session login and navigate
        token: authToken,
        expiry,
        user: userResponse,
        redirectUrl,
      });
      setIsLoading(false);
    } catch (err: any) {
      onError();
    }
  }, []);

  useEffect(() => {
    performMagicLogin();
    return () => {
      setIsLoading(false);
    };
  }, []);

  return (
    <AuthContainer className="col-xl-4 offset-xl-4">
      {isLoading && <Loader title={meta.title} content={meta.content} />}
    </AuthContainer>
  );
}
