import appUrls from "unmatched/utils/urls/app-urls";
import SurveyReports from "./SurveyReports/SurveyReports";
import Surveylist from "./SurveyList/SurveyList";
import Survey360Reports from "./SurveyReports/Survey360Reports";

const routes = [
  {
    name: "Reports",
    path: appUrls.admin.reports.getSurveyReportsUrl(":id"),
    isExact: false,
    component: SurveyReports,
  },
  {
    name: "360 Reports",
    path: appUrls.admin.reports.getSurvey360ReportsUrl(":id"),
    isExact: false,
    component: Survey360Reports,
  },
  {
    name: "Reports",
    path: appUrls.admin.reports.surveyList,
    isExact: false,
    component: Surveylist,
  },
];

export default routes;
