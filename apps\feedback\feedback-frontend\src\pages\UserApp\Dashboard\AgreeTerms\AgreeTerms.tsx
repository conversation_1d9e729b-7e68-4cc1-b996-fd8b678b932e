import React from "react";
import {
  Button,
  // Div,
  // FormControl,
  Layout,
  Text,
  Card,
  Placeholder,
  CustomModal as Modal,
} from "unmatched/components";
// import BackTodashboard from "../Shared/BackToDashboard";
import { useHistory } from "react-router";
import appUrls from "unmatched/utils/urls/app-urls";
import styled from "styled-components";
import { useParams } from "react-router-dom";
import { getSurveyInfoFactV2, logSurveyVisitFact } from "../dashboard-api";
import util from "unmatched/utils";
import useUtil from "../Shared/hooks/util-hook";
import { getAllFAQs } from "pages/AdminApp/Settings/settings-api";
import FAQView from "../Shared/FAQView";
import useToastr from "unmatched/modules/toastr/hook";

const AgreeTerms = () => {
  const history = useHistory();
  const { id } = useParams<any>();
  const toast = useToastr();
  const [isLoading, setIsLoading] = React.useState(true);
  const [surveyInfo, setSurveyInfo] = React.useState<any>({});
  const dashboardUtil = useUtil();

  const [show, setShow] = React.useState(false);
  const [FAQs, setFAQs] = React.useState([]);

  React.useEffect(() => {
    getCall();
  }, []);
  const getCall = async () => {
    try {
      const data = await getAllFAQs({ survey_index: id });
      setFAQs(data);
    } catch (e: any) {
      new Error(e.message || "");
    }
  };

  React.useEffect(() => {
    async function call() {
      try {
        const surveyInfoResponse = await getSurveyInfoFactV2(id);
        const deadline = surveyInfoResponse.data?.deadline;
        if (deadline) {
          if (new Date(deadline) < new Date()) {
            history.push(appUrls.user.dashboard.default);
            return;
          }
        }
        setTimeout(() => {
          setSurveyInfo(surveyInfoResponse.data);
          setIsLoading(false);
        }, 0);
      } catch (err) {
        setIsLoading(false);
      }
    }
    call();
  }, []);

  const navigateToSurvey = () => {
    const { Survey } = util.enums;
    const routes = appUrls.user.dashboard;
    switch (surveyInfo.type) {
      case Survey._360Degree:
      case Survey.Upward:
        history.push(routes.upwardReview.getUrl(id));
        return;
      case Survey.Engagement:
        history.push(routes.getEngagmentUrl(id));
        return;
      case Survey.Self:
        history.push(routes.getSelfEvaluationUrl(id));
        return;
      case Survey.Exit:
        history.push(routes.getExitUrl(id));
        return;
      default:
        return;
    }
  };

  const onProcessNext = async () => {
    try {
      await logSurveyVisitFact({ index: id });
      navigateToSurvey();
    } catch (error) {
      toast.onError(error)
    }
  };

  return (
    <>
      <Layout.Container>
        <Card className="mt-3">
          <CardHeader
            className="p-3"
            color={dashboardUtil.getSurveyColor(surveyInfo.type)}
          >
            <Text.H2>
              {isLoading ? (
                <>
                  <Placeholder width="col-6" />
                </>
              ) : (
                surveyInfo.title
              )}
            </Text.H2>
          </CardHeader>
          <CardBody className="px-md-5 px-3 py-md-5 py-3">
            <Text.P1>
              {isLoading ? (
                <>
                  <Placeholder width="col-12" />
                  <Placeholder width="col-11" />
                  <Placeholder width="col-12" />
                  <Placeholder width="col-3" />
                </>
              ) : (
                surveyInfo.description
                  .split("\n")
                  .map((item: string, i: number) => {
                    if (item.trim() === "") {
                      return <br key={i} />;
                    }
                    return (
                      <span key={i}>
                        {item}
                        <br />
                      </span>
                    );
                  })
              )}
            </Text.P1>
            <Layout.Flex className="mt-5">
              <Layout.FlexItem>
                <Button onClick={onProcessNext} disabled={isLoading}>
                  Start Survey
                </Button>
                {FAQs?.length > 0 && <Button
                  className="mx-3"
                  disabled={isLoading}
                  onClick={() => setShow(true)}
                  variant="outline-primary"
                >
                  FAQs
                </Button>}
              </Layout.FlexItem>
            </Layout.Flex>
          </CardBody>
        </Card>
      </Layout.Container>
      <Modal
        show={show}
        onHide={() => setShow(false)}
        size="lg"
        aria-labelledby="contained-modal-title-vcenter"
        centered
        dialogClassName="modal-90w"
      >
        <Modal.Header closeButton>
          <Modal.Title>FAQs</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <FAQView faqs={FAQs} />
        </Modal.Body>
      </Modal>
    </>
  );
};
const CardHeader = styled(Card.Header)`
  position: relative;
  background: ${(props: { color: string }) => props.color} !important;
`;
const CardBody = styled(Card.Body)`
  min-height: calc(100vh - 146px);
`;
export default AgreeTerms;
