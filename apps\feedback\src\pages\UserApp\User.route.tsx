import { USER_ROUTES } from "@/app.routes";
import UserLayout from "./User.layout";
import Dashboard from "./dashboard/Dashboard";
import SurveyList from "./surveyList/SurveyList";
import Terms from "./terms/Terms";
import UpwardReview from "./upwardReview/UpwardReview";
import TakeSurvey from "./upwardReview/TakeSurvey";
import EngagementSurvey from "./engagementSurvey/EngagementSurvey";
import SelfEvaluation from "./selfEvaluation/SelfEvaluation";
import ExitSurvey from "./exitSurvey/ExitSurvey";

const UserRoute = [
  {
    path: USER_ROUTES().default,
    element: <UserLayout />,
    children: [
      {
        path: USER_ROUTES().dashboard.default,
        element: <Dashboard />,
        children: [
          {
            path: USER_ROUTES().dashboard.surveyList,
            element: <SurveyList />,
          },
          {
            path: "terms/:id",
            element: <Terms />,
          },
          {
            path: "survey/:id/upward/take/:surveyId",
            element: <TakeSurvey />,
          },
          {
            path: "survey/:id/upward/pairings",
            element: <UpwardReview />,
          },
          {
            path: "survey/:id/upward",
            element: <UpwardReview />,
          },
          {
            path: "survey/:id/engagment",
            element: <EngagementSurvey />,
          },
          {
            path: "survey/:id/self-evaluation",
            element: <SelfEvaluation />,
          },
          {
            path: "survey/:id/exit",
            element: <ExitSurvey />,
          }
        ],
      }
    ],
  },
];

export default UserRoute;
