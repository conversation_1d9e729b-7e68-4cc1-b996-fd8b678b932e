import React from "react";
// import { Div } from "unmatched/components";
import util from "@/unmatched/utils";
// import { useHistory } from "@/unmatched/hooks";
import useSession from "@/unmatched/modules/session/hook";
import { logoutUserFact } from "@/unmatched/modules/session/api";
import Loader from "./Auth/Loader";
import { useNavigate } from "react-router";
import ROUTES from "@/routes";
// import PropTypes from 'prop-types'

const Logout = () => {
  const session = useSession();
  const navigate = useNavigate();

  const navigateToLogin = () => {
    navigate(ROUTES.AUTH.LOGIN);
  };

  const onLogout = async () => {
    // const beacon = (window as any).Beacon;
    try {
      await logoutUserFact();
    
      // if (typeof beacon === 'function') {
      //   beacon("logout");
      // }
      // beacon("config", {
      //   docsEnabled: false,
      // });
      // beacon('navigate', '/ask/chat/')
      session.resetSession();
      navigateToLogin();
    } catch (err) {
      // if (typeof beacon === 'function') {
      //   beacon("logout");
      // }
      // beacon("config", {
      //   docsEnabled: false,
      // });
      // beacon('navigate', '/ask/chat/')
      session.resetSession();
      navigateToLogin();
    }
  };

  React.useEffect(() => {
    if (util.session.getToken()) {
      onLogout();
    } else {
      // const beacon = (window as any).Beacon;
      // if (typeof beacon === 'function') {
      //   beacon("logout");
      // }
      // beacon("config", {
      //   docsEnabled: false,
      // });
      // beacon('navigate', '/ask/chat/')
      navigateToLogin();
    }
  }, []);

  return (
    <div>
      <Loader title="Logging out..." content="Please wait" />
    </div>
  );
};

Logout.propTypes = {};

export default Logout;
