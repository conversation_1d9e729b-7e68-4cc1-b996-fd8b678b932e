import * as yupModule from "yup";

export const yup = yupModule;

export const getFieldErrorMessage = (formik: any, key: string) => {
  return formik.touched[key] && formik.errors[key] ? formik.errors[key] : "";
};

export const isFieldInvalid = (formik: any, key: string) => {
  return formik.touched[key] && !!formik.errors[key];
};

export const isFieldValid = (formik: any, key: string) => {
  return formik.touched[key] && !formik.errors[key];
};

export const formikSubmitForm = (formik: any) => {
  const { submitForm, validateForm } = formik;
  return new Promise((resolve: Function, reject: Function) => {
    submitForm()
      .then(validateForm)
      .then((errors: any) => {
        const isValid = Object.keys(errors).length === 0;
        if (isValid) {
          resolve();
        } else {
          reject(errors);
        }
      })
      .catch((e: any) => {
        reject(e);
      });
  });
};

const data = {
  yup,
  isFieldValid,
  isFieldInvalid,
  getFieldErrorMessage,
  formikSubmitForm,
};

export default data;
