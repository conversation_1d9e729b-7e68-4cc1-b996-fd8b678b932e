import { useState, useEffect } from "react";
import { useParams } from "react-router";
import { useFileDetails } from "./useFileDetails";
import { useDebounce } from "@/hooks";
import { getSortIcon } from "./utils/sortingUtils";
import { downloadFileService, downloadErrorLogService, Employee } from "./filesService";
import {
  FileDetailsHeader,
  EmployeeTable,
  EmployeePagination
} from "./components";

export const FileDetails = () => {
  useEffect(() => {
    document.title = "File Details - Unmatched";
  }, []);
  const params = useParams();
  const { id } = params;

  const {
    loadingFile,
    loadingEmployees,
    fileDetails,
    employees,
    pagination,
    sorting,
    searchQuery,
    handleSearch,
    handleSort,
    handlePaginationChange
  } = useFileDetails(id);

  const [searchValue, setSearchValue] = useState(searchQuery);
  const debouncedSearchValue = useDebounce(searchValue, 500);

  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchValue(e.target.value);
  };

  // Effect to trigger search when debounced value changes
  useEffect(() => {
    if (debouncedSearchValue !== searchQuery) {
      handleSearch(debouncedSearchValue);
    }
  }, [debouncedSearchValue, handleSearch, searchQuery]);

  // Add a simple UI element to show we're rendering
  if (!id) {
    return (
      <div className="p-4">
        <h1 className="text-2xl font-bold">File Details</h1>
        <p className="text-red-500">No file ID provided</p>
      </div>
    );
  }

  // Get sort icon for column
  const getColumnSortIcon = (field: keyof Employee | string) => {
    return getSortIcon(field, sorting.field, sorting.direction);
  };

  const handleEmployeeRowClick = (_employee: Employee) => {
    // This could be implemented later to show employee details
    // This will be implemented in the future
  };

  const handleDownloadFile = () => {
    if (fileDetails?.file) {
      downloadFileService(fileDetails.file);
    } else if (id) {
      // If we don't have the file URL directly, try to fetch it using the file ID
      downloadFileService("", id);
    }
  };

  const handleDownloadErrorLog = () => {
    if (fileDetails?.error_log_file) {
      downloadErrorLogService(fileDetails.error_log_file);
    } else if (id) {
      // If we don't have the error log URL directly, try to fetch it using the file ID
      downloadErrorLogService("", id);
    }
  };

  return (
    <div className="space-y-4 p-4">
      <FileDetailsHeader
        fileDetails={fileDetails}
        searchValue={searchValue}
        onSearchChange={handleSearchInputChange}
        loadingFile={loadingFile}
        onDownloadFile={handleDownloadFile}
        onDownloadErrorLog={handleDownloadErrorLog}
      />

      <div className="mt-6">
        <EmployeeTable
          loadingEmployees={loadingEmployees}
          employees={employees}
          pagination={pagination}
          handleSort={handleSort}
          getSortIcon={getColumnSortIcon}
          onRowClick={handleEmployeeRowClick}
        />

        <EmployeePagination
          pagination={pagination}
          handlePaginationChange={handlePaginationChange}
        />
      </div>
    </div>
  );
};
