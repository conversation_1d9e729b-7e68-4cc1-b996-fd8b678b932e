import React from "react";
import {
  Div,
  Layout,
  Text,
  // <PERSON>ge,
  Card,
  Button,
  FormGroup,
  DatePicker,
  FormControl,
  Icon,
  PageContainer,
  Placeholder,
} from "unmatched/components";
import CustomHeader from "pages/AdminApp/Shared/CustomHeader/CustomHeader";
import appUrls from "unmatched/utils/urls/app-urls";
import icons from "assets/icons/icons";
import {
  patchSurveyPropertyFact,
  updateSurveyEndDateFact,
} from "../../survey-api";
import useToastr from "unmatched/modules/toastr/hook";
import { DateTime } from "luxon";
import { surveyProperties } from "unmatched/survey/creation/components";

const { Paste } = icons;

const Pannel = (props: any) => {
  const { children, heading } = props;
  return (
    <Div className="py-3">
      {heading && <Text.H3 className="pb-3">{heading}</Text.H3>}
      {children}
    </Div>
  );
};
const SwitchGroup = surveyProperties.SwitchGroup;

const SurveyDates = (props: any) => {
  const {
    survey,
    layout, //meta,
    globalLoad,
    rawSurvey,
    fetchSurveyInfo,
    user,
  } = props;

  const { showToast } = useToastr();

  const [dates, setDates] = React.useState<any>({
    startDate: new Date(),
    endDate: new Date(),
  });

  const [properties, setProperties] = React.useState<any>({
    enableSubmittedStatus: false,
  });

  React.useEffect(() => {
    if (survey.isoStartDate === undefined) return;
    setDates({
      startDate: survey.isoStartDate,
      endDate: survey.isoEndDate,
    });
    setProperties({
      enableSubmittedStatus: rawSurvey.enableSubmittedStatus,
    });
  }, [survey]);

  const getTimediff = () => {
    if (dates.startDate === "") return 0;
    const diff = dates.endDate.getTime() - dates.startDate.getTime();
    return Math.ceil(diff / (1000 * 3600 * 24));
  };

  const formatedTimezoneDateTime = (date: Date) => {
    const zone = DateTime.local({ zone: rawSurvey.timezone }).toFormat("ZZ");
    return `${date.getFullYear()}-${("0" + (date.getMonth() + 1)).slice(-2)}-${(
      "0" + date.getDate()
    ).slice(-2)}T${("0" + date.getHours()).slice(-2)}:${(
      "0" + date.getMinutes()
    ).slice(-2)}:00${zone}`;
  };

  const updateSurveyEndDate = async () => {
    const isOngoingSurvey = !rawSurvey.isDraft && !rawSurvey.isLocked;
    const res = await updateSurveyEndDateFact({
      indexID: rawSurvey.id,
      deadline: formatedTimezoneDateTime(dates.endDate),
    });
    if (res.data) {
      showToast({
        variant: "success",
        title: "Success",
        content: isOngoingSurvey
          ? "End date updated successfully"
          : "Survey restarted successfully",
      });

      fetchSurveyInfo();
    }
  };
  const updateSurveyProperties = async () => {
    const res = await patchSurveyPropertyFact(rawSurvey.id, {
      ...properties,
    });
    if (res) {
      showToast({
        variant: "success",
        title: "Success",
        content: "Survey Properties updated successfully",
      });
      fetchSurveyInfo();
    }
  };

  const getSurveyDatesTemplate = () => {
    const isOngoingSurvey = !rawSurvey.isDraft && !rawSurvey.isLocked;
    return (
      <Card className="mb-4" noShadow>
        <Card.Header className="pl-3 py-2">
          <Layout.Flex>
            <Layout.FlexItem>
              <Text.H3>Survey Dates</Text.H3>
            </Layout.FlexItem>
            <Layout.FlexItem className="ml-auto text-muted pr-2">
              <Text.P1>
                <Icon icon="far fa-clock" /> {getTimediff()} days
              </Text.P1>
            </Layout.FlexItem>
          </Layout.Flex>
        </Card.Header>
        <Div className="pl-3 py-2">
          <Layout.Row>
            <Layout.Col xl={3} className="py-2">
              <Text.H3>
                Survey Timezone: <span>{rawSurvey.timezone}</span>
              </Text.H3>
            </Layout.Col>
          </Layout.Row>
          <Layout.Row>
            <Layout.Col xl={3}>
              <FormGroup>
                <FormGroup.Label>Start Date</FormGroup.Label>
                <DatePicker
                  // onSelect={(date: Date) => console.log(date)}
                  onChange={(date: Date) =>
                    setDates({ ...dates, startDate: date })
                  }
                  selected={dates.startDate}
                  customInput={
                    <FormControl.Text size="sm" placeholder={"Choose Date"} />
                  }
                  disabled
                />
              </FormGroup>
            </Layout.Col>
            <Layout.Col className="d-flex align-items-center" xl={3}>
              <FormGroup>
                <FormGroup.Label>End Date</FormGroup.Label>
                <DatePicker
                  onSelect={(date: Date) => console.log(date)}
                  onChange={(date: Date) =>
                    setDates({ ...dates, endDate: date })
                  }
                  selected={dates.endDate}
                  customInput={
                    <FormControl.Text size="sm" placeholder={"Choose Date"} />
                  }
                  showTimeInput
                  dateFormat="MM/dd/yyyy h:mm aa"
                  popperPlacement="bottom"
                  minDate={
                    isOngoingSurvey ? new Date(survey.isoEndDate) : new Date()
                  }
                />
              </FormGroup>
              {/* <Button variant="link" className="py-0">
                    <Icon icon="fal fa-pencil" />
                  </Button> */}
            </Layout.Col>

            {/* <Layout.Col className="text-right align-self-center">
              {meta.canEnd && (
                <Button variant="link" className="text-danger">
                  End Survey
                </Button>
              )}
              {meta.canPause && (
                <Button variant="link" className="text-danger">
                  Pause Survey
                </Button>
              )}
              {meta.canRestart && (
                <Button variant="link" className="text-danger">
                  Restart Survey
                </Button>
              )}
              {meta.canForceStart && (
                <Button variant="link" className="text-danger">
                  Force Start Survey
                </Button>
              )}
            </Layout.Col> */}
            <Div className="d-flex align-items-center px-2">
              <Button
                disabled={survey.isoEndDate === dates.endDate}
                style={{ height: 28, margin: 5 }}
                onClick={updateSurveyEndDate}
                className="py-0"
              >
                {isOngoingSurvey ? "Save" : "Restart"}
              </Button>
            </Div>
          </Layout.Row>
        </Div>
      </Card>
    );
  };
  const getSurveySubmissionStatusTemplate = () => {
    // const isOngoingSurvey = !rawSurvey.isDraft && !rawSurvey.isLocked;
    return (
      <Card className="mb-4" noShadow>
        <Card.Header className="pl-3 py-2">
          <Layout.Flex>
            <Layout.FlexItem>
              <Text.H3>Survey Properties</Text.H3>
            </Layout.FlexItem>
          </Layout.Flex>
        </Card.Header>
        <Div className="pl-3 py-2">
          <Pannel>
            {/* <Div className="section-title mb-4">Survey Dates</Div> */}
            <SwitchGroup label="Show Submission Status">
              <FormControl.Switch
                name="enableSubmittedStatus"
                // isInvalid={isFieldInvalid(formik, "enablePairings")}
                checked={properties.enableSubmittedStatus}
                onChange={(e: any) =>
                  setProperties({
                    ...properties,
                    enableSubmittedStatus: e.target.checked,
                  })
                }
                disabled={
                  user.isSuperUser === false &&
                  (survey.type === "SurveyIndexUpward" ||
                    survey.type === "SurveyIndex360")
                }
              />
            </SwitchGroup>
          </Pannel>
          <Div className="d-flex align-items-center">
            <Button
              // disabled={survey.isoEndDate === dates.endDate}
              style={{ height: 28, margin: 5 }}
              onClick={updateSurveyProperties}
              className="py-0"
            >
              Save
            </Button>
          </Div>
        </Div>
      </Card>
    );
  };
  return (
    <div>
      <CustomHeader
        style={{ marginLeft: layout.marginLeft }}
        title={
          <Layout.Flex>
            <Layout.FlexItem>
              <Text.H1 className="pb-2">
                {globalLoad ? <Placeholder size="col-12" /> : survey.name}
              </Text.H1>
            </Layout.FlexItem>
            <Layout.FlexItem className="pl-2">
              {/* {globalLoad ? (
                <Placeholder width="col-12" />
              ) : (
                <Badge variant={meta.variant}>{meta.title}</Badge>
              )} */}
            </Layout.FlexItem>
          </Layout.Flex>
        }
        breadcrumbs={[
          {
            label: "Surveys",
            icon: <Paste className="grey-icon__svg" />,
            route: appUrls.admin.survey.default,
          },
          { label: "Manage Survey" },
          { label: survey.name },
        ]}
      />

          {getSurveyDatesTemplate()}
          {getSurveySubmissionStatusTemplate()}
   
    </div>
  );
};

// Surveydates.propTypes = {

// }

export default SurveyDates;
