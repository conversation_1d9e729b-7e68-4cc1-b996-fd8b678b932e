/* eslint-disable @typescript-eslint/no-explicit-any */
import SettingsLayout from "./settings.layout";
import { ROUTES } from "../../../routes";
import { Employees } from "./employees/Employees";
import { Files } from "./files/Files";
import { FileDetails } from "./files/FileDetails";
import { TextTemplates } from "./text-templates";
import SettingsHome from "./SettingsHome";

const SettingsRoutes: any = [
  {
    path: ROUTES.SETTINGS.ROOT,
    layout: () => <SettingsLayout />,
    children: [
      {
        path: ROUTES.SETTINGS.ROOT,
        protected: true,
        component: SettingsHome,
      },
      {
        path: ROUTES.SETTINGS.EMPLOYEES,
        protected: true,
        component: Employees,
      },
      {
        path: ROUTES.SETTINGS.FILES,
        protected: true,
        component: Files,
      },
      {
        path: ROUTES.SETTINGS.FILE_DETAILS,
        protected: true,
        component: FileDetails,
      },
      {
        path: ROUTES.SETTINGS.TEXT_TEMPLATES,
        protected: true,
        component: TextTemplates,
      },
    ],
  },
];

export default SettingsRoutes;
