import React from "react";
import appUrls from "unmatched/utils/urls/app-urls";
import icons from "../../assets/icons/icons";

const {
  Graph, //Cog, Group,
  Page,
  Paste,
  Email,
} = icons;

const data = [
  // {
  //   id: 1,
  //   title: "Dashboard",
  //   icon: <Chart />,
  //   route: appUrls.admin.home,
  //   children: [],
  // },
  {
    id: 2,
    title: "Survey",
    icon: <Paste />,
    route: appUrls.admin.survey.default,
    children: [],
  },
  {
    id: 7,
    title: "Emails",
    icon: <Email />,
    route: appUrls.admin.email,
    children: [],
  },
  {
    id: 3,
    title: "Analytics",
    icon: <Graph />,
    route: appUrls.admin.analytics.default,
    children: [
      // {
      //   id: 1,
      //   checkActive: `${appUrls.admin.analytics.default}/stats`,
      //   route: appUrls.admin.analytics.statistics.default,
      //   title: "Statistics",
      // },
      {
        id: 1,
        checkActive: false,
        route: appUrls.admin.analytics.statistics.default,
        title: "360, Upward & Self",
        type: "label",
      },
      {
        id: 2,
        checkActive: `${appUrls.admin.analytics.default}/people`,
        route: appUrls.admin.analytics.people.list,
        title: "People Analytics",
      },
      {
        id: 3,
        checkActive: `${appUrls.admin.analytics.default}/aggregate`,
        route: appUrls.admin.analytics.getAggregateUrl(),
        title: "Aggregate Analytics",
      },
      {
        id: 6,
        checkActive: `${appUrls.admin.analytics.default}/ranking`,
        route: appUrls.admin.analytics.geRankingListUrl(),
        title: "Ranking List",
      },
      {
        id: 4,
        checkActive: false,
        route: "",
        title: "Engagement",
        type: "label",
      },
      {
        id: 5,
        checkActive: `${appUrls.admin.analytics.default}/engagement`,
        route: appUrls.admin.analytics.engagement.default,
        title: "Engagement Analytics",
      },
      {
        id: 6,
        checkActive: false,
        route: "",
        title: "Exit Analytics",
        type: "label",
      },
      {
        id: 7,
        checkActive: `${appUrls.admin.analytics.default}/exit`,
        route: appUrls.admin.analytics.exit.default,
        title: "Exit Analytics",
      },
    ],
  },
  {
    id: 4,
    title: "Reports",
    icon: <Page />,
    route: appUrls.admin.reports.default,
    children: [],
  },
  // {
  //   id: 5,
  //   title: "Settings",
  //   icon: <Cog />,
  //   route: appUrls.admin.settings.getURL(),
  //   children: [],
  // },
  // {
  //   id: 6,
  //   title: "Data Load",
  //   icon: <Group />,
  //   route: appUrls.admin.dataLoad.default,
  //   children: [],
  // },
];

export default data;
