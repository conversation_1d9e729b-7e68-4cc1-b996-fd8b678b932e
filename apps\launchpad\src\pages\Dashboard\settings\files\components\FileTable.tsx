import React from "react";
import { Table } from "@repo/ui/components/table";
import { FileTableHeader } from "./FileTableHeader";
import { FileTableBody } from "./FileTableBody";
import { File } from "../filesService";

interface FileTableProps {
  loadingFiles: boolean;
  files: File[];
  handleSort: (field: keyof File | string) => void;
  getSortIcon: (field: keyof File | string) => React.ReactNode;
  onRowClick?: (file: File) => void;
  onDeleteFile?: (id: string) => Promise<boolean>;
  onDownloadFile?: (fileUrl: string, fileId?: string) => void;
  onDownloadErrorLog?: (errorLogUrl: string, fileId?: string) => void;
}

export const FileTable: React.FC<FileTableProps> = ({
  loadingFiles,
  files,
  handleSort,
  getSortIcon,
  onRowClick,
  onDeleteFile,
  onDownloadFile,
  onDownloadErrorLog,
}) => {
  return (
    <div className="rounded-md border">
      <Table>
        <FileTableHeader
          handleSort={handleSort}
          getSortIcon={getSortIcon}
        />
        <FileTableBody
          loadingFiles={loadingFiles}
          files={files}
          onRowClick={onRowClick}
          onDeleteFile={onDeleteFile}
          onDownloadFile={onDownloadFile}
          onDownloadErrorLog={onDownloadErrorLog}
        />
      </Table>
    </div>
  );
};
