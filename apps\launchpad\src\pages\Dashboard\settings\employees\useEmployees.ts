import { useEffect, useState, useMemo } from "react";
import { getUsersService, getMetadataLabels, FormattedMetadataField } from "./employeesService";
import { getNextSortDirection, getBackendFieldName } from "./utils/sortingUtils";

export type Employee = {
  key: string;
  email: string;
  empId: string;
  firstName: string;
  id: string;
  lastName: string;
  metadata: Record<string, unknown>;
  departed: string;
};

export type SortDirection = "asc" | "desc";

export type FilterOption = {
  label: string;
  value: string;
};

export const useEmployees = () => {
    const [loadingUsers, setLoadingUsers] = useState(false);
    const [loadingMetadata, setLoadingMetadata] = useState(false);
    const [employees, setEmployees] = useState<Employee[]>([]);
    const [metadataLabels, setMetadataLabels] = useState<FormattedMetadataField[]>([]);
    const [pagination, setPagination] = useState({
      pageIndex: 0,
      pageSize: 10,
      pageCount: 0,
      totalItems: 0,
    });
    const [sorting, setSorting] = useState<{ field: string; direction: SortDirection }>({
      field: "",
      direction: "asc",
    });
    const [searchQuery, setSearchQuery] = useState("");

    // Filter state for the two-dropdown approach
    const [selectedMetadataField, setSelectedMetadataField] = useState<string | null>("class_year");
    const [selectedMetadataValue, setSelectedMetadataValue] = useState<string | null>(null);

    // Fetch metadata labels from the API
    useEffect(() => {
      const fetchMetadataLabels = async () => {
        setLoadingMetadata(true);
        try {
          const labels = await getMetadataLabels();
          setMetadataLabels(labels);
        } catch (error) {
          console.error("Error fetching metadata labels:", error);
        } finally {
          setLoadingMetadata(false);
        }
      };

      fetchMetadataLabels();
    }, []);

    // Extract available metadata fields from the API response
    const metadataFields = useMemo(() => {
      // If we have metadata labels from the API, use those
      if (metadataLabels.length > 0) {
        return metadataLabels.map(field => field.field);
      }

      // Fallback to extracting from employee data if API hasn't returned yet
      const fields = new Set<string>();

      employees.forEach((employee) => {
        if (employee.metadata && typeof employee.metadata === 'object') {
          Object.keys(employee.metadata).forEach((key) => fields.add(key));
        }
      });

      // If no fields are found, add some default fields for testing
      if (fields.size === 0) {
        fields.add("class_year");
        fields.add("department");
        fields.add("current_level");
        fields.add("practice_group");
        fields.add("office_location");
      }

      return Array.from(fields).sort();
    }, [metadataLabels, employees]);

    // Get all possible values for the selected metadata field
    const metadataValues = useMemo(() => {
      if (!selectedMetadataField) return [];

      // If we have metadata labels from the API, use those
      const metadataField = metadataLabels.find(field => field.field === selectedMetadataField);
      if (metadataField) {
        return metadataField.values;
      }

      // Fallback to extracting from employee data if API hasn't returned yet
      const values = new Set<string>();

      employees.forEach(employee => {
        if (employee.metadata &&
            typeof employee.metadata === 'object' &&
            employee.metadata[selectedMetadataField] !== undefined) {
          values.add(String(employee.metadata[selectedMetadataField]));
        }
      });

      // If no values found, add some default values for testing
      if (values.size === 0) {
        if (selectedMetadataField === "class_year") {
          ["2000", "2012", "2015", "2016", "2018", "2021"].forEach(v => values.add(v));
        } else if (selectedMetadataField === "department") {
          ["Banking", "Litigation", "IP", "Corporate"].forEach(v => values.add(v));
        } else if (selectedMetadataField === "current_level") {
          ["Associate", "Partner", "Counsel"].forEach(v => values.add(v));
        } else if (selectedMetadataField === "practice_group") {
          ["Banking", "Litigation", "IP", "Corporate"].forEach(v => values.add(v));
        } else if (selectedMetadataField === "office_location") {
          ["New York", "Chicago", "San Francisco", "Los Angeles"].forEach(v => values.add(v));
        }
      }

      return Array.from(values).sort().map(value => ({
        label: value,
        value
      }));
    }, [selectedMetadataField, metadataLabels, employees]);

    useEffect(() => {
      // Reset the selected value when the field changes
      setSelectedMetadataValue(null);
    }, [selectedMetadataField]);

    useEffect(() => {
      // Prepare filter parameters
      const params: Record<string, unknown> = {
        page: pagination.pageIndex + 1,
        page_size: pagination.pageSize,
        search: searchQuery,
      };

      // Add ordering if present
      if (sorting.field) {
        params.ordering = `${sorting.direction === "desc" ? "-" : ""}${getBackendFieldName(sorting.field)}`;
      }

      // Add metadata filter if both field and value are selected
      if (selectedMetadataField && selectedMetadataValue) {
        params[selectedMetadataField] = selectedMetadataValue;
      }

      getEmployees(params);
    }, [
      pagination.pageIndex,
      pagination.pageSize,
      searchQuery,
      sorting,
      selectedMetadataField,
      selectedMetadataValue
    ]);

    const getEmployees = async (params?: Record<string, unknown>) => {
        try {
          setLoadingUsers(true);
          const response = await getUsersService(params || {});
          setLoadingUsers(false);
          setEmployees(response.results);
          setPagination(prev => ({
            ...prev,
            pageCount: response.totalPages,
            totalItems: response.totalElements
          }));
          return response;
        } catch (err) {
          console.error("Error fetching employees:", err);
          setLoadingUsers(false);
        }
      };

    const handleSearch = (query: string) => {
      setSearchQuery(query);
      setPagination(prev => ({
        ...prev,
        pageIndex: 0 // Reset to first page on new search
      }));
    };

    // Handle column sorting for both regular fields and metadata fields
    const handleSort = (field: keyof Employee | string) => {
      const direction = getNextSortDirection(field, sorting.field, sorting.direction);
      handleSorting(field, direction);
    };

    const handleSorting = (field: string, direction: SortDirection) => {
      setSorting({ field, direction });
    };

    const handlePaginationChange = (pageIndex: number, pageSize: number) => {
      setPagination(prev => ({
        ...prev,
        pageIndex,
        pageSize
      }));
    };

    // Handle metadata field selection
    const handleMetadataFieldChange = (field: string | null) => {
      setSelectedMetadataField(field);
      setSelectedMetadataValue(null); // Reset value when field changes

      // Reset to first page
      setPagination(prev => ({
        ...prev,
        pageIndex: 0
      }));
    };

    // Handle metadata value selection
    const handleMetadataValueChange = (value: string | null) => {
      setSelectedMetadataValue(value);

      // Reset to first page
      setPagination(prev => ({
        ...prev,
        pageIndex: 0
      }));
    };

    // Format field name for display (use display_name from API or format snake_case/camelCase)
    const formatFieldName = (field: string): string => {
      // If we have metadata from the API, use the display_name
      const metadataField = metadataLabels.find(meta => meta.field === field);
      if (metadataField) {
        return metadataField.displayName;
      }

      // Fallback to formatting the field name
      return field
        .replace(/_/g, ' ')
        .replace(/([A-Z])/g, ' $1')
        .replace(/^./, (str) => str.toUpperCase())
        .trim();
    };

  return {
    loadingUsers,
    loadingMetadata,
    employees,
    pagination,
    sorting,
    searchQuery,
    metadataFields,
    metadataValues,
    selectedMetadataField,
    selectedMetadataValue,
    metadataLabels,
    handleSearch,
    handleSorting,
    handlePaginationChange,
    handleSort,
    handleMetadataFieldChange,
    handleMetadataValueChange,
    formatFieldName
  };
};