import React, { useEffect, useState } from "react";
import { useFormik } from "formik";
// import * as yup from "yup";
// import { isFieldInvalid } from "unmatched/utils/formik";
import { FormControl, FormGroup, Div, Text } from "unmatched/components";
import { useCreateSurveyContext } from "../../Provider";
import util from "unmatched/utils";

const { isFieldInvalid, yup } = util.formik;

const OptionLabel = ({
  text,
  onSubmit,
  option,
  viewOnly,
}: {
  text: string;
  onSubmit: Function;
  option: any;
  setValidations: Function;
  viewOnly?: any;
}) => {
  const [focus, setFocus] = useState(false);

  const formik = useFormik({
    initialValues: {
      option: text || "",
    },
    validationSchema: yup.object().shape({
      option: yup.string().required(),
    }),
    onSubmit: () => {
      // on submit
    },
  });

  const { updateValidators } = useCreateSurveyContext();

  const { submitForm, values, handleBlur, handleChange, errors } = formik;

  useEffect(() => {
    const canValidate = !!option.id;
    if (!canValidate) return;
    const key = `${option.type}-option-${option.id}`;
    updateValidators({
      key,
      validate: () => util.formik.formikSubmitForm(formik),
    });
    return () => {
      updateValidators({
        key,
        validate: null,
      });
    };
  }, []);

  const getTextClassName = () => {
    return focus || errors["option"] || !values["option"]
      ? ""
      : "border-none bg-white";
  };

  const onBlur = (e: any) => {
    setFocus(false);
    handleBlur(e);
    submitForm().then(() => {
      onSubmit(values, option);
    });
  };

  const onEnter = (e: any) => {
    const isEnter = util.preventEnterKey(e);
    if (isEnter) {
      submitForm().then(() => {
        onSubmit(values, option);
      });
    }
  };

  return (
    <Div>
      {viewOnly ? (
        <Text.P1 className="pt-2">{values["option"]}</Text.P1>
      ) : (
        <FormControl.Text
          name={"option"}
          isInvalid={isFieldInvalid(formik, "option")}
          value={values["option"]}
          onBlur={onBlur}
          onKeyDown={onEnter}
          onKeyUp={util.preventEnterKey}
          className={getTextClassName()}
          onChange={handleChange}
          placeholder={!option.id ? "Add option" : "Label"}
          onFocus={() => {
            setFocus(true);
          }}
        />
      )}

      <FormGroup.InValidFeedback>
        {isFieldInvalid(formik, "option") && errors["option"]}
      </FormGroup.InValidFeedback>
    </Div>
  );
};

export default OptionLabel;
