import React from "react";
import { PageContainer, Div } from "unmatched/components";
import Rankings from "./Rankings";
import Categories from "./Categories";
import { useParams } from "unmatched/hooks";
import CustomHeader from "pages/AdminApp/Shared/CustomHeader/CustomHeader";
import icons from "assets/icons/icons";

const { Graph } = icons;

const RankingList = () => {
  const params = useParams<any>();

  return (
    <Div>
      <CustomHeader
        title="Ranking list"
        breadcrumbs={[
          {
            label: "Analytics",
            icon: <Graph className="grey-icon__svg" />,
          },
          { label: "Ranking List" },
        ]}
      />
      <PageContainer noHeader={true}>
        <Div style={{ marginTop: '5rem' }} className="pt-6 px-3">
          <Rankings />
          <Categories id={params.id} />
        </Div>
      </PageContainer>
    </Div>
  );
};

export default RankingList;
