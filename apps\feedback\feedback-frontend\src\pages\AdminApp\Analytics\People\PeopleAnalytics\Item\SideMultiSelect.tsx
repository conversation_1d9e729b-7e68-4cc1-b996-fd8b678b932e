import React from "react";
import styled from "styled-components";
import {
  <PERSON><PERSON>,
  Card,
  Icon,
  Text,
  Div,
  FormControl,
} from "unmatched/components";
// import PropTypes from 'prop-types'

const SideMultiSelect = (props: any) => {
  const { sections, selected, type, setSelected } = props;

  const filteredRatingSections = () => {
    // const arr: any[] = [];
    // sections.map((section: any, i: number) => {
    //   const items: any = [];
    //   section.questions
    //     .filter((item: any) => item.type === "Rating")
    //     .map((item: any) => items.push(item));
    //   if (items.length > 0) {
    //     return arr.push({
    //       ...sections[i],
    //       questions: items,
    //     });
    //   }
    //   return;
    // });

    // return arr;
    return sections.filter((item: any) => item.type === "questionrating");
  };

  const onSelect = (evt: any, key: string) => {
    if (evt.target.checked) {
      const dt = [...selected];
      dt.push(key);
      setSelected((old: any) => {
        return { ...old, [type]: dt };
      });
    } else {
      const dt = selected.filter((item: any) => item !== key);
      setSelected((old: any) => {
        return { ...old, [type]: dt };
      });
    }
  };

  const selectAll = () => {
    const arr: any = [];
    filteredRatingSections().forEach((el: any) =>
      el.map((_d: any) => arr.push(_d.key))
    );
    setSelected((old: any) => {
      return { ...old, [type]: arr };
    });
  };

  // const onSectionSelect = (evt: any, arr: string[]) => {
  //   if (evt.target.checked) {
  //     const items = selected;
  //     arr.forEach((el: any) => {
  //       if (!items.includes(el)) {
  //         items.push(el);
  //       }
  //     });
  //     setSelected((old: any) => {
  //       return { ...old, [type]: [...items] };
  //     });
  //   } else {
  //     let items = selected;
  //     arr.forEach((el: any) => {
  //       items = items.filter((item: any) => item !== el);
  //     });
  //     setSelected((old: any) => {
  //       return { ...old, [type]: items };
  //     });
  //   }
  //   return;
  // };

  // const selectionStatus = (arr: any[]) => {
  //   let status = true;
  //   arr.forEach((el: any) => {
  //     if (!selected.includes(el)) {
  //       status = false;
  //     }
  //   });
  //   return status;
  // };
  return (
    <>
      <Overlay onClick={props.onClose}></Overlay>
      <Card
        className=""
        style={{
          minHeight: "calc(100vh)",
          maxHeight: "calc(100vh)",
          position: "relative",
          zIndex: 100000,
        }}
      >
        <Card.Header className="p-3">
          <Text.H2 className="float-left"></Text.H2>
          <Button
            className="float-right p-0 text-muted"
            variant="link"
            onClick={props.onClose}
          >
            <Icon icon="fal fa-times" />
          </Button>
        </Card.Header>
        <Card.Body className="bg-white v-scroll">
          <Div className="row mb-3">
            <Div className="col">
              {selected.length > 0 ? (
                <Text.P1 className="text-primary pt-2">
                  {selected.length} Selected
                </Text.P1>
              ) : (
                ""
              )}
            </Div>
            <Div className="col text-right">
              {selected.length > 0 ? (
                <Button
                  variant="outline-danger"
                  className="fs-12"
                  onClick={() => setSelected({ ...selected, [type]: [] })}
                >
                  Unselect all
                </Button>
              ) : (
                <Button variant="primary" className="fs-12" onClick={selectAll}>
                  Select all
                </Button>
              )}
            </Div>
          </Div>
          <Div className="px-0">
            {[""].map((item: any) => {
              // const itemIDs = item.questions.map((item: any) => item.key);
              const getQuestionsTemplate = () => {
                return filteredRatingSections().map(
                  (question: any, index: number) => {
                    // if (question.type === "Rating")
                    return (
                      <Div className="mx-0 cursor-pointer row" key={index}>
                        <Div
                          className="border px-2 text-center py-2 col"
                          style={{ maxWidth: 35 }}
                        >
                          <FormControl.Checkbox>
                            <FormControl.Checkbox.Input
                              checked={selected.includes(question.id)}
                              onChange={(evt: any) =>
                                onSelect(evt, question.id)
                              }
                            />
                          </FormControl.Checkbox>
                        </Div>

                        <Div className="border px-2 py-2 col fs-12">
                          {question.label}
                        </Div>
                      </Div>
                    );
                  }
                );
              };
              return (
                <Div key={item.id} className="pb-4">
                  <Text.H3 className="py-2">{item.title}</Text.H3>
                  <Div className="mx-0 cursor-pointer row bg-light">
                    <Div
                      className="border px-2 text-center py-2 col"
                      style={{ maxWidth: 35 }}
                    >
                      <FormControl.Checkbox>
                        {/* <FormControl.Checkbox.Input
                          checked={selectionStatus(itemIDs)}
                          onClick={(evt: any) => onSectionSelect(evt, itemIDs)}
                        /> */}
                      </FormControl.Checkbox>
                    </Div>

                    <Div className="border px-2 py-2 col fs-12 fw-700">
                      Questions
                    </Div>
                  </Div>
                  {getQuestionsTemplate()}
                </Div>
              );
            })}
          </Div>
        </Card.Body>
      </Card>
    </>
  );
};

// SideMultiSelect.propTypes = {

// }
const Overlay = styled(Div)`
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 99;
  backdrop-filter: blur(3px);
  cursor: pointer;
`;
export default SideMultiSelect;
