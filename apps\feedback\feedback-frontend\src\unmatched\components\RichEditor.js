import React from "react";
import ReactQuill from "react-quill";
import styled from "styled-components";
import { Div, Layout, FormGroup } from "@unmatchedoffl/ui-core";

class RichEditor extends React.PureComponent {
  constructor(props) {
    super(props);
    this.state = { editorHtml: props.value, mountedEditor: false };
    this.quillRef = null;
    this.reactQuillRef = null;
    this.handleChange = this.handleChange.bind(this);
    this.handleClick = this.handleClick.bind(this);
    this.attachQuillRefs = this.attachQuillRefs.bind(this);
  }

  componentDidMount() {
    this.attachQuillRefs();
  }

  componentDidUpdate() {
    this.attachQuillRefs();
  }

  attachQuillRefs() {
    // Ensure React-Quill reference is available:
    if (typeof this.reactQuillRef.getEditor !== "function") return;
    // Skip if Quill reference is defined:
    if (this.quillRef != null) return;

    const quillRef = this.reactQuillRef.getEditor();
    if (quillRef != null) this.quillRef = quillRef;
  }

  clearFormat() {
    const range = this.quillRef.getSelection();
    this.quillRef.removeFormat(range.index, 1);
  }

  handleClick(title) {
    const range = this.quillRef.getSelection();
    let position = range ? range.index : 0;

    this.quillRef.insertText(position, `${title}`);
    // this.quillRef.insertText(position, `${title}`, {
    //   color: "#518cff",
    // });
    // this.quillRef.removeFormat(position + title.length, 2);
    // this.quillRef.insertText(position + title.length, " ", {
    //   color: "black",
    // });
  }

  handleChange(html, delta, source, editor) {
    const text = editor.getText();
    // const newHTML = `${html}`
    //   .split("&lt;")
    //   .map((el) => {
    //     if (!el.includes("&gt;")) {
    //       return el.substring(el.indexOf("</span>"));
    //     }
    //     return `&lt;${el}`;
    //   })
    //   .join("");
    const body = html.replaceAll("<p><br></p>", "<br />");
    this.props.onChange(body, text);
  }

  render() {
    return (
      <div>
        <InputOptsWrap className="d-flex">
          <FormGroup.Label className="pr-2">Insert: &nbsp;</FormGroup.Label>
          <Layout.Row className="align-items-center">
            {this.props.insertOptions.map((item) => {
              return (
                <CustomCol key={item.id}>
                  <InputOpt onClick={() => this.handleClick(`<${item.title}>`)}>
                    {item.title}
                  </InputOpt>
                </CustomCol>
              );
            })}
          </Layout.Row>
        </InputOptsWrap>
        <ReactQuill
          ref={(el) => {
            this.reactQuillRef = el;
            this.props?.setRef?.(el);
          }}
          theme={"snow"}
          onChange={this.handleChange}
          modules={RichEditor.modules}
          formats={RichEditor.formats}
          defaultValue={this.props.value}
          placeholder={this.props.placeholder}
          readOnly={this.props.disabled ?? false}
        />
      </div>
    );
  }
}

/*
 * Quill modules to attach to editor
 * See https://quilljs.com/docs/modules/ for complete options
 */
RichEditor.modules = {};
RichEditor.modules.toolbar = [
  //   ["bold", "italic", "underline", "strike"], // toggled buttons
  //   ["blockquote", "code-block"], // blocks
  //   [{ header: 1 }, { header: 2 }], // custom button values
  //   [{ list: "ordered" }, { list: "bullet" }], // lists
  //   [{ script: "sub" }, { script: "super" }], // superscript/subscript
  //   [{ indent: "-1" }, { indent: "+1" }], // outdent/indent
  //   [{ direction: "rtl" }], // text direction
  //   [{ size: ["small", false, "large", "huge"] }], // custom dropdown
  //   [{ header: [1, 2, 3, 4, 5, 6, false] }], // header dropdown
  //   [{ color: [] }, { background: [] }], // dropdown with defaults
  //   [{ font: [] }], // font family
  //   [{ align: [] }], // text align
  //   ["clean"], // remove formatting
];

/*
 * Quill RichEditor formats
 * See https://quilljs.com/docs/formats/
 */
RichEditor.formats = [
  "header",
  "font",
  "background",
  "color",
  "code",
  "size",
  "bold",
  "italic",
  "underline",
  "strike",
  "blockquote",
  "list",
  "bullet",
  "indent",
  "script",
  "align",
  "direction",
  "link",
  "image",
  "code-block",
  "formula",
  "video",
];

export default RichEditor;

const InputOpt = styled.button`
  font-family: "Inter";
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 18px;
  padding: 2px 5px;
  text-align: center;
  background: #fbfbfb;
  border: 1px solid #dadada;
  border-radius: 2px;
  cursor: pointer;
`;

const CustomCol = styled(Div)`
  padding: 0px;
  margin: 0 5px;
`;

const InputOptsWrap = styled(Div)`
  background-color: #f2f2f2;
  border-bottom: transparent;
  padding: 7px;
  border: 1px solid #f0f0f1;
  border-radius: 4px;
`;
