import React, { useState } from "react";
import { Upload, Download } from "lucide-react";
import {
Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@repo/ui/components/dialog";
import { Button } from "@repo/ui/components/button";
import {
  FormattedMetadataField,
  AddEmployeePayload,
  Employee,
  getSampleEmployeeListDownload
} from "../employeesService";
import { useEmployeeForm } from "../hooks/useEmployeeForm";
import { EmployeeForm } from "./EmployeeForm";
import { FileUploadModal } from "./FileUploadModal";

interface EmployeeModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  metadataFields: FormattedMetadataField[];
  onAddEmployee: (employee: AddEmployeePayload) => void;
  selectedEmployee?: Employee | null;
  isEditMode?: boolean;
}

export const EmployeeModal: React.FC<EmployeeModalProps> = ({
  open,
  onOpenChange,
  metadataFields,
  onAddEmployee,
  selectedEmployee = null,
  isEditMode = false,
}) => {
  const [showForm, setShowForm] = useState(isEditMode);
  const [showUploadModal, setShowUploadModal] = useState(false);

  const {
    form,
    onSubmit,
    isSubmitting,
    requiredFields,
    isLoadingRequiredFields,
  } = useEmployeeForm({
    metadataFields,
    onSuccess: (data) => {
      onAddEmployee(data);
      setShowForm(false);
      onOpenChange(false);
    },
    employee: selectedEmployee,
    isEditMode,
  });

  const handleDownloadSample = async () => {
    try {
      await getSampleEmployeeListDownload({ f: "xlsx" });
    } catch (error) {
      console.error("Error downloading sample file:", error);
    }
  };

  const handleCancel = () => {
    if (isEditMode) {
      onOpenChange(false);
    } else {
      setShowForm(false);
    }
  };

  const handleUploadSuccess = () => {
    // Refresh the employee list after successful upload
    onAddEmployee({ first_name: '', last_name: '', email: '', metadata: {} });
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[798px] max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {isEditMode
                ? "Edit Employee"
                : (showForm ? "Add Employees Manually" : "Add Employees")}
            </DialogTitle>
            <DialogDescription>
              {isEditMode
                ? "Update employee information in the system."
                : (showForm
                  ? "Enter all the required fields and add an employee to the employee list."
                  : "Add employees using the sample file by providing all the below details and upload the file. (or) Add employees manually."
                )
              }
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            {/* Required fields list - only shown when form is not visible */}
            {!showForm && !isEditMode && (
              <>
                <h3 className="font-medium mb-2">Required fields :</h3>

                {/* List of required fields */}
                <div className="space-y-1 mb-4 text-sm">
                  {isLoadingRequiredFields ? (
                    <p className="text-gray-500">Loading required fields...</p>
                  ) : requiredFields.length > 0 ? (
                    requiredFields.map((field) => (
                      <p key={field} className="text-gray-700">{field}</p>
                    ))
                  ) : (
                    <p className="text-gray-500">No required fields found</p>
                  )}
                </div>

                <div className="flex items-center gap-2 pt-2 mb-4">
                  <Button
                    type="button"
                    variant="link"
                    className="h-auto p-0 text-sm flex items-center gap-1"
                    onClick={handleDownloadSample}
                  >
                    <Download className="h-3 w-3" />
                    Download sample excel(.xlsx) file
                  </Button>
                </div>
              </>
            )}

            {/* Form for manual entry - shown in edit mode or after clicking "Manually Add" */}
            {(showForm || isEditMode) && (
              <EmployeeForm
                form={form}
                onSubmit={onSubmit}
                metadataFields={metadataFields}
                isSubmitting={isSubmitting}
                isEditMode={isEditMode}
                onCancel={handleCancel}
              />
            )}

            {/* Footer buttons - only shown when form is not visible and not in edit mode */}
            {!showForm && !isEditMode && (
              <DialogFooter className="flex justify-between sm:justify-between gap-2">
                <Button
                  type="button"
                  onClick={() => setShowForm(true)}
                >
                  Manually Add
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  className="flex items-center gap-2"
                  onClick={() => setShowUploadModal(true)}
                >
                  <Upload className="h-4 w-4" />
                  Upload a File
                </Button>
              </DialogFooter>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* File Upload Modal */}
      <FileUploadModal
        open={showUploadModal}
        onOpenChange={setShowUploadModal}
        onSuccess={handleUploadSuccess}
      />
    </>
  );
};
