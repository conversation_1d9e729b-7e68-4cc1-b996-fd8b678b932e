import React from "react";
import { keys, map, get } from "lodash";
import {
  Table,
  Icon,
  Text,
  Button,
  Div,
  Layout,
  Dropdown,
  Modal,
  OverlayTrigger,
  Tooltip,
} from "unmatched/components";
import useToastr from "unmatched/modules/toastr/hook";
import { useTable, useXHR, useHistory } from "unmatched/hooks";
import util from "unmatched/utils";
import {
  cloneSurveyFact,
  get360SurveysFact,
  // getStatsDataParallel,
  getSurveysFact,
  getSurveysFactV2,
  linkSelfSurveyFact,
  saveSurveyFact,
} from "../survey-api";
import { DateTime } from "luxon";
import { SURVEY_TYPES } from "./SurveyList";
import styled from "styled-components";
import icons from "assets/icons/icons";
import loading from "assets/images/load.gif";

const { Page } = icons;

export const StyledActionsDropdown = styled(Dropdown)`
  position: absolute;
  width: 80px;
  height: 30px;
  display: flex;
  margin-left: -8px;
`;

const SurveysView = (props: any) => {
  const { search, isActive, filter, resourceType } = props;
  const [collapse, setCollapse] = React.useState({});

  const history = useHistory();
  const surveyData = useXHR({ defaultResponse: [] }, "surveys");
  const toastr = useToastr();
  const tableMeta = useTable({
    totalPages: 0,
    size: 15,
    page: 1,
  });

  React.useEffect(() => {
    if (isActive) {
      const page = 1;
      tableMeta.onPageSelect(page);
      getSurveys({ search, page });
    }
  }, [search, isActive, resourceType]);

  const [columnsData, setColumnsData] = React.useState<any>({
    sNo: { label: "No." },
    title: { label: "Title", hasSort: true, sortValue: "", sortKey: "title" },
    ...(filter === SURVEY_TYPES.SCHEDULED && {
      startDate: {
        label: "Start Date",
        hasSort: true,
        sortValue: "",
        sortKey: "startDate",
      },
    }),
    endDate: {
      label: "End Date",
      hasSort: true,
      sortValue: "",
      sortKey: "deadline",
    },
    activeUsers: {
      label: "Active Users",
      hasSort: false,
      sortValue: "",
      sortKey: "active_user",
    },
    inactiveUsers: {
      label: "Inactive Users",
      hasSort: false,
      sortValue: "",
      sortKey: "inactive_user",
    },
    completed: {
      label: "Participation %",
      hasSort: false,
      sortValue: "",
      sortKey: "participated_percentage",
    },
    submited: {
      label: "Submitted",
      hasSort: false,
      sortValue: "",
      // sortKey: "participated_percentage",
    },
    type: { label: "Type", hasSort: false },
    actions: { label: "Actions" },
  });

  const [cloneModal, setCloneModal] = React.useState<any>({
    show: false,
    survey: null,
  });

  const [selfModal, setSelfModal] = React.useState<any>({
    show: false,
    survey: null,
  });

  const getColumns = () => {
    const coumnsList = keys(columnsData);
    return map(coumnsList, (key: string) => ({
      ...get(columnsData, key),
      key,
    }));
  };

  const getSortProp = (s: any) =>
    ({
      [SURVEY_TYPES.ON_GOING]: "deadline",
      [SURVEY_TYPES.ENDED]: "-deadline",
    }[s]);

  const [ordering, setOrdering] = React.useState(getSortProp(filter));

  const getSurveys = (params?: any) => {
    surveyData.setLoading(true);
    const lParams = {
      search,
      filter,
      page: tableMeta.page,
      size: tableMeta.size,
      sort: ordering,
      resource_type: resourceType,
      ...(params || {}),
    };

    const onSuccess = ({ data, totalPages, totalElements }: any) => {
      surveyData.setSurveys(data);
      tableMeta.updatePagination({ totalPages, totalItems: totalElements });
      surveyData.setLoading(false);
    };

    const onError = (err: any) => {
      surveyData.onError(err);
      toastr.onError(err);
    };

    if (resourceType === "SurveyIndex360") {
      get360SurveysFact(lParams, surveyData.getMetaConfig()).then(
        onSuccess,
        onError
      );
    } else {
      getSurveysFactV2(lParams, surveyData.getMetaConfig()).then(
        (data) => {
          getSurveysFact(lParams, surveyData.getMetaConfig()).then(
            ({ data }: any) => {
              surveyData.setSurveys(data);
            },
            onError
          );
          onSuccess(data)
        },
        onError
      );

      // getSurveysFactV2(lParams, surveyData.getMetaConfig()).then(
      //   (data: any) => {
      //     getStatsDataParallel(data.rawResults).then((finalRes: any) => {
      //       onSuccess({ ...data, data: finalRes });
      //     });
      //     onSuccess(data);
      //   },
      //   onError
      // );
    }
  };

  const isCollapsed = (id: number) => {
    return util.lib.get(collapse, id);
  };

  const toggleCollapse = (id: number) => {
    setCollapse((_collapse: any) => ({
      ..._collapse,
      [id]: !_collapse[id],
    }));
  };

  const getVersionsTemplate = (_survey: any, hasVersions: boolean) => {
    const isResourceType360 = resourceType === "SurveyIndex360";
    const { versions, id } = _survey;
    if (hasVersions && isCollapsed(id)) {
      return versions.map((item: any, index: number) => {
        return (
          <Table.Row
            {...(resourceType === "SurveyIndex360" && {
              onClick: () => {
                history.push(util.appUrls.admin.survey.getSurveyUrl(item.id));
              },
            })}
            key={item.id}
            even
          >
            {!index && <Table.Data rowSpan={versions.length}></Table.Data>}
            <Table.Data>
              <Text.P1>{item.title}</Text.P1>
            </Table.Data>
            <Table.Data>
              {item.endDate ? (
                <Text.P1>
                  {DateTime.fromISO(item.endDate).toFormat(
                    " LLL dd, yyyy, HH:mm"
                  )}
                </Text.P1>
              ) : (
                "-"
              )}
            </Table.Data>
            <Table.Data>
              <Text.P1>{item.activeUsers || "-"}</Text.P1>
            </Table.Data>
            <Table.Data>
              <Text.P1>{item.inactiveUsers || "-"}</Text.P1>
            </Table.Data>
            <Table.Data>
              <Text.P1>{item.completed !== undefined ? `${item.completed}%` :  renderCellLoading()}</Text.P1>
            </Table.Data>
            <Table.Data>
              <Text.P1>{isResourceType360 ? '-' : item?.admin_stats?.submitted_count ?? renderCellLoading()}</Text.P1>
            </Table.Data>
            <Table.Data>
              <Text.P1>
                {isResourceType360
                  ? "360 Degree Feedback Survey"
                  : getSurveyType(resourceType)}
              </Text.P1>
            </Table.Data>
            {!index && <Table.Data rowSpan={versions.length}></Table.Data>}
          </Table.Row>
        );
      });
    }
  };

  const getSurveyType = (type: string) => {
    const types: any = {
      SurveyIndexUpward: "Upward Feedback",
      SurveyIndexEngagement: "Engagement Survey",
      SurveyIndexSelf: "Self Assessment Survey",
      SurveyIndex360: "360 Degree Feedback",
    };
    return types[type] ?? "";
  };

  const cloneSurvey = (id: string) => {
    cloneSurveyFact({ id }).then(
      ({ data }: any) => {
        toastr.onSucces({
          title: "Success",
          content: "Survey cloned successfully.",
        });
        history.push(
          util.appUrls.admin.survey.create.getUpwardReviewUrl(data.id)
        );
      },
      (err: any) => {
        toastr.onError(err);
      }
    );
  };
  const onCreateSelfEvaluation = async (id: string) => {
    try {
      const selfData = await saveSurveyFact(util.enums.Survey.Self);
      await linkSelfSurveyFact({
        selfID: selfData.data.id,
        id,
      });
      history.push(
        util.appUrls.admin.survey.create.getUpwardReviewUrl(selfData.data.id)
      );
    } catch (err: any) {
      console.log(err);
      toastr.onError(err);
    }
  };

  const renderRowActions = (item: any) => {
    return (
      <Div className="d-flex">
        <OverlayTrigger
          placement="top"
          overlay={
            <Tooltip id="`tooltip-bottom" className="fs-10">
              Clone
            </Tooltip>
          }
        >
          <Div
            onClick={(e: any) => {
              e.stopPropagation();
              setCloneModal({ show: true, survey: item.title, id: item.id });
            }}
            className="text-left text-truncate"
            style={{ maxWidth: 300 }}
          >
            <Icon className="fs-14" icon="fal fa-clone text-primary mr-2" />
          </Div>
        </OverlayTrigger>
        {filter === "ENDED" &&
        (item.type === "SurveyIndexUpward" ||
          item.type === "SurveyIndex360" ||
          resourceType === "SurveyIndex360") ? (
          <OverlayTrigger
            placement="top"
            overlay={
              <Tooltip id="`tooltip-bottom" className="fs-10">
                Comment Review
              </Tooltip>
            }
          >
            <Div
              onClick={(e: any) => {
                e.stopPropagation();
                history.push(
                  util.appUrls.admin.survey.editComments.getQuestions(item.id)
                );
              }}
              className="text-left text-truncate"
              style={{ maxWidth: 300 }}
            >
              <Icon className="fs-14" icon="fal fa-pencil text-primary mr-2" />
            </Div>
          </OverlayTrigger>
        ) : null}

        {props.showReports && (
          <OverlayTrigger
            placement="top"
            overlay={
              <Tooltip id="`tooltip-bottom" className="fs-10">
                Show Reports
              </Tooltip>
            }
          >
            <Div
              onClick={(e: any) => {
                e.stopPropagation();
                history.push(
                  util.appUrls.admin.reports.getSurveyReportsUrl(item.id)
                );
              }}
              className="text-left text-truncate"
              style={{ maxWidth: 300 }}
            >
              <Page width={16} className={"blue__svg mr-2"} />
            </Div>
          </OverlayTrigger>
        )}
        <OverlayTrigger
          placement="top"
          overlay={
            <Tooltip id="`tooltip-bottom" className="fs-10">
              Manage Survey
            </Tooltip>
          }
        >
          <Div
            onClick={(e: any) => {
              e.stopPropagation();
              history.push(util.appUrls.admin.survey.getSurveyUrl(item.id));
            }}
            className="text-left text-truncate"
            style={{ maxWidth: 300 }}
          >
            <Icon className="fs-14" icon="fal fa-cog text-primary mr-2" />{" "}
          </Div>
        </OverlayTrigger>

        {item.type === "SurveyIndexUpward" ? (
          <OverlayTrigger
            placement="top"
            overlay={
              <Tooltip id="`tooltip-bottom" className="fs-10">
                Create Self Assessment Survey
              </Tooltip>
            }
          >
            <Div
              onClick={(e: any) => {
                e.stopPropagation();
                setSelfModal({ show: true, survey: item.title, id: item.id });
              }}
              className="text-left text-truncate"
              style={{ maxWidth: 300 }}
            >
              <Icon className="fs-14" icon="fal fa-user text-primary mr-2" />{" "}
            </Div>
          </OverlayTrigger>
        ) : null}
      </Div>
    );
  };

  // const [items360, setItems360] = React.useState<any>({});

  const renderCellLoading = () => {
    return <Div><img width="15" height="15" src={loading} /></Div>
  }

  const getRowsTemplate = () => {
    return surveyData.surveys.map((item: any, index: number) => {
      const isEven = util.math.isEven(index);
      const isCollapse = isCollapsed(item.id);
      const hasVersions = !!(item.versions && item.versions.length > 1);
      const isResourceType360 = resourceType === "SurveyIndex360";
      // debugger;
      return (
        <React.Fragment key={item.id}>
          <Table.Row
            {...(!isResourceType360 && {
              onClick: () => {
                history.push(util.appUrls.admin.survey.getSurveyUrl(item.id));
              },
            })}
            even={isEven}
          >
            <Table.Data>
              <Text.P1>{tableMeta.getIndex(index)}</Text.P1>
            </Table.Data>
            <Table.Data>
              <div className="cursor-pointer">
                <Layout.Flex>
                  <Text.P1
                    className="flex-grow-1"
                    {...(!isResourceType360 && {
                      onClick: () => {
                        history.push(
                          util.appUrls.admin.survey.create.getUpwardReviewUrl(
                            item.id
                          )
                        );
                      },
                    })}
                  >
                    {item.title}
                  </Text.P1>
                  {hasVersions && (
                    <Div className="ml-auto align-self-start">
                      <Button
                        className="py-0 text-dark fs-16"
                        variant="link"
                        onClick={(e: any) => {
                          e.stopPropagation();
                          toggleCollapse(item.id);
                        }}
                      >
                        <Icon
                          icon={
                            !isCollapse
                              ? "fas fa-caret-right"
                              : "fas fa-caret-down"
                          }
                        />
                      </Button>
                    </Div>
                  )}
                </Layout.Flex>
              </div>
            </Table.Data>
            {filter === SURVEY_TYPES.SCHEDULED && (
              <Table.Data width="190px">
                <Text.P1>
                  {DateTime.fromISO(item.startDate).toFormat(
                    " LLL dd, yyyy, HH:mm"
                  )}
                </Text.P1>
              </Table.Data>
            )}
            <Table.Data width="190px">
              {!isResourceType360 ? (
                <Text.P1>
                  {DateTime.fromISO(item.endDate).toFormat(
                    " LLL dd, yyyy, HH:mm"
                  )}
                </Text.P1>
              ) : (
                "-"
              )}
            </Table.Data>
            <Table.Data>
              <Text.P1>
                {!isResourceType360 ? item.admin_stats?.active_user ?? renderCellLoading() : "-"}
              </Text.P1>
            </Table.Data>
            <Table.Data>
              <Text.P1>
                {!isResourceType360 ? item.admin_stats?.inactive_user ?? renderCellLoading() : "-"}
              </Text.P1>
            </Table.Data>
            <Table.Data>
              <Text.P1>
                {!isResourceType360 ? item.completed !== undefined ? `${item.completed}%` : renderCellLoading() : "-"}
              </Text.P1>
            </Table.Data>
            <Table.Data>
              <Text.P1>
                {isResourceType360 ? '-' : item?.admin_stats?.submitted_count ?? renderCellLoading()}
              </Text.P1>
            </Table.Data>
            <Table.Data>
              <Text.P1>
                {isResourceType360
                  ? "360 Degree Survey Group"
                  : getSurveyType(item.type)}
              </Text.P1>
            </Table.Data>
            <Table.Data width="120px">
              {/* <Button className="p-0" preventDefault variant="link"> */}
              {/* <Link
                onClick={(e) => e.stopPropagation()}
                to={util.appUrls.admin.survey.getSurveyUrl(item.id)}
              >
                <Text.P1
                  className="d-inline-block mr-2"
                  style={{ minWidth: 75 }}
                >
                  <Icon icon="fal fa-cog text-primary mr-1" />{" "}
                  <span className="text-black fs-12">Manage</span>
                </Text.P1>
              </Link>
              {filter === "ENDED" &&
              (item.type === "SurveyIndexUpward" ||
                item.type === "SurveyIndex360") ? (
                <Link
                  onClick={(e) => e.stopPropagation()}
                  to={util.appUrls.admin.survey.editComments.getQuestions(
                    item.id
                  )}
                >
                  <Text.P1 className="d-inline-block" style={{ minWidth: 91 }}>
                    <Icon icon="fal fa-pencil text-primary mr-1" />{" "}
                    <span className="text-black fs-12">Comments</span>
                  </Text.P1>
                </Link>
              ) : null} */}

              {!isResourceType360 && renderRowActions(item)}
              {/* </Button> */}
            </Table.Data>
          </Table.Row>
          {getVersionsTemplate(item, hasVersions)}
        </React.Fragment>
      );
    });
  };

  return (
    <Div className="pt-4">
      <Table
        columns={getColumns()}
        isLoading={surveyData.isLoading}
        type="bordered"
        rows={surveyData.surveys}
        customRows
        render={getRowsTemplate}
        onSort={(item: any) => {
          const label = util.label.getSortingLabel(
            item.sortKey,
            item.sortValue
          );
          setColumnsData((_columns: any) =>
            tableMeta.resetColumns(_columns, item)
          );
          setOrdering(label);
          getSurveys({ sort: label, search });
        }}
        onPageSelect={(number: number) => {
          tableMeta.onPageSelect(number);
          getSurveys({ page: number, search });
        }}
        hasPagination
        activePage={tableMeta.page}
        pages={tableMeta.totalPages}
        size={tableMeta.size}
        totalItems={tableMeta.totalItems}
        {...(search && { notFoundMsg: util.noSearchRecordsFoundMsg })}
      />
      <Modal show={cloneModal.show} centered>
        <Modal.Header className="py-3">
          <Modal.Title>
            <Text.H3>Clone Survey</Text.H3>
          </Modal.Title>
        </Modal.Header>
        <Modal.Body className="pb-5">
          <Text.P1>
            Are you sure, you want to clone <b>{cloneModal.survey}</b>?
          </Text.P1>
        </Modal.Body>
        <Modal.Footer className="py-2">
          <Button className="fs-12" onClick={() => cloneSurvey(cloneModal.id)}>
            Yes
          </Button>
          <Button
            className="fs-12"
            variant="outline-danger"
            onClick={() => setCloneModal({ show: false, survey: null })}
          >
            No
          </Button>
        </Modal.Footer>
      </Modal>

      <Modal show={selfModal.show} centered>
        <Modal.Header className="py-3">
          <Modal.Title>
            <Text.H3>Create Self Survey</Text.H3>
          </Modal.Title>
        </Modal.Header>
        <Modal.Body className="pb-5">
          <Text.P1>
            Are you sure, you want to create Self Assessment{" "}
            <b>{selfModal.survey}</b>?
          </Text.P1>
        </Modal.Body>
        <Modal.Footer className="py-2">
          <Button
            className="fs-12"
            onClick={() => onCreateSelfEvaluation(selfModal.id)}
          >
            Yes
          </Button>
          <Button
            className="fs-12"
            variant="outline-danger"
            onClick={() => setSelfModal({ show: false, survey: null })}
          >
            No
          </Button>
        </Modal.Footer>
      </Modal>

      {/* onCreateSelfEvaluation(item.id) */}
    </Div>
  );
};

export default SurveysView;

