import { cloneDeep } from "lodash";

export const setState = (state: any, payload: any) => {
  return {
    ...state,
    ...payload,
  };
};

export const getXHRState = () => {
  return {
    isLoading: false,
    error: {
      statusCode: 0,
      errorCode: "",
      msg: "",
    },
  };
};

export const generateReducer = (ACTIONS: any, initialState: any) => {
  return (
    state = cloneDeep(initialState),
    action: { type: string; payload: any }
  ) => {
    const { type, payload } = action;
    return ACTIONS[type] ? ACTIONS[type](state, payload) : state;
  };
};

const addItem = (list: any, item: any) => {
  return [...list, item];
};

const updateItem = (list: any, index: number, item: any) => {
  list.splice(index, 1, item);
  return [...list];
};

const removeItem = (list: any, index: number) => {
  list.splice(index, 1);
  return [...list];
};

export const mutateList = {
  add: addItem,
  update: updateItem,
  remove: removeItem,
};

const store = {
  getXHRState,
};

export default store;
