// import _ from "lodash";
import React from "react";
// import { Text, Div } from '../../../../components';
// Toolt<PERSON>, Legend,
// import PropTypes from 'prop-types';
import {
  AreaChart as AC,
  Area,
  // Cell,
  XAxis,
  YAxis,
  // CartesianGrid,
  // Tooltip,
  // Legend,
  ResponsiveContainer,
} from "recharts";

const CustomizedDot = (props: any) => {
  const { cx, cy } = props;

  return (
    <circle
      cx={cx}
      cy={cy}
      r={5}
      stroke="#518CFF"
      strokeWidth={1}
      fill="white"
    />
  );
};
const AreaChart = (props: any) => {
  const { data } = props;

  return (
    <ResponsiveContainer width={"100%"} height={300}>
      <AC width={900} height={300} data={data}>
        {/* <CartesianGrid horizontalPoints={[1]} stroke="#DADADA">
          <CartesianAxis axisLine={{ stroke: "#DADADA" }} />
        </CartesianGrid> */}
        <defs>
          <linearGradient id="colorUv" x1="0" y1="0" x2="0" y2="1">
            <stop offset="5%" stopColor="#518CFF" stopOpacity={0.5} />
            <stop offset="95%" stopColor="#FFFFFF" stopOpacity={0.1} />
          </linearGradient>
        </defs>
        <XAxis
          dataKey="name"
          tickLine={false}
          axisLine={{ stroke: "#DADADA" }}
          stroke="#DADADA"
          tick={{
            fontSize: "10px",
            dy: 0,
            stroke: "#838383",
            fontFamily: "Inter",
            fontWeight: "100",
          }}
          padding={{ left: 80, right: 80 }}
        />
        <YAxis dataKey="data" />
        {/* <Tooltip /> */}
        {/* <Legend /> */}
        {/* {getBarsTemplate()} */}
        {/* <Bar
          dataKey="data"
          fill="#96B9FF"
          // shape={<RoundedBar />}
          // stroke={_.get(colors, 0)} */}
        {/* /> */}
        <Area
          // type="monotone"
          dataKey="data"
          fillOpacity={1}
          stroke="#518CFF"
          fill="url(#colorUv)"
          dot={<CustomizedDot />}
        />
      </AC>
    </ResponsiveContainer>
  );
};

// LineChart;.propTypes = {

// }

export default AreaChart;
