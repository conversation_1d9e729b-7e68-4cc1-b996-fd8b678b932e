import React, { useState, useRef, useEffect } from "react";
import { ChevronDown } from "lucide-react";
import { FilterOption } from "../useEmployees";

interface FilterDropdownProps {
  label: string;
  options: FilterOption[];
  value: string | null;
  onChange: (value: string | null) => void;
  disabled?: boolean;
  formatFieldName: (field: string) => string;
}

export const FilterDropdown: React.FC<FilterDropdownProps> = ({
  label,
  options,
  value,
  onChange,
  disabled = false,
  formatFieldName,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        className="flex items-center justify-between w-[180px] px-3 py-2 border rounded-md bg-background"
        onClick={() => setIsOpen(!isOpen)}
        disabled={disabled}
      >
        <span className="text-sm">
          {value ? options.find(o => o.value === value)?.label || value : formatFieldName(label)}
        </span>
        <ChevronDown className="h-4 w-4 ml-2" />
      </button>

      {isOpen && (
        <div className="absolute z-10 mt-1 w-[180px] bg-background border rounded-md shadow-lg">
          <div
            className="px-3 py-2 text-sm hover:bg-muted cursor-pointer"
            onClick={() => {
              onChange(null);
              setIsOpen(false);
            }}
          >
            All {formatFieldName(label)}
          </div>
          {options.map(option => (
            <div
              key={option.value}
              className="px-3 py-2 text-sm hover:bg-muted cursor-pointer"
              onClick={() => {
                onChange(option.value);
                setIsOpen(false);
              }}
            >
              {option.label}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};
