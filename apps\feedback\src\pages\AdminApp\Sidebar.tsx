import { useEffect, useRef, useState } from "react";
import DASHBOARD_LINKS from "./admin-app-meta";
import {
  TooltipProvider,
  Tooltip,
  TooltipTrigger,
} from "@repo/ui/components/tooltip";

function Sidebar() {
  return (
    <TooltipProvider>
      <div className="dashboard-sidebar w-[60px] bg-secondary h-full fixed left-0 top-0">
        <div className="text-center">
          {DASHBOARD_LINKS.filter((el: any) => el.id !== 6).map((item: any) => (
            <div key={`sidebar-${item.id} un-sidebar`}>
                {/* {item.icon} */}
              {/* <SidebarMenuItem
                buttonContent={item.icon}
                title={item.title}
                items={item.children}
                route={item.route}
                //   isActive={history.location.pathname.includes(item.route)}
                //   as={Link}
                //   pathName={history.location.pathname}
              /> */}
            </div>
          ))}
          {/* <MenuFooter>
          <SidebarMenuItem
            isActive
            buttonContent={name}
            bottom
            title="Session"
            items={settingRoutes}
          />
        </MenuFooter> */}
        </div>
      </div>
    </TooltipProvider>
  );
}

// const SidebarMenuItem = (props: {
//   buttonContent: React.ReactNode;
//   title: string;
//   items?: any[];
//   route?: string;
// }) => {
//   const [tooltip, setTooltip] = useState(false);
//   const [show, setShow] = useState(false);
//   const [position, setPosition] = useState({ x: 0, y: 0 });
//   // const history = useHistory();
//   const buttonRef = useRef<HTMLDivElement>(null);

//   useEffect(() => {
//     const cb = () => {
//       setShow(false);
//     };
//     window.addEventListener("click", cb);
//     return () => window.removeEventListener("click", cb);
//   });

//   const getTooltip = () => {
//     return (
//       <Overlay
//         target={() => buttonRef.current || null}
//         show={tooltip}
//         placement={"right"}
//       >
//         {(_props) => (
//           <Tooltip id={props.title} {..._props}>
//             {props.title}
//           </Tooltip>
//         )}
//       </Overlay>
//     );
//   };

//   if (props.items && props.items.length) {
//     const getDropdownItemLabel = (item: any) => {
//       if (!item.onClick) {
//         return item.title;
//         // return (
//         //   <props.as to={item.route} className="text-white">
//         //     {item.title}
//         //   </props.as>
//         // );
//       }
//       return item.title;
//     };

//     const getDropdownMenuTemplate = () => {
//       return (
//         <DropdownMenu {...position}>
//           {props.items.map((item: any) =>
//             item.type && item.type === "label" ? (
//               <Text.P1
//                 className="text-muted px-4 py-1"
//                 key={item.id}
//                 style={{
//                   borderLeftWidth: 5,
//                   borderStyle: "solid",
//                   borderColor: "transparent",
//                 }}
//               >
//                 {item.title}
//               </Text.P1>
//             ) : (
//               <props.as to={item.route} className="text-white">
//                 <DropdownItem
//                   onClick={() => (item.onClick ? item.onClick() : "")}
//                   key={item.id}
//                   as={div}
//                   className="pl-4 cursor-pointer fs-12"
//                   isActive={props.pathName.includes(item.route)}
//                 >
//                   {getDropdownItemLabel(item)}
//                 </DropdownItem>
//               </props.as>
//             )
//           )}
//         </DropdownMenu>
//       );
//     };

//     const showMenu = (evt: any) => {
//       evt.preventDefault();
//       evt.stopPropagation();
//       setShow(true);
//       const { left, top, width } = evt.currentTarget.getBoundingClientRect();
//       setPosition({
//         x: left + width,
//         y: props.bottom ? top - 80 : top,
//       });
//     };

//     return (
//       <div className="py-2">
//         {getTooltip()}
//         <Dropdown show={show} drop="right">
//           <DropdownToggle
//             title={props.buttonTitle}
//             ref={buttonRef}
//             variant="link"
//             className="bg-secondary px-0"
//             block
//             size="lg"
//             onClick={showMenu}
//             onMouseEnter={() => {
//               setTooltip(true);
//             }}
//             onMouseLeave={() => {
//               setTooltip(false);
//             }}
//           >
//             <DropdownLabel
//               title={props.title}
//               className={`m-auto ${props.isActive ? "btn-theme" : null}`}
//               isActive={props.isActive}
//             >
//               <div
//                 className={props.isActive ? "icon-wrap-active" : "icon-wrap"}
//               >
//                 {props.buttonContent}
//               </div>
//             </DropdownLabel>
//           </DropdownToggle>
//           {getDropdownMenuTemplate()}
//         </Dropdown>
//       </div>
//     );
//   } else {
//     return (
//       <div
//         onMouseEnter={() => {
//           setTooltip(true);
//         }}
//         onMouseLeave={() => {
//           setTooltip(false);
//         }}
//       >
//         {getTooltip()}
//         <Button
//           to={props.route}
//           as={props.as}
//           ref={buttonRef}
//           variant="link"
//           className="bg-secondary text-white py-2 px-0"
//           block
//           size="lg"
//         >
//           <DropdownLabel
//             className={`m-auto ${props.isActive ? "btn-theme" : null}`}
//             isActive={props.isActive}
//           >
//             <div className={props.isActive ? "icon-wrap-active" : "icon-wrap"}>
//               {props.buttonContent}
//             </div>
//           </DropdownLabel>
//         </Button>
//       </div>
//     );
//   }
// };

export default Sidebar;
