import Create<PERSON>ur<PERSON> from ".././Survey/CreateSurvey/CreateSurvey";
import ManageSurvey from ".././Survey/ManageSurvey/ManageSurvey";
import ViewQuestions from "./EditComments/CommentsQuestion";
import ViewComments from "./EditComments/ViewComments";
import appUrls from "unmatched/utils/urls/app-urls";
import SurveyList from "./SurveyList/SurveyList";

const routes = [
  {
    name: "Create Survey",
    path: appUrls.admin.survey.create.getUrl(":id"),
    isExact: false,
    component: CreateSurvey,
  },
  {
    name: "Manage Single Survey",
    path: appUrls.admin.survey.getSurveyUrl(":id"),
    isExact: false,
    component: ManageSurvey,
  },
  {
    name: "Survey List",
    path: appUrls.admin.survey.getSurveysUrl(),
    isExact: false,
    component: SurveyList,
  },
  {
    name: "All Comment Questions",
    path: appUrls.admin.survey.editComments.getQuestions(":id"),
    isExact: true,
    component: ViewQuestions,
  },
  {
    name: "View Comments",
    path: appUrls.admin.survey.editComments.viewComments(":id", ":qID"),
    isExact: false,
    component: ViewComments,
  },
];

export default routes;
