import React, { useState } from "react";
import {
  Div,
  Text,
  Layout,
  Button,
  FormControl,
  Table,
  FormGroup,
  Icon,
} from "unmatched/components";
import { useTable, useXHR } from "unmatched/hooks";
import util from "unmatched/utils";
import Header from "../../Header";
import { useCreateSurveyContext } from "../../Provider";
import {
  assosiateParticipantFact,
  getAllUserFilesFact,
  removeParticipantFact,
} from "pages/AdminApp/Survey/survey-api";

export const Empty = () => {
  return (
    <Div
      className="d-flex flex-column align-items-center"
      style={{ height: "calc(100vh - 120px)", padding: "150px 0" }}
    >
      <img src={util.images.PAIRICON} alt="" />
      <Text.P1 className="pb-4 text-center" style={{ margin: 30 }}>
        Add users to be able to set up surveys for your employees. <br /> You
        can add users in bulk by uploading files in excel .xls, .xlsx, or .csv
        file formats.
      </Text.P1>
    </Div>
  );
};

export default function AddUsers({
  auxiliary,
  viewOnly,
  breadcrumbs,
  title,
  button
}: any) {
  const { updateBuilderStatus, survey = {} } = useCreateSurveyContext();
  const [isSaving, setIsSaving] = useState(false);
  const filesState = useXHR();
  const tableMeta = useTable({});
  
  const getFiles = async (params?: any) => {
    try {
      filesState.setLoading(true);
      const fileResponse = await getAllUserFilesFact({
        ...tableMeta.getParams(),
        ...(params || {}),
      });
      filesState.onSuccess(fileResponse.data || []);
      tableMeta.updatePagination({ totalPages: fileResponse.totalPages });
      return filesState;
    } catch (err: any) {
      filesState.onError(err);
    }
  };

  const onSearch = (search: any) => {
    tableMeta.setSearch(search);
    getFiles({ search });
  };

  const onSelect = (item: any) => {
    tableMeta.onSelect(item, "id");
    setIsSaving(true);
    if (checkSelected(item)) {
      removeParticipantFact({
        fileId: item.id,
        indexId: survey.data?.id,
      }).then(
        () => {
          // debugger;
          survey.setData({
            ...survey.data,
            users: survey.data?.users.filter((file: any) => file !== item.id),
            updatedAt: util.date.getFormatedTime(
              new Date(),
              " MMMM dd, yyyy, HH:mm"
            ),
          });
          setIsSaving(false);
        },
        () => {
          tableMeta.onSelect(item, "id");
          setIsSaving(false);
        }
      );
    } else {
      assosiateParticipantFact({
        fileId: item.id,
        indexId: survey.data?.id,
      }).then(
        () => {
          survey.setData({
            ...survey.data,
            users: [...(survey.data?.users || []), item.id],
            updatedAt: util.date.getFormatedTime(
              new Date(),
              " MMMM dd, yyyy, HH:mm"
            ),
          });
          setIsSaving(false);
        },
        () => {
          tableMeta.onSelect(item, "id");
          setIsSaving(false);
        }
      );
    }
  };

  const checkSelected = (item: any) => {
    return survey.data?.users.includes(item.id);
  };

  // const onCheckAll = (checked: boolean) => {
  //   tableMeta.onSelectAll(filesState.data, checked, "id");
  // };

  const onPageSelect = async (page: number) => {
    getFiles({ page }).then(() => {
      tableMeta.onPageSelect(page);
    });
    // debugger
    // try {
    //   const response = await getAllUserFilesFact({
    //     page,
    //     search: "" || undefined,
    //     page_size: 10
    //   });
    //   setUserData(response.data);
    //   tableMeta.onPageSelect(page);
    // } catch (e) { }
  };

  // const getCheckAllTemplate = () => (
  //   <FormGroup className="pb-1">
  //     <FormControl.Checkbox>
  //       <FormControl.Checkbox.Input
  //         checked={tableMeta.selectAll}
  //         onChange={(evt: any) => onCheckAll(evt.target.checked)}
  //       />
  //     </FormControl.Checkbox>
  //   </FormGroup>
  // );

  const getColumnsData = () => {
    return [
      {
        key: 1,
        renderItem: "",
        hasSort: false,
      },
      {
        key: 3,
        label: "Title",
        hasSort: true,
      },
      { key: 4, label: "Total Records", hasSort: false },
      {
        key: 6,
        label: "Uploaded On",
        hasSort: false,
      },
      { key: 5, label: "Tags", hasSort: false },
      // {
      //   key: 7,
      //   label: "Last Updated On",
      //   hasSort: false,
      // },
    ];
  };

  const getRowsTemplate = () => {
    return filesState.data.map((item: any, index: number) => {
      const checked = checkSelected(item);
      const isEven = index % 2 === 0 || index === 0;
      return (
        <Table.Row even={!isEven} key={item.empId} selected={checked}>
          <Table.Data width="30px">
            <FormGroup>
              <FormControl.Checkbox>
                <FormControl.Checkbox.Input
                  onChange={() => onSelect(item)}
                  onClick={(evt: any) => evt.stopPropagation()}
                  checked={checked}
                />
              </FormControl.Checkbox>
            </FormGroup>
          </Table.Data>
          <Table.Data width="40%">
            <Text.P1>
              {item.title || <i className="text-muted">No Name</i>}
            </Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.records}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>
              {util.date.getBrowserTime(
                item.uploadedOn,
                "MMMM dd, yyyy, HH:mm"
              )}
            </Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>
              {item.tags}
              {/* {item.tags.length > 0 ? (
                item.tags.join(", ")
              ) : (
                <span className="text-muted">-</span>
              )} */}
            </Text.P1>
          </Table.Data>
        </Table.Row>
      );
    });
  };

  React.useEffect(() => {
    !viewOnly && updateBuilderStatus?.(util.enums.SurveyStatus.AddPairings);
    getFiles();
  }, []);

  return (
    <Div>
      <Header
        metaItem={
          <>
            <Text.P1 className="px-2 pt-2">
              {isSaving ? (
                <>
                  <Icon spin icon="fal fa-spinner-third" /> Saving
                </>
              ) : (
                survey.data?.updatedAt
              )}
            </Text.P1>
            {button}
          </>
        }
        title={
          <Text.H1 className="pb-2">{title ?? "Add Participants"}</Text.H1>
        }
        breadcrumbs={breadcrumbs}
      />
      <Layout.Container className="pt-3" fluid>
        <Div>
          {survey.data?.type === "SurveyIndexSelf" ? (
            auxiliary ? (
              <Text.P1>
                <>
                  All the reviewees in the "{auxiliary}" are participants for
                  this self evaluation survey. Choose from existing lists of
                  employees to add more participants who can take the self
                  evaluation.
                </>
              </Text.P1>
            ) : (
              <Text.P1>
                Choose a list of employees from the lists below to add
                participants for this self evaluation survey.
              </Text.P1>
            )
          ) : (
            ""
          )}
          {survey.data?.type === "SurveyIndexEngagement" && (
            <>
              <Text.P1 className="d-inline">
                Below are the uploaded employee records we found. Choose one
                from the list to send out surveys to the users or
              </Text.P1>
              <Button className="px-1 fs-14 fw-400" variant="link">
                {" "}
                upload a new list.
              </Button>
            </>
          )}
        </Div>
        <Layout.Row className="my-3">
          <Layout.Col>
            <FormControl.Search
              value={tableMeta.search}
              onChange={(e: any) => {
                tableMeta.setSearch(e.target.value);
              }}
              onSearch={onSearch}
              placeholder="Search for tags, titles.."
            />
          </Layout.Col>
          <Layout.Col></Layout.Col>
        </Layout.Row>
        <Table
          columns={getColumnsData()}
          isLoading={filesState.isLoading}
          rows={filesState.data}
          customRows
          render={() => getRowsTemplate()}
          hasPagination
          activePage={tableMeta.page}
          pages={tableMeta.totalPages}
          onPageSelect={onPageSelect}
        />
        {/* {!filesState.isLoading && filesState?.data.length === 0 && <Empty />} */}
      </Layout.Container>
    </Div>
  );
}
