{"name": "web", "version": "0.1.0", "type": "module", "private": true, "scripts": {"dev": "next dev --turbopack --port 3000", "build": "next build", "start": "next start", "lint": "next lint --max-warnings 0", "check-types": "tsc --noEmit"}, "dependencies": {"@repo/ui": "workspace:*", "@tailwindcss/postcss": "^4.1.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.488.0", "next": "^15.2.4", "postcss": "^8.5.3", "react": "^19.1.0", "react-dom": "^19.1.0", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.2.5"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/node": "^22.14.0", "@types/react": "19.1.0", "@types/react-dom": "19.1.1", "autoprefixer": "^10.4.21", "eslint": "^9.24.0", "tailwindcss": "3", "typescript": "5.8.2"}}