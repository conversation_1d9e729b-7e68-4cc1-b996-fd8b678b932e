import React, { useEffect } from "react";
import { getAllPairFromFileFact } from "pages/AdminApp/DataLoad/dataload-api";
import { Text, Table, Layout, Icon, Button } from "unmatched/components";
import Header from "../../Header";
import { useTable } from "unmatched/hooks";
import { disableInputs } from "./AllPairings";
import util from "unmatched/utils";


interface Pairing {
  // key: string;
  count_items: number;
  count_pages: number;
  results: Array<Pairs>;
}
interface Pairs {
  rater: PairInfo;
  target: PairInfo;
  file: string;
}
interface PairInfo {
  emp_id: string;
  first_name: string;
  last_name: string;
  email: string;
  is_active: boolean;
  metadata: any;
  is_tenant_admin: boolean;
}

export default function PairingUsers(props: any) {
  const { viewPair, onBack, viewOnly } = props;
  const [pairings, setPairings] = React.useState<Pairing>({
    count_items: 0,
    count_pages: 0,
    results: [],
  });
  const [filters, setFilters] = React.useState({
    search: "",
    page: 1,
    totalPages: 0,
    size: 10,
  });
  const tableMeta = useTable({});
  const [isLoading, setIsLoading] = React.useState(true);

  useEffect(() => {
    if (viewOnly) {
      disableInputs();
      setTimeout(() => {
        enableRequiredInputs();
      }, 100)
    }
  });

  const enableRequiredInputs = () => {
    const elmsToEnable = document.getElementsByClassName('always-enabled');
    util.enableDisableTree(elmsToEnable, false);
  }

  React.useEffect(() => {
    // getUsers(viewPair.key, filters);

    async function call() {
      await getPairings();
    }
    call();
  }, []);
  const getPairings = async (page?: number) => {
    // setPairings(JSON_DATA);
    setPairings({ ...pairings, results: [] });
    try {
      const response = await getAllPairFromFileFact({
        file_id: viewPair.key,
        page: page ? page : 1,
        page_size: filters.size,
      });
      setPairings(response.data);
      tableMeta.updatePagination({ totalPages: response.data?.count_pages });
      setIsLoading(false);
    } catch (err) {
      console.log(err);
      setIsLoading(false);
    }
  };
  // const getUsers = (fileId: string, _filters: any) => {
  //   setUsers(JSON_DATA);
  //   // setLoading(false);
  //   // setLoading(true);
  //   // getAllUserFilesFact(fileId, _filters).then(() => {
  //   //   // const { totalPages } = response;
  //   //   // setFilters({
  //   //   //   ..._filters,
  //   //   //   totalPages,
  //   //   // });

  //   // }, (err: any) => {
  //   //   setLoading(false);
  //   // });
  // };

  const getColumns = () => {
    return [
      { key: 1, label: "No.", hasSort: false },
      { key: 3, label: "Rater First Name", hasSort: true },
      { key: 4, label: "Rater Last Name", hasSort: true },
      { key: 5, label: "Rater Employee ID", hasSort: true },
      { key: 6, label: "Rater Email", hasSort: true },
      { key: 7, label: "Target First Name", hasSort: true },
      { key: 8, label: "Target Last Name", hasSort: true },
      { key: 9, label: "Target Employee ID", hasSort: true },
      { key: 10, label: "Target Email", hasSort: true },
    ];
  };

  const getRows = () => {
    return pairings.results.map((item: any, index: number) => {
      // const checked = checkSelected(item);
      const isEven = index % 2 === 0 || index === 0;
      return (
        <Table.Row
          even={!isEven}
          key={item.key}
          // selected={checked}
        >
          <Table.Data width="30px">
            <Text.P1>{filters.page * 10 - 10 + index + 1}.</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.rater.first_name}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.rater.last_name}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.rater.emp_id}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.rater.email}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.target.first_name}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.target.last_name}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.target.emp_id}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.target.email}</Text.P1>
          </Table.Data>
        </Table.Row>
      );
    });
  };

  const onPageSelect = (page: number) => {
    setFilters({
      ...filters,
      page,
    });
    setIsLoading(true);
    getPairings(page);
    // getUsers(viewPair.key, {
    //   ...filters,
    //   page,
    // });
  };

  return (
    <>
      <Header
        metaItem={<></>}
        title={
          <>
            <Button
              className="mb-3 pointer pl-1 always-enabled"
              variant="link"
              onClick={() => onBack()}
            >
              <Icon icon="far fa-chevron-left mr-2" /> Employees / Pairings
            </Button>
            <Text.H2 className="text-primary">
              {viewPair.name ? (
                viewPair.name
              ) : (
                <span className="text-muted">No Name</span>
              )}
            </Text.H2>
          </>
        }
        breadcrumbs={props.breadcrumbs}
      />
      <Layout.Container className="pt-5" fluid>
        <Table
          columns={getColumns()}
          isLoading={isLoading}
          rows={pairings.results}
          customRows
          render={() => getRows()}
          hasPagination
          activePage={filters.page}
          pages={tableMeta.totalPages}
          onPageSelect={onPageSelect}
        />
      </Layout.Container>
    </>
  );
}
