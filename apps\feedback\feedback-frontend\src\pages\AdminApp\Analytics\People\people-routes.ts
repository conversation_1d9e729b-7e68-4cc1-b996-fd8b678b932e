import appUrls from "unmatched/utils/urls/app-urls";
import PeopleAnalytics from "./PeopleAnalytics/PeopleAnalytics";
import PeopleList from "./PeopleList";
// import SurveyList from "./SurveyList/SurveyList";

const routes = [
  {
    name: "People Analytics",
    path: appUrls.admin.analytics.people.getAnalyticsUrl(":id"),
    isExact: false,
    component: PeopleAnalytics,
  },
  {
    name: "People Analytics",
    path: appUrls.admin.analytics.people.list,
    isExact: false,
    component: PeopleList,
  },
  // {
  //   name: "Survey List - People Analytics",
  //   path: appUrls.admin.analytics.people.list,
  //   isExact: true,
  //   component: SurveyList,
  // },
];

export default routes;
