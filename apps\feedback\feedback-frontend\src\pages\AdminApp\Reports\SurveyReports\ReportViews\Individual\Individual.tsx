import React, { useState } from "react";
import { useDebounce, useTable } from "unmatched/hooks";
import Header from "../../Header";
import ViewReport from "./ViewReport";
import EmailReport from "./EmailReports";
import {
  PageContainer,
  Text,
  Button,
  Icon,
  Layout,
  FormControl,
  FormGroup,
  Table,
  Div,
  ComboFilter,
  Dropdown,
} from "unmatched/components";
import ReportsNoDataFound from "../../../ReportsNoDataFound";
import {
  get360ReportsByIdFact,
  getUpwardReportsByIdFact,
  getUpwardZippedReport,
  requestReportDownload,
  sendEmailReports,
} from "../../../reports-api";
import util from "unmatched/utils";
import { keys, map, get } from "lodash";
import iconsSvgs from "assets/icons/icons";
import appUrls from "unmatched/utils/urls/app-urls";
import DownloadInfoModal from "./DownloadInfoModal";
import DownloadReports from "./DownloadReports";
import useToastr from "unmatched/modules/toastr/hook";
import { useLocalFilter } from "pages/CommonFilters/useLocalFilters";

const { Page } = iconsSvgs;

interface User {
  key: string;
  firstName: string;
  lastName: string;
  id: string;
  email: string;
  updatedAt: string;
  target_details: any;
}

const icons = {
  DOWNLOAD: "fal fa-file-download",
  EMAIL: "fal fa-paper-plane",
  CHECKED: "fal fa-check-circle",
  COG: "fas fa-cog",
};

// const COHARTS = [
//   { key: "department", title: "Department" },
//   { key: "location", title: "Location" },
// ];

// const getUsersFact = () =>
//   new Promise((resolve: Function) => {
//     window.setTimeout(() => {
//       resolve();
//     }, 400);
//   });

const Individual = (props: any) => {
  const { survey, layout, surveyId, generateReport, loaders } = props;
  const tableMeta = useTable({
    size: 15
  });
  const toast = useToastr();
  // const [filters, setFilters] = React.useState({
  //   showUnflagged: false,
  //   cohart: {
  //     options: COHARTS,
  //     selected: "department",
  //   },
  //   applied: {
  //     options: [],
  //     selected: "Option1",
  //   },
  // });
  // const { showToast } = useToastr();
  const [isLoading, setLoading] = React.useState(false);
  const [users, setUsers] = React.useState<Array<User>>([]);
  const [modals, setModals] = React.useState({
    user: {},
    download: false,
    view: false,
    email: false,
    downloadFiltered: false,
  });
  const [totalReports, setTotalReports] = React.useState(0);
  const [ordering, setOrdering] = React.useState<any>();
  const filtersState = useLocalFilter(surveyId, undefined, 'report');

  React.useEffect(() => {
    filtersState.getFilters();
  }, []);

  React.useEffect(() => {
    getUsers();
    //eslint-disable-next-line
  }, []);

  const onReportSend = async (data?: any) => {
    try {
      await sendEmailReports({ index: surveyId, ...data });
      // onHide();
      toast.onSucces("Emails sent successfully.");
      setModals((_modal: any) => ({ ..._modal, email: false, user: {} }));
    } catch (error) {
      toast.onError("There was an error while trying to send mails.");
    }
  };
  const checkSelected = (item: User | any) => {
    return tableMeta.isSelected(item.id);
  };

  const onSearch = (search: string) => {
    getUsers(null, {
      search,
    });
  };

  const onSelectUser = (item: User) => {
    tableMeta.onSelect(item, "id");
  };

  const onCheckAll = (checked: boolean) => {
    //  const _u = [...users]
    // console.log(getTempUsers());
    tableMeta.onSelectAll(users, checked, "id");
    // setUsers([])
    // debugger;
    // console.log(tableMeta)
  };

  
  // const onDownload = () => "";

  // const onEmail = () => "";

  const downloadZippedUpwardReport = async (_filters?: any, params?: any) => {
    try {
      await getUpwardZippedReport({
        index_id: surveyId,
        ...(params || {}),
      });
    } catch (err) {
      console.log(err);
    }
  };

  const getUsers = async (_filters?: any, params?: any) => {
    // console.log(filtersState.selected);
    try {
      setLoading(true);
      const queryParams = {
        ...(ordering && { ordering }),
        ...(params || {}),
        ...filtersState.getParams(_filters || filtersState.selected),
      };
      const repRes =
        props.type === "360"
          ? await get360ReportsByIdFact({
              group_id: surveyId,
              ...queryParams,
              page_size: tableMeta.size,
            })
          : await getUpwardReportsByIdFact({
              index_id: surveyId,
              ...queryParams,
              page_size: tableMeta.size,
            });
      setUsers(repRes.data);
      // const { search, ...nParams } = params || {};
      const nParams = { ...params };
      delete nParams.search;
      setTotalReports(repRes.totalElements);
      tableMeta.setPagination({
        page: 1,
        totalPages: repRes.totalPages,
        size: tableMeta.size,
        totalItems: repRes.totalElements,
        ...nParams,
        ...filtersState.getParams(_filters || filtersState.selected),
      });
      setLoading(false);
    } catch (err) {
      console.log(err);
      setLoading(false);
    }
  };

  // Header Templates
  const getHeaderMetaTemplate = () => {
    return (
      <>
        <Dropdown>
          <Dropdown.Toggle
            variant="outline-primary"
            className="mr-2"
            id="dropdown-basic"
          >
            <Icon icon={icons.COG} />
          </Dropdown.Toggle>

          <Dropdown.Menu
            className="mt-3 text-left fs-12 shadow-sm"
            align="right"
          >
            <Dropdown.Item className="px-3" onClick={generateReport}>
              <Icon icon="far fa-sync-alt mr-2" />
              Regenerate
            </Dropdown.Item>
            {/* <Dropdown.Item href="#refresh">
              Refresh
              <Icon icon="far fa-redo ml-2" />
            </Dropdown.Item> */}
            {/* <Dropdown.Item href="#copy">
              Copy
              <Icon icon="far fa-copy ml-2" />
            </Dropdown.Item> */}
            {/* <Dropdown.Item
              href="#download"
              onClick={() =>
                downloadZippedUpwardReport(
                  {},
                  {
                    f: "zip",
                  }
                )
              }
            >
              Download
              <Icon icon="far fa-download ml-2" />
            </Dropdown.Item> */}
            {/* <Dropdown.Item href="#delete" className="text-danger">
              Delete
              <Icon icon="far fa-trash ml-2" />
            </Dropdown.Item> */}
          </Dropdown.Menu>
        </Dropdown>
        <Button
          variant="primary"
          onClick={(e: any) => {
            e.stopPropagation();
            // onDownload(tableMeta.selected);
            const data = {
              index: survey.id,
              parameters: {
                ...filtersState.selected,
              },
            };
            requestReportDownload(data, () => {
              setModals((_modals: any) => ({
                ..._modals,
                downloadFiltered: true,
              }));
            });
          }}
          className="mr-2"
        >
          <Icon icon={icons.DOWNLOAD} className="mr-2" />
          Download
        </Button>
        <Button
          variant="primary"
          onClick={(e: any) => {
            e.stopPropagation();
            setModals((_modals: any) => ({ ..._modals, email: true }));
          }}
          className="mr-2"
        >
          <Icon icon={icons.EMAIL} className="mr-2" />
          Email Reports
        </Button>
        {/* <Button
          onClick={() => {
            // onDownload(tableMeta.selected);
            setModals((_modals: any) => ({ ..._modals, email: true }));
          }}
          variant="primary"
        >
          <Icon icon={icons.EMAIL} className="mr-1" />
          Email Reports
        </Button> */}
      </>
    );
  };

  // TableColumn templates

  const getCheckAllTemplate = () => (
    <FormGroup className="pb-1">
      <FormControl.Checkbox>
        <FormControl.Checkbox.Input
          checked={tableMeta.selectAll}
          onChange={(evt: any) => onCheckAll(evt.target.checked)}
          // onChange={(e: any) =>
          //   tableMeta.onSelectAll(users, e.target.checked, "id")
          // console.log(_u)
          // }
        />
      </FormControl.Checkbox>
    </FormGroup>
  );

  // const getFlagTemplate = (flagged: boolean) => {
  //   const className = flagged ? "text-primary" : "text-muted";
  //   return <Icon icon={icons.CHECKED} className={className} />;
  // };

  const getColumnsData = () => {
    return [
      {
        key: 1,
        renderItem: getCheckAllTemplate,
        hasSort: false,
      },
      { key: 2, label: "No.", hasSort: false },
      // {
      //   key: 3,
      //   renderItem: () => {
      //     return getFlagTemplate(false);
      //   },
      //   hasSort: false,
      // },
      {
        key: 4,
        label: "Name",
        hasSort: true,
        sortValue: "asc",
        sortKey: "first_name",
      },
      {
        key: 5,
        label: "Email",
        hasSort: true,
        sortValue: "asc",
        sortKey: "email",
      },
      {
        key: 6,
        label: "Updated At",
        hasSort: true,
        sortValue: "dsc",
        sortKey: "updated_at",
      },
      { key: 9, label: "Actions", hasSort: false },
    ];
  };

  const [columnsData, setColumnsData] = useState<any>(getColumnsData());

  useDebounce(
    () => {
      setColumnsData(getColumnsData());
    },
    200,
    [users, tableMeta.selected]
  );

  const getColumns = () => {
    let columnsList = keys(columnsData);
    columnsList = map(columnsList, (key: string) => ({
      ...get(columnsData, key),
      key,
    }));
    return columnsList;
  };

  // Table Row Templates

  const getRowsTemplate = () => {
    return users.map((item: User, index: number) => {
      const checked = checkSelected(item);
      const isEven = index % 2 === 0 || index === 0;
      return (
        <Table.Row
          even={!isEven}
          key={item.key}
          selected={checked}
          onClick={() => {
            // setModals((_modals) => ({
            //   ..._modals,
            //   view: true,
            //   user: item,
            // }));
            setModals((_modals: any) => ({
              ..._modals,
              download: true,
              user: item,
            }));
          }}
        >
          <Table.Data width="30px">
            <FormGroup>
              <FormControl.Checkbox>
                <FormControl.Checkbox.Input
                  onChange={() => onSelectUser(item)}
                  onClick={(evt: any) => evt.stopPropagation()}
                  checked={checked}
                />
              </FormControl.Checkbox>
            </FormGroup>
          </Table.Data>
          <Table.Data width="60px">
            <Text.P1>{tableMeta.getSNo(index)}.</Text.P1>
          </Table.Data>
          {/* <Table.Data width="30px">
            <Text.P1>{getFlagTemplate(false)}</Text.P1>
          </Table.Data> */}
          <Table.Data>
            <Text.P1>
              {item.firstName} {item.lastName}
            </Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.email}</Text.P1>
          </Table.Data>
          <Table.Data width="190px">
            <Text.P1>
              {util.date.getBrowserTime(item.updatedAt, "MMM dd, yyyy, HH:mm")}
            </Text.P1>
          </Table.Data>
          <Table.Data width="100px">
            <Button
              className="py-1 text-muted"
              preventDefault
              stopPropagation
              onClick={() => {
                setModals((_modals: any) => ({
                  ..._modals,
                  download: true,
                  user: item,
                }));
              }}
              variant="link"
            >
              <Icon icon={icons.DOWNLOAD} />
            </Button>
            <Button
              className="py-1 text-muted"
              preventDefault
              stopPropagation
              variant="link"
              onClick={() => {
                setModals((_modals: any) => ({
                  ..._modals,
                  email: true,
                  user: item,
                }));
              }}
            >
              <Icon icon={icons.EMAIL} />
            </Button>
          </Table.Data>
        </Table.Row>
      );
    });
  };
  const isFilterApplied = Object.keys(filtersState.selected).length > 0;
  if (
    tableMeta.search.length === 0 &&
    !isFilterApplied &&
    users.length === 0 &&
    !isLoading
  ) {
    return (
      <ReportsNoDataFound generateReport={generateReport} loaders={loaders} />
    );
  }

  return (
    <PageContainer>
      <Header
        survey={survey}
        layout={layout}
        metaTemplate={getHeaderMetaTemplate()}
        breadcrumbs={[
          {
            label: "Reports",
            icon: <Page className="grey-icon__svg" />,
            route: appUrls.admin.reports.default,
          },
          { label: "Individual Reports" },
          { label: survey.name },
        ]}
      />
      {/* tableMeta.selected.length && */}
      <Layout.Container className="pt-4" fluid>
        {/* {JSON.stringify(getTempUsers())} */}
        <Layout.Row>
          <Layout.Col xl={4}>
            <ComboFilter
              filters={filtersState.filters}
              selected={filtersState.selected}
              onFilterSelect={(_selected: any) => {
                filtersState.onSelect(_selected);
                getUsers(_selected, { search: tableMeta.search });
              }}
              onSubmit={() => ""}
            />
          </Layout.Col>
          <Layout.Col className="pt-2 fs-12">
            {tableMeta.selected.length >= 1 &&
              `${tableMeta.selected.length} Participant Selected`}
            {/* {!tableMeta.selected.length && (
              <FormControl.Checkbox>
                <FormControl.Checkbox.Label>
                  Show Unflagged only
                </FormControl.Checkbox.Label>
                <FormControl.Checkbox.Input
                  checked={filters.showUnflagged}
                  onChange={(evt: any) => {
                    const showUnflagged = evt.target.checked;
                    setFilters(() => ({
                      ...filters,
                      showUnflagged,
                    }));
                  }}
                />
              </FormControl.Checkbox>
            )} */}
          </Layout.Col>
          <Layout.Col className="text-right">
            <FormControl.Search
              value={tableMeta.search}
              onChange={(e: any) => {
                tableMeta.setSearch(e.target.value);
                if (!e.target.value) {
                  onSearch("");
                }
              }}
              onSearch={(value: string) => {
                if (!value) return;
                onSearch(value);
              }}
              placeholder="Search for employee , ID.."
            />
          </Layout.Col>
        </Layout.Row>
        <Div className="pt-4">
          <Table
            columns={getColumns()}
            isLoading={isLoading}
            rows={users}
            customRows
            render={() => getRowsTemplate()}
            hasPagination
            activePage={tableMeta.page}
            pages={tableMeta.totalPages}
            onPageSelect={(number: number) => {
              tableMeta.onPageSelect(number);
              getUsers(null, { page: number });
            }}
            onSort={(item: any) => {
              const label = util.label.getSortingLabel(
                item.sortKey,
                item.sortValue
              );
              setColumnsData((_columns: any) => {
                return tableMeta.resetColumns(_columns, item);
              });
              setOrdering(label);
              getUsers(null, { ordering: label, search: tableMeta.search });
            }}
            {...(tableMeta.search && {
              notFoundMsg: util.noSearchRecordsFoundMsg,
            })}
            size={tableMeta.size}
            totalItems={tableMeta.totalItems}
          />
        </Div>
      </Layout.Container>
      <ViewReport
        show={modals.view}
        user={modals.user}
        onHide={() =>
          setModals((_modal) => ({ ..._modal, view: false, user: {} }))
        }
        onView={() => ""}
        // onDownload={onDownload}
        // onEmail={onEmail}
      />
      <DownloadInfoModal
        show={modals.downloadFiltered}
        user={modals.user}
        users={users}
        selected={tableMeta.selected}
        onHide={() =>
          setModals((_modal: any) => ({
            ..._modal,
            downloadFiltered: false,
            user: {},
          }))
        }
        total={totalReports}
        // onDownload={onDownload}
        downloadBulkZip={downloadZippedUpwardReport}
        selectedFilters={filtersState.selected}
        onTabSelect={props.onTabSelect}
      />
      <DownloadReports
        show={modals.download}
        user={modals.user}
        users={users}
        selected={tableMeta.selected}
        onHide={() =>
          setModals((_modal: any) => ({ ..._modal, download: false, user: {} }))
        }
        total={totalReports}
        // onDownload={onDownload}
        downloadBulkZip={downloadZippedUpwardReport}
        selectedFilters={filtersState.selected}
      />
      <EmailReport
        show={modals.email}
        user={modals.user}
        users={users}
        selected={tableMeta.selected}
        onHide={() =>
          setModals((_modal: any) => ({ ..._modal, email: false, user: {} }))
        }
        onReportSend={onReportSend}
        total={totalReports}
        selectedFilters={filtersState.selected}
      />
    </PageContainer>
  );
};

export default Individual;
