import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router';
import { Button } from '@repo/ui/components/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@repo/ui/components/card';
import { Badge } from '@repo/ui/components/badge';
import { Skeleton } from '@repo/ui/components/skeleton';
import { Alert, AlertDescription } from '@repo/ui/components/alert';
import { USER_ROUTES } from '@/app.routes';
import axiosInstance from '@/lib/axios';

interface User {
  id: number;
  first_name: string;
  last_name: string;
  emp_id: string;
  email: string;
  department: string;
  location: string;
  survey: string | number;
  response_id?: string;
  target: {
    first_name: string;
    last_name: string;
    emp_id: string;
  };
}

interface SurveyInfo {
  id: string;
  title: string;
  isPairsEditable: boolean;
}

const Pairings: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [users, setUsers] = useState<User[]>([]);
  const [surveyInfo, setSurveyInfo] = useState<SurveyInfo | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [status, setStatus] = useState('TODO');

  useEffect(() => {
    if (id) {
      fetchSurveyInfo();
      fetchUsers();
    }
  }, [id, status]);

  const fetchSurveyInfo = async () => {
    try {
      const response = await axiosInstance.get(`/survey/index/${id}/`);
      setSurveyInfo(response.data);
    } catch (err) {
      console.error('Error fetching survey info:', err);
      setError('Failed to load survey information');
    }
  };

  const fetchUsers = async () => {
    try {
      setIsLoading(true);
      const response = await axiosInstance.get('/survey/pairing/', {
        params: {
          index_id: id,
          status,
          page_size: 50,
          exclude_disabled: true
        }
      });
      setUsers(response.data.results || []);
      setError(null);
    } catch (err) {
      console.error('Error fetching users:', err);
      setError('Failed to load pairings');
    } finally {
      setIsLoading(false);
    }
  };

  const handleStartSurvey = async (user: User) => {
    try {
      if (user.response_id && user.survey) {
        // Navigate directly if response already exists
        navigate(USER_ROUTES().dashboard.upwardReview.getTakeSurveyUrl(user.response_id, user.survey.toString()));
      } else {
        // Create new response
        const response = await axiosInstance.post('/survey/survey-response/', {
          survey: user.survey,
          pairing: user.id
        });
        navigate(USER_ROUTES().dashboard.upwardReview.getTakeSurveyUrl(response.data.id, response.data.survey));
      }
    } catch (err) {
      console.error('Error starting survey:', err);
      setError('Failed to start survey');
    }
  };

  const getStatusBadge = (status: string) => {
    const statusMap = {
      TODO: { label: 'To Do', variant: 'secondary' as const },
      PROG: { label: 'In Progress', variant: 'default' as const },
      SUBM: { label: 'Completed', variant: 'default' as const },
      DECL: { label: 'Declined', variant: 'destructive' as const }
    };
    return statusMap[status as keyof typeof statusMap] || { label: status, variant: 'secondary' as const };
  };

  const statusTabs = [
    { key: 'TODO', label: 'To Do' },
    { key: 'PROG', label: 'In Progress' },
    { key: 'SUBM', label: 'Completed' },
    { key: 'DECL', label: 'Declined' }
  ];

  if (error) {
    return (
      <div className="p-6">
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="mb-6">
        <Button 
          variant="ghost" 
          onClick={() => navigate(USER_ROUTES().dashboard.surveyList)}
          className="mb-4"
        >
          ← Back to Surveys
        </Button>
        <h1 className="text-3xl font-bold">
          {surveyInfo?.title || 'Survey Pairings'}
        </h1>
      </div>

      {/* Status Tabs */}
      <div className="flex gap-2 mb-6">
        {statusTabs.map((tab) => (
          <Button
            key={tab.key}
            variant={status === tab.key ? 'default' : 'outline'}
            onClick={() => setStatus(tab.key)}
          >
            {tab.label}
          </Button>
        ))}
      </div>

      {/* Users List */}
      {isLoading ? (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {Array(6).fill(0).map((_, index) => (
            <Card key={index}>
              <CardHeader>
                <Skeleton className="h-6 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-2/3" />
              </CardContent>
            </Card>
          ))}
        </div>
      ) : users.length === 0 ? (
        <Card>
          <CardContent className="text-center py-8">
            <p className="text-gray-500">No pairings found for this status.</p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {users.map((user) => {
            const statusInfo = getStatusBadge(status);
            return (
              <Card key={user.id} className="hover:shadow-md transition-shadow">
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <CardTitle className="text-lg">
                      {user.target.first_name} {user.target.last_name}
                    </CardTitle>
                    <Badge variant={statusInfo.variant}>
                      {statusInfo.label}
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-600">ID: {user.target.emp_id}</p>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2 text-sm">
                    <p><span className="font-medium">Department:</span> {user.department}</p>
                    <p><span className="font-medium">Location:</span> {user.location}</p>
                  </div>
                  
                  {status === 'TODO' && (
                    <Button 
                      className="w-full mt-4"
                      onClick={() => handleStartSurvey(user)}
                    >
                      Start Survey
                    </Button>
                  )}
                  
                  {status === 'PROG' && (
                    <Button 
                      className="w-full mt-4"
                      variant="outline"
                      onClick={() => handleStartSurvey(user)}
                    >
                      Continue Survey
                    </Button>
                  )}
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default Pairings;
