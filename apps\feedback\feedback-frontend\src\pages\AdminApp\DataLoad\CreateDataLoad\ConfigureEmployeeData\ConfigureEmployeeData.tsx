import React from "react";
import { CustomModal as Modal } from "unmatched/components";
import AddEmployeeScreen1 from "./RequiredFields";
import AddEmployeeScreen2 from "./UploadFile";
import AddEmployeeScreen3 from "./PublishFile";
import { getRequiredUserFieldFact } from "../../dataload-api";

export default function ConfigureEmployeeData(props: any) {
  // let screen: number = props.screen;
  const [isLoading, setIsLoading] = React.useState(true);
  const [requiredFields, setRequiredFields] = React.useState([]);
  const [response, setResponse] = React.useState({
    uploadCount: 0,
    userAdded: 0,
    userUpdated: 0,
  });

  React.useEffect(() => {
    async function getRequiredFields() {
      const response = await getRequiredUserFieldFact();
      const data = await response.data.fields;
      setRequiredFields(data);
      setIsLoading(false);
    }
    getRequiredFields();
  }, []);
  const [screen, setScreen] = React.useState<number>(1);
  const onClose = () => {
    setScreen(1);
    setResponse({
      uploadCount: 0,
      userAdded: 0,
      userUpdated: 0,
    });
    props.onHide();
  };
  const ScreenView = (inPosition: number, props: any) => {
    switch (screen) {
      case 1:
        return (
          <AddEmployeeScreen1
            {...props}
            setScreen={setScreen}
            data={requiredFields}
            loading={isLoading}
          />
        );
      case 2:
        return (
          <AddEmployeeScreen2
            {...props}
            setScreen={setScreen}
            res={response}
            setRes={setResponse}
          />
        );
      case 3:
        return (
          <AddEmployeeScreen3
            {...props}
            setScreen={setScreen}
            res={response}
            onClose={onClose}
          />
        );
      default:
        return "";
    }
  };
  return (
    <Modal
      {...props}
      onHide={onClose}
      children={
        <>
          <Modal.Header closeButton>
            <Modal.Title>Add Employees</Modal.Title>
          </Modal.Header>
          {ScreenView(screen, props)}
        </>
      }
    />
  );
}
