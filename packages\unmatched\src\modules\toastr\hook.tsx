import { useDispatch, useSelector } from "../../hooks";
// import { ErrorType } from "unmatched/types";
import actions from "./slice";

export default function useToastr() {
  const toastrs: Array<any> = useSelector((state: any) => state.toastr.toastrs);
  const dispatch = useDispatch();

  const showToast = (config: any) => {
    const hasMessage = toastrs.find(
      (item) => item.title === config.title && item.content === config.content
    );
    if (!hasMessage) {
      dispatch(actions.add(config));
    }
  };

  const errorToast = (content: any = "Something went wrong.") => {
    if (typeof content !== "string") {
      content = "Something went wrong.";
    }
    showToast({
      show: true,
      title: "Error",
      variant: "danger",
      content,
    });
  };

  const warningToast = (content: any = "Something went wrong.") => {
    if (typeof content !== "string") {
      content = "Something went wrong.";
    }
    showToast({
      show: true,
      // title: "",
      variant: "warning",
      content,
    });
  };

  const onError = (err: any) => {
    let message = err.msg;
    if (message === "Canceled Request") return;
    // title: `Error ${err.statusCode}`,
    if (typeof message !== "string") {
      message = "Something went wrong.";
    }
    showToast({
      show: true,
      variant: "danger",
      content: message || "Something went wrong",
    });
  };

  const onSucces = (config: any) => {
    showToast({
      show: true,
      variant: "success",
      title: config.title || "Success",
      content: config.content || "",
    });
  };
  const resetToast = (index: number) => {
    dispatch(actions.remove(index));
  };

  return {
    toastrs,
    resetToast,
    showToast,
    onError,
    onSucces,
    errorToast,
    warningToast,
  };
}
