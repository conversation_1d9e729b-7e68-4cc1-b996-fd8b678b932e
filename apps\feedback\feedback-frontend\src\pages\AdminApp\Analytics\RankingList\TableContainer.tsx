import React from "react";
import { Button, Div, Icon, Text } from "unmatched/components";

const TableContainer = (props: any) => {
  return (
    <Div className="bg-light py-3 my-4 border">
      <Div className="p-3">
        <Div className="row">
          <Div className="col-8">
            <Text.H3 className="pb-3">{props.title}</Text.H3>
          </Div>
          <Div className="col-4 d-flex justify-content-end">
            {props.download ? (
              <Button variant="outline-primary" onClick={props.download}>
                Download XLSX <Icon icon="fas fa-file-download ml-2" />
              </Button>
            ) : null}
          </Div>
        </Div>

        {props.filters}
      </Div>
      {props.children}
    </Div>
  );
};

export default TableContainer;
