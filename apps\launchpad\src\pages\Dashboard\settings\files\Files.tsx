import { useState, useEffect } from "react";
import { useFiles } from "./useFiles";
import { useEffect as useDocumentEffect } from "react";
import { useDebounce } from "@/hooks";
import { useNavigate } from "react-router";
import ROUTES from "@/routes";
import { getSortIcon } from "./utils/sortingUtils.tsx";
import { downloadFileService, downloadErrorLogService, File } from "./filesService";
import {
  FileHeader,
  FileTable,
  FilePagination
} from "./components";

export const Files = () => {
  useDocumentEffect(() => {
    document.title = "Files - Unmatched";
  }, []);
  const {
    files,
    loadingFiles,
    pagination,
    sorting,
    searchQuery,
    handleSearch,
    handleSort,
    handlePaginationChange,
    handleDeleteFile
  } = useFiles();

  const [searchValue, setSearchValue] = useState(searchQuery);
  const debouncedSearchValue = useDebounce(searchValue, 500);

  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchValue(e.target.value);
  };

  // Effect to trigger search when debounced value changes
  useEffect(() => {
    if (debouncedSearchValue !== searchQuery) {
      handleSearch(debouncedSearchValue);
    }
  }, [debouncedSearchValue, handleSearch, searchQuery]);

  // Get sort icon for column
  const getColumnSortIcon = (field: keyof File | string) => {
    return getSortIcon(field, sorting.field, sorting.direction);
  };

  const navigate = useNavigate();

  const handleFileRowClick = (file: File) => {
    // Navigate to file details page
    navigate(ROUTES.SETTINGS.FILE_DETAILS_PATH(file.id));
  };

  const handleAddFile = () => {
    // Open file upload modal
    console.log("Open file upload modal");
    // This will be implemented in the next phase
  };

  const handleDownloadFile = (fileUrl: string, fileId?: string) => {
    downloadFileService(fileUrl, fileId);
  };

  const handleDownloadErrorLog = (errorLogUrl: string, fileId?: string) => {
    downloadErrorLogService(errorLogUrl, fileId);
  };

  return (
    <div className="space-y-4 p-4">
      <FileHeader
        searchValue={searchValue}
        onSearchChange={handleSearchInputChange}
        onAddFile={handleAddFile}
      />

      <FileTable
        loadingFiles={loadingFiles}
        files={files}
        handleSort={handleSort}
        getSortIcon={getColumnSortIcon}
        onRowClick={handleFileRowClick}
        onDeleteFile={handleDeleteFile}
        onDownloadFile={handleDownloadFile}
        onDownloadErrorLog={handleDownloadErrorLog}
      />

      <FilePagination
        pagination={pagination}
        handlePaginationChange={handlePaginationChange}
      />
    </div>
  );
}