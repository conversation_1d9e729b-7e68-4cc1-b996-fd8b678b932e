import axios from "axios";
import util from "unmatched/utils";

export const getSurveyPairsFact = (params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return axios.get(`${util.apiUrls.GET_UPWARD_PAIRS}`, config);
};

export const getSurveyAllUserFact = (params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return axios.get(`${util.apiUrls.GET_ALL_USERS_IN_SURVEY}`, config);
};

export const setAddUserToSurveyFact = (data: any, params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return axios.post(`${util.apiUrls.SET_ADD_PAIRING}`, data, config);
};

export const getSetSurveyUserResponseFact = (
  data: any,
  params?: any,
  meta?: any
) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return axios.post(`${util.apiUrls.GET_SET_RESPONSE}`, data, config);
};
export const getSetSurveyUserResponseActionFact = (
  data: any,
  responseID: string,
  type: string,
  params?: any,
  meta?: any
) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  const types: any = {
    DECL: "decline",
    TODO: "reinstate",
  };
  return axios.post(
    `${util.apiUrls.GET_SET_RESPONSE}${responseID}/${types[type]}/`,
    data,
    config
  );
};
