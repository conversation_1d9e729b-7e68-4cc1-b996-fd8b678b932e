// import React, { ReactNode } from "react";
// import { Text, Icon, Div } from "unmatched/components";
import { Text } from "@repo/ui/components/Text";
import { Loader2 } from "lucide-react";
import { ReactNode } from "react";

const Loader = (props: {
  hideContent?: boolean;
  title?: string;
  content?: string;
  icon?: string;
  iconTemplate?: ReactNode;
  image?: ReactNode;
}) => {
  const { iconTemplate, hideContent } = props;
  const getIcon = () => {
    return (
      iconTemplate || (
        // <Icon
        //   icon={props.icon}
        //   spin
        //   className="fs-spin my-3 mb-4 fs-60"
        //   variant="primary"
        // ></Icon>
        <Loader2 className="animate-spin mx-auto" />
      )
    );
  };
  return (
    <div style={{ padding: "15px 0px" }} className="text-center my-4">
      <div className="my-3 mb-4">{getIcon()}</div>
      {!hideContent && (
        <>
          <Text.H2 className="text-primary mb-4">{props.title}</Text.H2>
          <Text.P1>{props.content}</Text.P1>
        </>
      )}
    </div>
  );
};

// Loader.defaultProps = {
//   icon: "fal fa-circle-notch",
// };

export default Loader;
