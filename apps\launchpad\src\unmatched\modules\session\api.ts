import { AxiosResponse } from "axios";
import util from "../../utils";
import axiosInstance from "@/lib/axios";

// const { api, apiUrls } = util;
import api from "../../utils/api";
import apiUrls from "../../utils/urls/api-urls";

export const loginUserFact = (
  data: {
    email: string;
    password: string;
  },
  params?: any,
  meta?: any
) => {
  console.log(data);
  const config = api.getConfigurations(params, meta);
  return axiosInstance.post(`${apiUrls.LOGIN}`, data, config);
};

export const logoutUserFact = (data?: any, params?: any, meta?: any) => {
  const config = api.getConfigurations(params, meta);
  return axiosInstance.post(`${apiUrls.LOGOUT}`, data, config);
};

export const validateUserEmailFact = (
  params: {
    email: string;
  },
  meta?: any
) => {
  console.log(params);
  const config = api.getConfigurations(null, meta);
  console.log(config);
  return axiosInstance
    .get(`${apiUrls.VALIDATE_EMAIL(params.email)}`)
    .then((response: any) => ({
      isActive: response.data.is_active,
      isAdmin: response.data.is_admin,
      isPasswordSet: response.data.is_password_set,
      hasMagicLink: response.data.is_magiclink_enabled,
    }));
};

export const getSessionFact = (meta?: any) => {
  const config = api.getConfigurations(null, meta);
  return axiosInstance.get(`${apiUrls.SESSION}`, config).then((response: any) => {
    const { data } = response;
    return {
      id: data.emp_id,
      email: data.email,
      firstName: data.first_name,
      lastName: data.last_name,
      title: "",
      department: data.department,
      location: data.office_location,
      role: data.is_tenant_admin
        ? util.enums.Roles.ADMIN
        : util.enums.Roles.USER,
      isPasswordSet: data.is_password_set,
    };
  });
};

export const getClientInfoFact = (meta?: any) => {
  const config = api.getConfigurations(null, meta);
  return axiosInstance.get("/staff/customers/info/", config).then((response: any) => {
    const { data } = response;
    return {
      // id: data.id,
      name: data.name,
      homepage: data.homepage,
      homepagePath: data.homepage_path,
      lightLogo: data.logo_light || util.images.UNMATCHED_LOGO,
      darkLogo: data.logo_dark || util.images.UNMATCHED_DARK_LOGO,
      thumbnail: data.logo_thumbnail || util.images.UNMATCHED_THUMBNAIL,
      loader: data.loader || util.images.MainLoader,
      loginText: data.login_text || "",
      contactText: data.contact_verbiage || "",
      contacts: data.contact_list || [],
      sso: {
        enabled: data.is_sso_enabled,
        clientID: data.sso_client_id,
        issuer: data.sso_issuer
      },
      features: data.features
    };
  });
};

export const patchClientInfoFact = (data?: any, meta?: any) => {
  const config = api.getConfigurations(null, meta);
  return axiosInstance
    .patch("/staff/customers/info/", data, config)
    .then((response: any) => {
      const { data } = response;
      return {
        // id: data.id,
        name: data.name,
        lightLogo: data.logo_light || util.images.UNMATCHED_LOGO,
        darkLogo: data.logo_dark || util.images.UNMATCHED_DARK_LOGO,
        thumbnail: data.logo_thumbnail || util.images.UNMATCHED_THUMBNAIL,
        loader: data.loader || util.images.MainLoader,
        loginText: data.login_text || "",
        contactText: data.contact_verbiage || "",
        contacts: data.contact_list || [],
      };
    });
};

export const getUserFact = (
  params: {
    token: string;
  },
  meta?: any
) => {
  const config = api.getConfigurations(null, meta);
  return axiosInstance
    .get(`${apiUrls.SESSION}`, {
      ...config,
      headers: {
        authorization: `Token ${params.token}`,
      },
    })
    .then((response: any) => {
      const { data } = response;
      return {
        id: data.emp_id,
        email: data.email,
        firstName: data.first_name,
        lastName: data.last_name,
        title: "",
        department: data.department,
        location: data.office_location,
        role: data.is_tenant_admin
          ? util.enums.Roles.ADMIN
          : util.enums.Roles.USER,
        // isAdmin: true,
        // isAdmin: data.is_tenant_admin,
        isPasswordSet: data.is_password_set,
      };
    });
};

export const updateUserFact = (
  data: any,
  params: {
    token: string;
  },
  meta?: any
) => {
  const config = api.getConfigurations(null, meta);
  const payload = {
    first_name: data.firstName,
    last_name: data.lastName,
    office_location: data.location,
    // "current_level": data.firstName,
    // "practice_group": data.firstName,
    // "target_group": data.firstName,
    department: data.department,
    // "class_year": 0
  };
  return axiosInstance
    .put(`${apiUrls.SESSION}`, payload, {
      ...config,
      headers: {
        authorization: `Token ${params.token}`,
      },
    })
    .then((response: any) => {
      const { data } = response;
      return {
        id: data.emp_id,
        email: data.email,
        firstName: data.first_name,
        lastName: data.last_name,
        title: "",
        department: data.department,
        location: data.office_location,
      };
    });
};

export const getUserByIdFact = (userId: any, meta?: any) => {
  const config = api.getConfigurations(null, meta);
  return axiosInstance
    .get(`/auth/users/${userId}`, config)
    .then(({ data }: AxiosResponse) => {
      return {
        id: data.id,
        empId: data.emp_id,
        name: util.getFullName({
          firstName: data.first_name,
          lastName: data.last_name,
          email: data.email,
        }),
        email: data.email,
      };
    });
};

export const getUsersFact = (search: string) =>
  axiosInstance
    .get("/auth/users/", {
      params: {
        search,
        page_size: 12,
      },
    })
    .then((res: AxiosResponse) => {
      return res.data.results.map((item: any) => {
        return {
          id: item.id,
          empId: item.emp_id,
          name: util.getFullName({
            firstName: item.first_name,
            lastName: item.last_name,
            email: item.email,
          }),
          email: item.email,
        };
      });
    });
