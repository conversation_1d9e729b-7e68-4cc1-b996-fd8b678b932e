import React from "react";
import CustomHeader from "pages/AdminApp/Shared/CustomHeader/CustomHeader";
import { Button, Div, PageContainer } from "unmatched/components";
import CountsView from "./components/CountsView";
import styled from "styled-components";
import Notification from "./components/Notification";

const Content = styled(Div)`
  margin: 40px 15px 0 15px;
`;

export default function Dashboard() {
  return (
    <div style={{ minHeight: "100vh" }}>
      <Div>
        <CustomHeader title="Dashboard" />
      </Div>
      <PageContainer id="create-survey-container">
        <Content>
          <CountsView />
          <Notification
            title="Send Reminder Emails?"
            description="You have an ongoing survey titled ‘ Upward review - Xyz 2021’ and the participation rate is 23%. Would you like to send a reminder email to the participants?"
          >
            <Button onClick={() => null}>Send Reminder Emails</Button>
          </Notification>
          <Notification
            title="Reports are now generated!"
            description="You have an ongoing survey titled ‘ Upward review - Xyz 2021’ and the participation rate is 23%. Would you like to send a reminder email to the participants?"
          >
            <Button onClick={() => null}>Download Survey Reports</Button>
          </Notification>
        </Content>
      </PageContainer>
    </div>
  );
}
