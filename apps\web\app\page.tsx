"use client";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@repo/ui/components/accordion";
import { But<PERSON> } from "@repo/ui/components/button";
import { toast } from "sonner";

export default function Home() {
  return (
    <div>
      <Accordion type="single" collapsible>
        <AccordionItem value="item-1">
          <AccordionTrigger>Is it accessible?</AccordionTrigger>
          <AccordionContent>
            Yes. It adheres to the WAI-ARIA design pattern.
          </AccordionContent>
        </AccordionItem>
      </Accordion>
      <Button onClick={() => toast("Hello world!")}>Click me</Button>
      <h1 className="text-3xl font-bold underline">Hello world!</h1>
    </div>
  );
}
