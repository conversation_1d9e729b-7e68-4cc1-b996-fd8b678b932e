"use client"

import { useTheme } from "next-themes"
import { Toaster as Son<PERSON>, ToasterProps } from "sonner"
import "./sonner.css"

const Toaster = ({ ...props }: ToasterProps) => {
  const { theme = "system" } = useTheme()

  return (
    <Sonner
      theme={theme as ToasterProps["theme"]}
      className="toaster group"
      style={
        {
          // Light mode colors
          "--normal-bg": "hsl(0 0% 100%)",
          "--normal-text": "hsl(240 10% 3.9%)",
          "--normal-border": "hsl(240 5.9% 90%)",

          // Success toast colors
          "--success-bg": "hsl(143 85% 96%)",
          "--success-text": "hsl(140 100% 27%)",
          "--success-border": "hsl(145 92% 91%)",

          // Error toast colors
          "--error-bg": "hsl(359 100% 97%)",
          "--error-text": "hsl(358 75% 59%)",
          "--error-border": "hsl(359 100% 94%)",

          // Dark mode overrides (applied via CSS selector)
          ".dark &": {
            "--normal-bg": "hsl(240 10% 3.9%)",
            "--normal-text": "hsl(0 0% 98%)",
            "--normal-border": "hsl(240 3.7% 15.9%)",

            "--success-bg": "hsl(150 100% 6%)",
            "--success-text": "hsl(150 86% 65%)",
            "--success-border": "hsl(147 100% 12%)",

            "--error-bg": "hsl(358 76% 10%)",
            "--error-text": "hsl(358 100% 81%)",
            "--error-border": "hsl(357 89% 16%)",
          }
        } as React.CSSProperties
      }
      {...props}
    />
  )
}

export { Toaster }
