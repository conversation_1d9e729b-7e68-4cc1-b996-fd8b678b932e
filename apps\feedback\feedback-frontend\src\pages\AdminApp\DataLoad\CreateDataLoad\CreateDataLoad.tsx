import React from "react";
import {
  Text,
  Layout,
  Div,
  FormControl,
  Modal,
  <PERSON><PERSON>,
  Tab,
  Button,
} from "unmatched/components";
import data from "../../../../assets/images/images";
import CustomHeader from "../../Shared/CustomHeader/CustomHeader";
import AddEmployee from "./ConfigureEmployeeData/ConfigureEmployeeData";
import SelectDataLoad from "./SelectDataLoad";
import { useState, useQuery } from "unmatched/hooks";
import EmployeeDataList from "./EmployeeDataList/EmployeeDataList";
import PairingDataList from "./PairingDataList/PairingDataList";
import OngoingDataList from "./OngoingDataList/OngoingDataList";
import ConfigureEmployeeData from "./ConfigureEmployeeData/ConfigureEmployeeData";
import ConfigurePairingData from "./ConfigurePairingData/ConfigurePairingData";
import {
  getAllAssociateFileFact,
  getAllPairFileFact,
  getAllUserFilesFact,
} from "../dataload-api";
// import appUrls from "unmatched/utils/urls/app-urls";
// import iconsSvgs from "assets/icons/icons";

// const { Group } = iconsSvgs;

const SURVEY_TYPES = {
  EMPLOYEE: "EMPLOYEE",
  PAIRING: "PAIRING",
  ONGOING: "ONGOING",
};

const CreateDataLoad = () => {
  const query = useQuery();
  const [show, setShow] = React.useState(false);

  const [showAddEmployee, setShowAddEmployee] = React.useState<boolean>(false);
  const [showAddPair, setShowAddPair] = React.useState<boolean>(false);
  // setDroppedFiles
  // droppedFiles
  const [, setDroppedFiles] = React.useState([]);
  const handleFileDrop = React.useCallback((_, monitor) => {
    if (monitor) {
      const files = monitor.getItem().files;
      setDroppedFiles(files);
    }
  }, []);
  const [Screen, setScreen] = React.useState(1);
  // console.log(droppedFiles);

  // const [data] = React.useState([
  //   {
  //     key: "row1",
  //     id: 1,
  //     title: "Employee Database_June 2021",
  //     date: "01-29-2021",
  //     record: 90,
  //     tag: "Test Tag",
  //   },
  //   {
  //     key: "row2",
  //     id: 2,
  //     title: "Employee Database_June 20202",
  //     date: "01-29-2021",
  //     record: 90,
  //     tag: "Test Tag",
  //   },
  // ]);

  const [isLoading, setLoading] = React.useState(true);
  const [ordering, setOrdering] = React.useState("title");
  const [userData, setUserData] = React.useState<any>({ results: [] });
  const [pairData, setPairData] = useState<any>({ results: [] });
  const [associateData, setAssociateData] = useState<any>({ results: [] });
  const [tempSearch, setTempSearch] = useState<string>("");
  const [filters, setFilters] = React.useState({
    search: "",
    page: 1,
    totalPages: 4,
  });

  React.useEffect(() => {
    async function call() {
      await dataCall();
    }
    call();
  }, [filters.search]);

  const dataCall = async (params?: any) => {
    setLoading(true);
    try {
      const pairResponse = await getAllPairFileFact({
        page_size: 10,
        search: filters.search.length > 0 ? filters.search : undefined,
        ordering: params?.pairOrdering || ordering,
      });
      const userResponse = await getAllUserFilesFact({
        page_size: 10,
        search: filters.search.length > 0 ? filters.search : undefined,
        ordering: params?.empOrdering || ordering,
      });
      const associateResponse = await getAllAssociateFileFact({
        page_size: 10,
        search: filters.search.length > 0 ? filters.search : undefined,
        ordering: params?.associateOrdering || ordering,
      });
      setUserData(userResponse.data);
      setPairData(pairResponse.data);
      setAssociateData(associateResponse.data);
      setLoading(false);
    } catch (err) {
      setLoading(false);
    }
  };

  const onClose = async () => {
    await dataCall();
  };
  // const [pairData] = React.useState([
  //   {
  //     key: "row1",
  //     id: 1,
  //     title: "Parings All emp",
  //     date: "01-29-2021",
  //     record: 90,
  //     tag: "Test Tag",
  //   },
  // ]);
  const [ModalType, setModalType] = React.useState("");

  //Modal Closing Logic
  const setModalClose = () => {
    setModalType("");
    setShow(false);
    setScreen(1);
  };

  //For Providing Views
  const ViewProvider = () => {
    switch (ModalType) {
      case "addEmployee":
        return (
          <AddEmployee
            screen={Screen}
            onDrop={handleFileDrop}
            setScreen={setScreen}
            setShow={setShow}
          />
        );
      case "addPair":
        return (
          <ConfigurePairingData
            screen={Screen}
            onDrop={handleFileDrop}
            setScreen={setScreen}
            setShow={setShow}
          />
        );

      default:
        return <> </>;
    }
  };
  const getFilterLink = (key: string, title: string) => {
    return title;
  };

  // const history = useHistory();

  const onFilterChange = (filter: string) => {
    setParams((_params: any) => ({
      ..._params,
      filter,
    }));
  };

  const [params, setParams] = useState({
    search: "",
    filter: query.get("view") || SURVEY_TYPES.EMPLOYEE,
    sort: [],
    size: 0,
    page: 1,
    totalElements: 20,
    totalPages: 4,
  });

  const getTabsTemplate = () => {
    return (
      <Div className="custom-tabs-1 mb-4">
        <Tabs
          activeKey={params.filter}
          onSelect={(k: any) => onFilterChange(k)}
        >
          <Tab
            eventKey={SURVEY_TYPES.EMPLOYEE}
            title={getFilterLink(SURVEY_TYPES.EMPLOYEE, "Employee")}
          >
            <EmployeeDataList
              filters={filters}
              data={userData}
              isLoading={isLoading}
              setUserData={setUserData}
              dataCall={dataCall}
              ordering={ordering}
              setOrdering={setOrdering}
            />
          </Tab>
          <Tab
            eventKey={SURVEY_TYPES.PAIRING}
            title={getFilterLink(SURVEY_TYPES.PAIRING, "Pairings")}
          >
            <PairingDataList
              filters={filters}
              data={pairData}
              isLoading={isLoading}
              setPairData={setPairData}
              dataCall={dataCall}
              ordering={ordering}
              setOrdering={setOrdering}
            />
          </Tab>
          <Tab
            eventKey={SURVEY_TYPES.ONGOING}
            title={getFilterLink(SURVEY_TYPES.ONGOING, "Ongoing / Associated")}
          >
            <OngoingDataList
              filters={filters}
              data={associateData}
              setAssociateData={setAssociateData}
              isLoading={isLoading}
              dataCall={dataCall}
              ordering={ordering}
              setOrdering={setOrdering}
            />
          </Tab>
        </Tabs>
      </Div>
    );
  };

  const onSearch = (search: string) => {
    setFilters((_filters) => ({
      ..._filters,
      search,
    }));
  };

  return (
    <Div className="bg-white pt-3 px-3 pb-5 DataLoad-List">
      <CustomHeader
        title={<Text.H1 className="pb-2">Employees / Pairings</Text.H1>}
        metaItem={
          <>
            <FormControl.Search
              value={tempSearch}
              onChange={(e: any) => {
                setTempSearch(e.target.value);
              }}
              onSearch={(value: string) => {
                onSearch(value);
              }}
              placeholder="Search for title, tag"
            />
            {/* <RoundButton
              type="button"
              className="ml-4 flex align-items-center justify-content-center"
              variant="primary"
              size="lg"
              onClick={() => setShow(true)}
            >
              +
            </RoundButton> */}
            <Button
              className="mx-3 font-weight-normal"
              onClick={() => {
                setShowAddPair(true);
              }}
            >
              Add Pairings
            </Button>
            <Button
              className="mr-3 font-weight-normal"
              onClick={() => {
                setShowAddEmployee(true);
              }}
            >
              Add Employees
            </Button>
          </>
        }
        // breadcrumbs={[
        //   {
        //     label: "Dataload",
        //     icon: <Group className="grey-icon__svg" />,
        //     route: appUrls.admin.dataLoad.default,
        //   },
        //   { label: "Employees / Pairings" },
        // ]}
      />
      <Layout.Container fluid className="">
        <Div>{getTabsTemplate()}</Div>

        <Modal
          show={show}
          size="lg"
          aria-labelledby="contained-modal-title-vcenter"
          centered
          onHide={setModalClose}
        >
          {ModalType === "" ? (
            <SelectDataLoad setModalType={setModalType} />
          ) : (
            <ViewProvider />
          )}
        </Modal>
        <ConfigureEmployeeData
          show={showAddEmployee}
          size="lg"
          onHide={() => {
            setShowAddEmployee(false);
          }}
          aria-labelledby="contained-modal-title-vcenter"
          centered
          dialogClassName="modal-90w"
          backdrop="static"
          onSuccessClose={onClose}
        />
        <ConfigurePairingData
          size="lg"
          onHide={() => {
            setShowAddPair(false);
          }}
          aria-labelledby="contained-modal-title-vcenter"
          centered
          dialogClassName="modal-90w"
          backdrop="static"
          show={showAddPair}
          onSuccessClose={onClose}
        />
      </Layout.Container>
    </Div>
  );
};
export const Empty = () => {
  const { PAIRICON } = data;
  return (
    <Div
      className="d-flex flex-column align-items-center"
      style={{ height: "calc(100vh - 120px)", padding: "150px 0" }}
    >
      <img src={PAIRICON} alt="" />
      <Text.P1 className="pb-4 text-center" style={{ margin: 30 }}>
        Add users to be able to set up surveys for your employees. <br /> You
        can add users in bulk by uploading files in excel .xls, .xlsx, or .csv
        file formats.
      </Text.P1>
    </Div>
  );
};
export default CreateDataLoad;
