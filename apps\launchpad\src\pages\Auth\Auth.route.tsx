import AuthLayout from "./auth.layout";
import Login from "./Login/Login";
import { ROUTES } from "../../routes";
import ResetPassword from "./ResetPassword/ResetPassword";
import RequestPassword from "./RequestPassword/RequestPassword";
import Activation from "./Activation/Activation";
import MagicLink from "./MagicLink/MagicLink";

const AuthRoute = [
  {
    path: ROUTES.AUTH.ROOT,
    layout: () => <AuthLayout />,
    children: [
      {
        name: "Login",
        path: ROUTES.AUTH.LOGIN,
        component: () => <Login />,
      },
      {
        name: "Reset Password",
        path: ROUTES.AUTH.RESET_PASSWORD,
        isExact: false,
        component: ResetPassword,
      },
      {
        name: "Request password change",
        path: ROUTES.AUTH.REQUEST_PASSWORD,
        isExact: false,
        component: RequestPassword,
      },
      {
        name: "Activation",
        path: ROUTES.AUTH.ACTIVATION,
        isExact: false,
        component: Activation,
      },
      {
        name: "Magic Link",
        path: ROUTES.AUTH.MAGIC_LINK,
        isExact: false,
        component: MagicLink,
      },
    ],
  },
];

export default AuthRoute;
