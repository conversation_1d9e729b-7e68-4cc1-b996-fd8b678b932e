import React from "react";
import { useParams } from "unmatched/hooks";
import util from "unmatched/utils";
import R360SurveyReport from "./R360SurveyReport/R360SurveyReport";
import { get360ReportDetail } from "../reports-api";

export default function ReportsContainer() {
  const params = useParams<any>();
  const [survey, setSurveyInfo] = React.useState<any>({
    id: params.id,
    name: "",
    type: "",
    startDate: new Date().toDateString(),
    endDate: new Date().toDateString(),
  });

  React.useEffect(() => {
    async function fetchSurveyInfo() {
      const response: any = await get360ReportDetail(params.id);
      const { getBrowserTime } = util.date;
      // const startDate = response.startDate?.toISOString();
      setSurveyInfo((_d: any) => {
        return {
          ..._d,
          name: response.name,
          type: response.type,
          startDate: getBrowserTime(response.startDate, "dd MMMM yyyy"),
          endDate: getBrowserTime(response.endDate, "dd MMMM yyyy"),
        };
      });
      // console.log(survey);
    }
    fetchSurveyInfo();

    //eslint-disable-next-line
  }, []);

  return <R360SurveyReport survey={survey} />;
}
