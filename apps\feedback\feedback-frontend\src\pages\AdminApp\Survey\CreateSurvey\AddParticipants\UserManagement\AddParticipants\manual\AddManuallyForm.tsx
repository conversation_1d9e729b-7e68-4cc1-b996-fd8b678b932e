import React, { useState } from "react";
import styled from "styled-components";
import {
  CustomModal as Modal,
  Text,
  Button,
  Layout,
  FormGroup,
  MultiSelect,
  Div,
} from "unmatched/components";
import icons from "assets/icons/icons";
import { components } from "react-select";

export const TranparentButton = styled.button`
  border: none;
  font-size: 12px;
  background: none;

  &:focus {
    background-color: none !important;
    outline: none;
  }
  &:active {
    border: none;
    outline: none;
  }
`;

export const FormContainer = styled(Div)`
  background: #fbfbfb;
  padding: 16px 24px;
  border: 1px solid #f2f2f2;
  box-sizing: border-box;
  border-radius: 5px;
`;

export const ModalFooter = styled(Modal.Footer)`
  border: none;
  padding: 2rem;
  display: flex;
  justify-content: space-between;
`;

export default function Manualpairing(props: any) {
  const {
    setParticipantsData,
    participants,
    deletePairSelect,
    errors,
    onInputChange,
    userOptions,
    // hasError,
    // updateErrors,
    onSelect,
    addParticipants,
    initParticipantData,
    pairErrors,
    setPairErrors,
    setScreen,
  } = props;

  const { Add, CrossButton } = icons;
  const [formDirty, setFormDirty] = useState(false);

  const CustomOption = (props: any) => {
    return (
      <div className="px-2 py-1">
        <components.Option {...props} className="rounded pt-1 pb-2">
          <Div>
            {props.data.first_name} {props.data.last_name}
          </Div>
          <Div>{props.data.email}</Div>
          <Div>ID: {props.data.emp_id}</Div>
        </components.Option>
        <hr className="my-0" />
      </div>
    );
  };

  const optionStyles = {
    option: (styles: any, state: any) => {
      return {
        ...styles,
        backgroundColor: state.isSelected ? "#518cff" : "#fff",
        color: state.isSelected ? "#FFF" : "#000",
        cursor: state.isDisabled ? "not-allowed" : "default",
      };
    },
  };

  const inputProps = {
    closeMenuOnSelect: true,
    isCustom: true,
    onInputChange,
    isMulti: false,
    placeholder: "",
    options: userOptions,
    CustomOption,
    styles: optionStyles,
    postfix: () => null,
    customValContainer: true,
  };

  return (
    <>
      <Modal.Body>
        <Text.P1 className="mb-3">Add more pairings </Text.P1>
        <Text.P2 className="mb-3">
          You can add more pairings in bulk by uploading a csv or excel file or
          you can also add pairings one by one manually. <br />
          <b>
            Note : All exisiting rules will once again be validated for new
            pairings.
          </b>
        </Text.P2>
        <FormContainer>
          {participants.map((pD: any, index: number) => (
            <Layout.Row style={{ marginBottom: 20 }} key={pD.id}>
              <Layout.Col className="col-10">
                <Text.H3 className="pb-2">Rater Email/ID</Text.H3>
                <MultiSelect
                  {...inputProps}
                  onSelect={onSelect(index, "rater")}
                  hasError={
                    (formDirty && errors[index]?.rater) || pairErrors[index]
                  }
                />
                <FormGroup.InValidFeedback>
                  {formDirty && errors[index]?.rater}
                  {pairErrors[index]}
                </FormGroup.InValidFeedback>
              </Layout.Col>

              <Layout.Col className="col-2">
                {participants.length === index + 1 && (
                  <TranparentButton
                    onClick={() =>
                      setParticipantsData((data: any) => [
                        ...data,
                        {
                          id: Math.random().toString().split(".")[1],
                          rater: "",
                        },
                      ])
                    }
                    className={"mt-4"}
                  >
                    <Add width="18px" height="18px" />
                  </TranparentButton>
                )}
                {participants.length > 1 && (
                  <TranparentButton
                    className={"mt-4"}
                    onClick={() => deletePairSelect(index)}
                  >
                    <CrossButton width="18px" height="18px" />
                  </TranparentButton>
                )}
              </Layout.Col>
            </Layout.Row>
          ))}
        </FormContainer>
      </Modal.Body>
      <ModalFooter>
        <Button
          variant="outline-primary"
          onClick={() => {
            setParticipantsData(initParticipantData);
            setPairErrors([]);
            setScreen(1);
          }}
        >
          Back
        </Button>
        <Button
          variant="primary"
          onClick={() => {
            setFormDirty(true);
            // updateErrors();
            // if (!hasError(errors))
            addParticipants(participants);
          }}
        >
          Add Particpants and Send Invite
        </Button>
      </ModalFooter>
    </>
  );
}
