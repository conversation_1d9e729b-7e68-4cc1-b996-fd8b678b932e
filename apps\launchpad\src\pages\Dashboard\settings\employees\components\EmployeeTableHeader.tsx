import React, { useMemo } from "react";
import { <PERSON>Head, TableHeader, TableRow } from "@repo/ui/components/table";
import { Employee } from "../useEmployees";

interface EmployeeTableHeaderProps {
  handleSort: (field: keyof Employee | string) => void;
  getSortIcon: (field: keyof Employee | string) => React.ReactNode;
  employees: Employee[];
}

export const EmployeeTableHeader: React.FC<EmployeeTableHeaderProps> = ({
  handleSort,
  getSortIcon,
  employees,
}) => {
  // Extract unique metadata keys from all employees
  const metadataKeys = useMemo(() => {
    const keys = new Set<string>();

    employees.forEach((employee) => {
      if (employee.metadata && typeof employee.metadata === 'object') {
        Object.keys(employee.metadata).forEach((key) => keys.add(key));
      }
    });

    // Convert to array and sort alphabetically for consistent display
    return Array.from(keys).sort();
  }, [employees]);

  // Format metadata key for display (convert snake_case or camelCase to Title Case)
  const formatMetadataKey = (key: string): string => {
    return key
      .replace(/_/g, ' ')
      .replace(/([A-Z])/g, ' $1')
      .replace(/^./, (str) => str.toUpperCase())
      .trim();
  };

  return (
    <TableHeader>
      <TableRow>
        <TableHead
          className="cursor-pointer"
          onClick={() => handleSort("firstName")}
        >
          <div className="flex items-center">
            First Name
            {getSortIcon("firstName")}
          </div>
        </TableHead>
        <TableHead
          className="cursor-pointer"
          onClick={() => handleSort("lastName")}
        >
          <div className="flex items-center">
            Last Name
            {getSortIcon("lastName")}
          </div>
        </TableHead>
        <TableHead
          className="cursor-pointer"
          onClick={() => handleSort("email")}
        >
          <div className="flex items-center">
            Email
            {getSortIcon("email")}
          </div>
        </TableHead>
        <TableHead
          className="cursor-pointer"
          onClick={() => handleSort("empId")}
        >
          <div className="flex items-center">
            Employee ID
            {getSortIcon("empId")}
          </div>
        </TableHead>
        <TableHead
          className="cursor-pointer"
          onClick={() => handleSort("departed")}
        >
          <div className="flex items-center">
            In Firm
            {getSortIcon("departed")}
          </div>
        </TableHead>

        {/* Dynamic metadata columns */}
        {metadataKeys.map((key) => (
          <TableHead
            key={key}
            className="cursor-pointer"
            onClick={() => handleSort(`metadata.${key}`)}
          >
            <div className="flex items-center">
              {formatMetadataKey(key)}
              {getSortIcon(`metadata.${key}`)}
            </div>
          </TableHead>
        ))}
      </TableRow>
    </TableHeader>
  );
};
