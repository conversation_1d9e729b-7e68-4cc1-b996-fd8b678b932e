// import Header from "pages/UserApp/Header/Header";
// import CommonContainer from "../CommonContainer/CommonContainer";
// import React from "react";
// import util from "unmatched/utils";
import Confidentiality from "./Confidentiality";

// import PropTypes from 'prop-types'

const ConfidentialityContainer = () => {
  return (
    <div>
      {/* <Header hideOptions /> */}
      {/* <CommonContainer> */}
        <Confidentiality />
      {/* </CommonContainer> */}
    </div>
  );
};

// Faq.propTypes = {

// }

export default ConfidentialityContainer;
