// import { isFieldInvalid, getFieldErrorMessage } from "../../utils/formik";
// import FormControl from "../Form/FormControl/FormControl";
// import FormGroup from "../Form/FormGroup/FormGroup";

import { FormControl, FormGroup } from "@unmatchedoffl/ui-core";
import { isFieldInvalid, getFieldErrorMessage } from "./utilsFormik";

interface Props {
  label?: string;
  keyName: string;
  placeholder?: string;
  formik: any;
  className?: string;
  required?: boolean;
}

export default function FormikTextInput(props: Props) {
  const { label, keyName, placeholder, formik, className, required } = props;

  return (
    <FormGroup className={className}>
      <FormGroup.Label>{label}{required && <span className="required-input">*</span>}</FormGroup.Label>
      <FormControl.Text
        name={keyName}
        isInvalid={isFieldInvalid(formik, keyName)}
        value={formik.values[keyName]}
        onBlur={formik.handleBlur}
        onChange={formik.handleChange}
        placeholder={placeholder}
      ></FormControl.Text>
      <FormGroup.InValidFeedback
        text={getFieldErrorMessage(formik, keyName)}
      ></FormGroup.InValidFeedback>
    </FormGroup>
  );
}
