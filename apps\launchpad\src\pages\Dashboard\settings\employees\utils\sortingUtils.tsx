import React from "react";
import { ArrowUp, ArrowDown } from "lucide-react";
import { Employee, SortDirection } from "@/pages/settings/employees/useEmployees";

/**
 * Mapping between frontend camelCase field names and backend snake_case field names
 */
export const fieldNameMapping: Record<keyof Employee, string> = {
  key: "id",
  email: "email",
  empId: "emp_id",
  firstName: "first_name",
  id: "id",
  lastName: "last_name",
  metadata: "metadata",
  departed: "is_terminated"
};

/**
 * Convert a frontend field name to the corresponding backend field name
 * Also handles metadata fields in the format "metadata.field_name"
 */
export const getBackendFieldName = (field: string): string => {
  // Check if this is a metadata field
  if (field.startsWith('metadata.')) {
    const metadataField = field.split('.')[1];
    return `metadata__${metadataField}`;
  }

  // Handle regular fields
  return fieldNameMapping[field as keyof Employee] || String(field);
};

/**
 * Get the appropriate sort icon based on the current sorting state
 */
export const getSortIcon = (
  field: string,
  sortingField: string,
  sortingDirection: SortDirection
): React.ReactNode => {
  if (sortingField !== field) return null;
  return sortingDirection === "asc"
    ? <ArrowUp className="h-4 w-4 ml-1" />
    : <ArrowDown className="h-4 w-4 ml-1" />;
};

/**
 * Handle column sorting logic
 */
export const getNextSortDirection = (
  field: string,
  currentField: string,
  currentDirection: SortDirection
): SortDirection => {
  return currentField === field && currentDirection === "asc" ? "desc" : "asc";
};
