import { surveyCreateCore } from "@unmatchedoffl/ui-core";
// import CloneSurvey from "./CloneSurvey";
// import CreateVersion from "./CreateVersion";
// import SurveyVersions from "./SurveyVersions";
// import ValidateSurvey from "./ValidateSurvey";

// import * as properties from "./properties";

export const surveyProperties = surveyCreateCore.surveyProperties;

export default {
  ...surveyCreateCore,
  // CloneSurvey: CloneSurvey,
  // SurveyVersions: SurveyVersions,
  // ValidateSurvey: ValidateSurvey,
  // CreateVersion: CreateVersion,
};
