import React, { useState, useEffect } from "react";
import { Div, CustomModal as Modal } from "unmatched/components";
import RequiredFields from "./RequiredFields";
import UploadFile from "./UploadFile";
import { getRequiredPairFieldFact } from "../pairings-api";
import ConfirmRules from "./ConfirmRules";
import SuccessModal from "./SuccessModal";
import { AddPairingManually } from "./manual/AddPairingManually";
// import ConfirmExplicitEmailInvite from "./ConfirmExplicitEmailInvite";

export default function AddPairings(props: any) {
  // let screen: number = props.screen;
  const [isLoading, setIsLoading] = useState(true);
  const [requiredFields, setRequiredFields] = useState([]);
  const [apiSuccess, setApiSuccess] = useState(false);
  useEffect(() => {
    async function getRequiredFields() {
      const response = await getRequiredPairFieldFact();
      const data = await response.data.fields;
      setRequiredFields(data);
      setIsLoading(false);
    }
    getRequiredFields();
  }, []);

  const [screen, setScreen] = useState<number>(1);
  const ScreenView = (inPosition: number, props: any) => {
    switch (inPosition) {
      case 1:
        return (
          <RequiredFields
            {...props}
            setScreen={setScreen}
            loading={isLoading}
            data={requiredFields}
          />
        );
      case 2:
        return (
          <UploadFile
            {...props}
            setApiSuccess={setApiSuccess}
            setScreen={setScreen}
          />
        );
      case 3:
        return (
          <SuccessModal
            {...props}
            apiSuccess={apiSuccess}
            setApiSuccess={setApiSuccess}
            setScreen={setScreen}
          />
        );
      case 4:
        return <ConfirmRules {...props} setScreen={setScreen} />;
      case 5:
        return (
          <AddPairingManually
            {...props}
            surveyIndex={props.survey?.data?.id}
            setScreen={setScreen}
            // getPairings={getPairings}
          />
        );
      // case 6:
      //   return (
      //     <ConfirmExplicitEmailInvite
      //       {...props}
      //       surveyIndex={props.survey?.data?.id}
      //       setScreen={setScreen}
      //     />
      //   );
      default:
        return "";
    }
  };
  return (
    <Modal
      {...props}
      onHide={() => {
        setScreen(1);
        props.onHide();
        setApiSuccess(false);
      }}
      children={
        <>
          <Modal.Header closeButton>
            <Modal.Title>Add Pairings</Modal.Title>
          </Modal.Header>
          <Div>{ScreenView(screen, props)}</Div>
        </>
      }
    />
  );
}
