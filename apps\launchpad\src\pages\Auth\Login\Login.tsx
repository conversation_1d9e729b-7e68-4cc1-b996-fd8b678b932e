/* eslint-disable react-refresh/only-export-components */
// Node modules
import React, { useState } from "react";
// import appUrls from "unmatched/utils/urls/app-urls";
//useHistory,
import { useLocation, useXHR } from "../../../unmatched/hooks";
import {
  getUserFact,
  loginUserFact,
  validateUserEmailFact,
} from "../../../unmatched/modules/session/api";
// import { FormGroup } from "unmatched/components";
// import useToastr from "unmatched/modules/toastr/hook";
import useSession from "../../../unmatched/modules/session/hook";
// Components
import Loader from "../Loader";
// import AuthContainer from "../AuthContainer";
// import { loginUserAction } from "../../Session/session-actions";
import { reqActivationFact, requestMagicLinkFact } from "../auth.api";
import LoginForm from "./Form";
import { toast } from "sonner";
import { Button } from "@repo/ui/components/button";
import { Text } from "@repo/ui/components/Text";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@repo/ui/components/dialog";
import AuthContainer from "../AuthContainer";
// import {
//   CustomModal as Modal,
//   util,
//   Text,
//   Button,
// } from "@unmatchedoffl/ui-core";

export enum ViewStates {
  DRAFT = "DRAFT",
  ACTIVE = "ACTIVE",
  IN_ACTIVE = "IN_ACTIVE",
  NOT_FOUND = "NOT_FOUND",
  SENT_ACTIVATION = "SENT_ACTIVATION",
}
/* eslint-disable no-unused-vars */

const META_DATA = {
  title: "Welcome",
  content: "Log in to your account to continue",
  loadingTitle: "Signing in...",
  loadingContent: "Please wait while we setup your account",
  info: (
    <>
      This email id is not activated
      <br />
      Check your email to activate or click below to resend the activation email
    </>
  ),
};
function useQuery() {
  const { search } = useLocation();
  return React.useMemo(() => new URLSearchParams(search), [search]);
}

const LoginComponent = () => {
  // const history = useHistory();
  const session = useSession();
  // const toastr = useToastr();
  // const toast = useToast();
  const validation = useXHR();
  const activation = useXHR();
  const login = useXHR();
  const magicLink = useXHR();
  const user = useXHR();

  const [showInactive, setShowInActive] = useState(false);
  const query = useQuery();

  const [meta, setMeta] = useState(META_DATA);
  const [formData, setFormData] = useState({
    email: "",
    password: "",
  });
  const [sentMagicLink, setSentMagicLink] = useState(false);
  const [current, setCurrent] = useState<ViewStates>(ViewStates.DRAFT);
  const [isAdmin, setIsAdmin] = useState<boolean>(false);
  const [hasMagicLink, setMagicLink] = useState<boolean>(false);
  const [isPasswordSet, setIsPasswordSet] = useState<boolean>(true);
  const [email, setEmail] = useState(null);

  const checkLoading = login.isLoading || user.isLoading || magicLink.isLoading;

  const onLogin = async (data: any) => {
    setFormData(data);
    login.setLoading(true);
    try {
      const response = await loginUserFact(data, null, login.getMetaConfig());
      login.onSuccess(response);
      getUserData(response.data);
    } catch (err: any) {
      console.log(err);
      login.onError(err);
      toast.error("Login Error.", {
        description: "Invalid password. Please try again.",
      });

      // toastr.showToast({
      //   title: "Error",
      //   content: "Invalid password. Please try again.",
      // });
    }
  };

  const requestMagicLink = async (data: any) => {
    magicLink.setLoading(true);
    try {
      setMeta({
        ...meta,
        loadingTitle: "Requesting magic link...",
        loadingContent: "Please wait while we send magic link email.",
      });
      const response = await requestMagicLinkFact(data);
      magicLink.onSuccess(response);
      setSentMagicLink(true);
      setMeta({
        ...meta,
        loadingTitle: "Check your mailbox.",
        loadingContent:
          "You would have received an email with a magic link. Click the link to login to your account.",
      });
    } catch (err: any) {
      magicLink.setLoading(false);
      toast.error("Login Error.", {
        description: "Something went wrong!",
      });
      // toastr.errorToast("Something went wrong!");
      magicLink.onError(err);
    }
  };

  const onLoginSuccess = (response: any, { token, expiry }: any) => {
    // const { firstName, lastName } = response;
    // if (!firstName || !lastName) {
    //   history.push(
    //     `${appUrls.auth.accountInformation}?token=${token}&&expiry=${expiry}`
    //   );
    // } else {

    // }
    session.login({
      token,
      expiry,
      user: response,
      redirectUrl: query.get("redirect"),
      app: query.get("app_root"),
      // ...(session.client?.homepage === "FEEDBACK" &&

        //TODO OOOOO
        // response.role === util.enums.Roles.USER && {
        //   redirectToFeedback: true,
        //   page: session.client?.homepagePath,
        // }),
    });
  };

  const getUserData = async (loginResponse: any) => {
    user.setLoading(true);
    try {
      const { token } = loginResponse;
      const response = await getUserFact({ token }, user.getMetaConfig());
      user.onSuccess(response);
      onLoginSuccess(response, loginResponse);
    } catch (err: any) {
      user.onError(err);
      // toastr.onError(err);
      toast.error("Login Error.", {
        description: err?.message || "Something went wrong!",
      });
    }
  };

  const validateEmail = async (email: string, success: Function) => {
    console.log(email);
    setFormData((_state: any) => ({
      ..._state,
      email,
    }));

    validation.setLoading(true);
    try {
      const response = await validateUserEmailFact(
        { email },
        validation.getMetaConfig()
      );
      validation.onSuccess(response);
      if (success)
        success(
          response.isActive,
          response.isAdmin,
          response.isPasswordSet,
          response.hasMagicLink
        );
      validation.setLoading(false);
    } catch (err: any) {
      validation.onError(err);
      if (err.statusCode === 404) {
        setCurrent(ViewStates.NOT_FOUND);
      } else {
        // toastr.onError(err);
        console.log(err);
        toast.error("Login Error.", {
          description: err?.message || "Something went wrong!",
        });
      }
    }
  };

  const onValidateEmail = ({ email }: any) => {
    validateEmail(
      email,
      (
        isActive: boolean,
        isAdmin: boolean,
        isPasswordSet: boolean,
        _hasMagicLink: boolean
      ) => {
        if (!isActive) {
          setEmail(email);
        }
        setCurrent(isActive ? ViewStates.ACTIVE : ViewStates.IN_ACTIVE);
        setShowInActive(isActive ? false : true);
        setIsAdmin(isAdmin);
        setIsPasswordSet(isPasswordSet);
        setMagicLink(_hasMagicLink);
      }
    );
  };

  const onResendActivation = async (data: any) => {
    activation.setLoading(true);
    try {
      const response = await reqActivationFact(
        data,
        null,
        activation.getMetaConfig()
      );
      activation.onSuccess(response);
      setCurrent(ViewStates.SENT_ACTIVATION);
      setEmail(null);
      setShowInActive(false);
      setMeta({
        ...meta,
        loadingTitle: "Check your mailbox.",
        loadingContent:
          "Please check your email and click the activation button or link to activate your account.",
      });
    } catch (err: any) {
      activation.setLoading(false);
      // toastr.errorToast("Something went wrong!");
      toast.error("Login Error.", {
        description: err?.message || 'Something went wrong!',
      });
      activation.onError(err);
    }
  };

  const getLoginTemplate = () => {
    if (checkLoading) {
      return (
        <Loader
          title={meta.loadingTitle}
          content={meta.loadingContent}
        ></Loader>
      );
    } else {
      if (current === ViewStates.NOT_FOUND) {
        const Feedback = (
          // <FormGroup.InValidFeedback>
          <>
            This email id isn't recognized. Please check the email id or contact
            your firm for access.
          </>
          // </FormGroup.InValidFeedback>
        );
        return (
          <LoginForm
            meta={meta}
            payload={formData}
            Feedback={Feedback}
            loading={validation.isLoading}
            updateLoginState={() => setCurrent(ViewStates.DRAFT)}
            onSubmit={onValidateEmail}
            loginState={current}
            submit={{ variant: "primary", label: "Next" }}
            hasMagicLink={hasMagicLink}
            hasPassword={false}
          />
        );
      } else if (current === ViewStates.IN_ACTIVE) {
        const Feedback = (
          <>
            {/* <FormGroup.InValidFeedback>
              This email id is not activated
            </FormGroup.InValidFeedback>
            <FormGroup.Description className="pt-1">
              Check your email to activate or click below to resend the
              activation email
            </FormGroup.Description> */}
          </>
        );
        return (
          <LoginForm
            meta={meta}
            payload={formData}
            Feedback={Feedback}
            loading={activation.isLoading}
            onSubmit={onResendActivation}
            updateLoginState={() => setCurrent(ViewStates.DRAFT)}
            submit={{ variant: "outline-primary", label: "Resend Activation" }}
            hasMagicLink={hasMagicLink}
            hasPassword={false}
            loginState={current}
            requestMagicLink={requestMagicLink}
            sentMagicLink={sentMagicLink}
            isAdmin={isAdmin}
            showSubmit={isAdmin || !hasMagicLink}
            isPasswordSet={isPasswordSet}
          />
        );
      } else if (current === ViewStates.ACTIVE) {
        return (
          <LoginForm
            meta={meta}
            payload={formData}
            onSubmit={onLogin}
            clearAjaxError={login.resetError}
            incorrectPassword={
              login.error.statusCode === 400 &&
              login.error.msg === "WRONG_PASSWORD"
                ? "Invalid password. Please try again."
                : ""
            }
            submit={{ variant: "primary", label: "Login" }}
            hasMagicLink={hasMagicLink}
            loginState={current}
            hasPassword
            requestMagicLink={requestMagicLink}
            {...(sentMagicLink && { sentMagicLink })}
            isAdmin={isAdmin}
          />
        );
      } else if (current === ViewStates.DRAFT) {
        return (
          <LoginForm
            meta={meta}
            payload={formData}
            loading={validation.isLoading}
            onSubmit={onValidateEmail}
            loginState={current}
            submit={{ variant: "primary", label: "Next" }}
            hasMagicLink={hasMagicLink}
            hasPassword={false}
          />
        );
      } else if (current === ViewStates.SENT_ACTIVATION) {
        return (
          <LoginForm
            meta={meta}
            payload={formData}
            loading={validation.isLoading}
            onSubmit={onValidateEmail}
            submit={{ variant: "primary", label: "Next" }}
            hasMagicLink={hasMagicLink}
            loginState={current}
            hasPassword={false}
            sentActivation={true}
          />
        );
      }
    }
  };
  return (
    <AuthContainer className="max-w-lg mx-auto w-full min-h-[40vh] flex flex-col justify-center ">
      <title>Login - Unmatched</title>
      <Dialog open={showInactive}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Inactive User</DialogTitle>
            <DialogDescription>
              {/* This email id is not activated */}
              <Text.P1 className="text-danger">
                This email id is not activated
              </Text.P1>
              <Text.P2 className="text-muted">
                Check your email to activate or click below to resend the
                activation email
              </Text.P2>
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button onClick={() => onResendActivation({ email })}>
              Resend Activation
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      {/* <Modal show={showInactive} centered>
        <Modal.Header>
          <Text.P1 className="text-left">Inactive User</Text.P1>
        </Modal.Header>
        <Modal.Body size="sm">
          <Text.P1 className="text-danger">
            This email id is not activated
          </Text.P1>
          <Text.P2 className="text-muted">
            Check your email to activate or click below to resend the activation
            email
          </Text.P2>
        </Modal.Body>
        <Modal.Footer>
          <Button onClick={() => onResendActivation({ email })}>
            Resend Activation
          </Button>
        </Modal.Footer>
      </Modal> */}
      
      {getLoginTemplate()}
    </AuthContainer>
  );
};

export default LoginComponent;
