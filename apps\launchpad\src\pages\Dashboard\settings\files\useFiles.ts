import { useEffect, useState } from "react";
import { getFilesService, deleteFileService, File, FileListParams } from "./filesService";

export type SortDirection = "asc" | "desc";

export const useFiles = () => {
  const [loadingFiles, setLoadingFiles] = useState(false);
  const [files, setFiles] = useState<File[]>([]);
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
    pageCount: 0,
    totalItems: 0,
  });
  const [sorting, setSorting] = useState<{ field: string; direction: SortDirection }>({
    field: "uploadedOn",
    direction: "desc",
  });
  const [searchQuery, setSearchQuery] = useState("");

  useEffect(() => {
    // Prepare filter parameters
    const params: FileListParams = {
      page: pagination.pageIndex + 1,
      page_size: pagination.pageSize,
      search: searchQuery || undefined,
    };

    // Add ordering if present
    if (sorting.field) {
      const backendFieldMap: Record<string, string> = {
        title: "title",
        successRecords: "success_upload_count",
        rejectedRecords: "rejected_records",
        totalRecords: "total_file_records",
        uploadedOn: "created_at",
      };
      
      const backendField = backendFieldMap[sorting.field] || sorting.field;
      params.ordering = `${sorting.direction === "desc" ? "-" : ""}${backendField}`;
    }

    getFiles(params);
  }, [
    pagination.pageIndex,
    pagination.pageSize,
    searchQuery,
    sorting,
  ]);

  const getFiles = async (params?: FileListParams) => {
    try {
      setLoadingFiles(true);
      const response = await getFilesService(params || {});
      setLoadingFiles(false);
      setFiles(response.results);
      setPagination(prev => ({
        ...prev,
        pageCount: response.totalPages,
        totalItems: response.totalElements
      }));
      return response;
    } catch (err) {
      console.error("Error fetching files:", err);
      setLoadingFiles(false);
    }
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setPagination(prev => ({
      ...prev,
      pageIndex: 0 // Reset to first page on new search
    }));
  };

  const handleSort = (field: keyof File | string) => {
    setSorting(prev => {
      if (prev.field === field) {
        // Toggle direction if same field
        return {
          ...prev,
          direction: prev.direction === "asc" ? "desc" : "asc",
        };
      }
      // New field, default to ascending
      return {
        field,
        direction: "asc",
      };
    });
  };

  const handlePaginationChange = (pageIndex: number, pageSize: number) => {
    setPagination(prev => ({
      ...prev,
      pageIndex,
      pageSize,
    }));
  };

  const handleDeleteFile = async (id: string) => {
    try {
      await deleteFileService(id);
      // Remove the deleted file from the state
      setFiles(prev => prev.filter(file => file.id !== id));
      // Refresh the list if needed
      getFiles({
        page: pagination.pageIndex + 1,
        page_size: pagination.pageSize,
        search: searchQuery || undefined,
      });
      return true;
    } catch (error) {
      console.error("Error deleting file:", error);
      return false;
    }
  };

  return {
    loadingFiles,
    files,
    pagination,
    sorting,
    searchQuery,
    handleSearch,
    handleSort,
    handlePaginationChange,
    handleDeleteFile,
  };
};
