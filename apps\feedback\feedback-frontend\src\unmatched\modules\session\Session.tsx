// node modules
import React, { ReactNode, useEffect } from "react";
import { ThemeProvider } from "styled-components";
// Helpers
import util from "unmatched/utils";
import { Div, Image } from "unmatched/components";
import useSession from "unmatched/modules/session/hook";

interface SessionProps {
  children: ReactNode;
  appFailTemplate?: Function;
}

const Session = (props: SessionProps) => {
  const { children, appFailTemplate } = props;
  const session = useSession();

  useEffect(() => {
    initSession();
  }, []);

  const initStorageEvent = () => {
    window.addEventListener("storage", ({ key }: any) => {
      if (!key) {
        session.logout();
      }
    });
  };

  const initSession = async () => {
    util.api.addInterceptors();
    initStorageEvent();
    session.fetchSession();
  };

  if (session.isLoading || !session.sessionLoaded) {
    // return <Loader text="Loading Session...." />;
    return (
      <Div
        className="d-flex align-items-stretch justify-content-center"
        style={{ height: "100vh" }}
      >
        {session.client.loader && (
          <Image
            src={session.client.loader}
            style={{
              width: "100px",
              height: "auto",
            }}
          />
        )}
      </Div>
    );
    // Application Loading Component
  } else if (session.error.msg) {
    // Application Failed to load component
    return appFailTemplate
      ? appFailTemplate(session.error)
      : "There is some issue with the server";
  }
  return <ThemeProvider theme={session.theme}>{children}</ThemeProvider>;
};

export default Session;
