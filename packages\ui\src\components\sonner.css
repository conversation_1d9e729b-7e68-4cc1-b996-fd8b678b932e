/* Custom styles for Sonner toast */
[data-sonner-toaster] {
  --offset: 16px;
  --width: 356px;
  --border-radius: 0.5rem;
  --shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

[data-sonner-toast] {
  box-shadow: var(--shadow);
  border-width: 1px;
  border-style: solid;
  border-radius: var(--border-radius);
  overflow: hidden;
  opacity: 1 !important;
}

/* Ensure toast has solid background */
[data-sonner-toast][data-type="normal"] {
  background-color: var(--normal-bg, #fff);
  color: var(--normal-text, #000);
  border-color: var(--normal-border, #e2e8f0);
}

[data-sonner-toast][data-type="success"] {
  background-color: var(--success-bg, #ecfdf5);
  color: var(--success-text, #047857);
  border-color: var(--success-border, #d1fae5);
}

[data-sonner-toast][data-type="error"] {
  background-color: var(--error-bg, #fef2f2);
  color: var(--error-text, #ef4444);
  border-color: var(--error-border, #fee2e2);
}

/* Ensure toast title and description have proper contrast */
[data-sonner-toast] [data-title] {
  font-weight: 600;
  font-size: 0.875rem;
}

[data-sonner-toast] [data-description] {
  font-size: 0.75rem;
  opacity: 0.8;
}

/* Ensure close button has proper contrast */
[data-sonner-toast] [data-close-button] {
  opacity: 0.5;
}

[data-sonner-toast] [data-close-button]:hover {
  opacity: 1;
}
