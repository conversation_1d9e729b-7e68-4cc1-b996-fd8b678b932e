import React from "react";
import { TableHead, TableHeader, TableRow } from "@repo/ui/components/table";
import { Employee } from "../filesService";

interface EmployeeTableHeaderProps {
  handleSort: (field: keyof Employee | string) => void;
  getSortIcon: (field: keyof Employee | string) => React.ReactNode;
}

export const EmployeeTableHeader: React.FC<EmployeeTableHeaderProps> = ({
  handleSort,
  getSortIcon,
}) => {
  return (
    <TableHeader>
      <TableRow>
        <TableHead className="w-[50px]">ID</TableHead>
        <TableHead 
          className="cursor-pointer"
          onClick={() => handleSort("firstName")}
        >
          First Name {getSortIcon("firstName")}
        </TableHead>
        <TableHead 
          className="cursor-pointer"
          onClick={() => handleSort("lastName")}
        >
          Last Name {getSortIcon("lastName")}
        </TableHead>
        <TableHead 
          className="cursor-pointer"
          onClick={() => handleSort("email")}
        >
          Email {getSortIcon("email")}
        </TableHead>
        <TableHead 
          className="cursor-pointer"
          onClick={() => handleSort("empId")}
        >
          Employee ID {getSortIcon("empId")}
        </TableHead>
        <TableHead 
          className="cursor-pointer"
          onClick={() => handleSort("departed")}
        >
          Departed {getSortIcon("departed")}
        </TableHead>
      </TableRow>
    </TableHeader>
  );
};
